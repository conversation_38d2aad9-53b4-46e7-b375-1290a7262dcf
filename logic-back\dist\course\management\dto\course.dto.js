"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseResponseDto = exports.CreateCourseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCourseDto {
    seriesId;
    title;
    description;
    coverImage;
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    contentConfig;
    teachingInfo;
    additionalResources;
    orderIndex;
}
exports.CreateCourseDto = CreateCourseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属系列ID',
        example: 124
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)({ message: '所属系列ID不能为空' }),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "seriesId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程标题',
        example: '第一课：Node.js基础入门'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '课程标题不能为空' }),
    __metadata("design:type", String)
], CreateCourseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '课程简介',
        example: '了解Node.js的基本概念、安装配置和核心模块'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCourseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '课程封面',
        example: 'https://example.com/nodejs-basic-cover.jpg'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCourseDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '是否包含视频：0=否，1=是',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '是否包含文档：0=否，1=是',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '是否包含音频：0=否，1=是',
        example: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '视频时长(秒)',
        example: 3600
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "videoDuration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '详细内容配置',
        example: {
            "hasVideo": 1,
            "hasDocument": 1,
            "hasAudio": 0,
            "video": {
                "url": "https://example.com/videos/nodejs-basics.mp4",
                "name": "Node.js基础入门讲解.mp4"
            },
            "document": {
                "url": "https://example.com/documents/nodejs-basics-slides.pdf",
                "name": "Node.js基础入门课件.pdf"
            }
        }
    }),
    (0, class_validator_1.IsObject)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateCourseDto.prototype, "contentConfig", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '教学相关信息',
        example: [
            {
                "title": "教学目标",
                "content": [
                    "理解Node.js的基本概念和特点",
                    "掌握Node.js的安装和环境配置"
                ]
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCourseDto.prototype, "teachingInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '辅助学习资源',
        example: [
            {
                "title": "Node.js官方文档",
                "url": "https://nodejs.org/docs/",
                "description": "Node.js官方学习资源"
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCourseDto.prototype, "additionalResources", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '在系列中的顺序',
        example: 1,
        default: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseDto.prototype, "orderIndex", void 0);
class CourseResponseDto {
    id;
    seriesId;
    title;
    description;
    coverImage;
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    contentConfig;
    teachingInfo;
    additionalResources;
    orderIndex;
    status;
    creatorId;
    createdAt;
    updatedAt;
}
exports.CourseResponseDto = CourseResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程ID',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属系列ID',
        example: 124
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "seriesId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程标题',
        example: '第一课：Node.js基础入门'
    }),
    __metadata("design:type", String)
], CourseResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程简介',
        nullable: true,
        example: '了解Node.js的基本概念、安装配置和核心模块'
    }),
    __metadata("design:type", String)
], CourseResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '课程封面',
        nullable: true,
        example: 'https://example.com/nodejs-basic-cover.jpg'
    }),
    __metadata("design:type", String)
], CourseResponseDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含视频',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含文档',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '是否包含音频',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '视频时长(秒)',
        example: 3600
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "videoDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '详细内容配置',
        nullable: true,
        example: {
            "hasVideo": 1,
            "hasDocument": 1,
            "hasAudio": 0,
            "video": {
                "url": "https://example.com/videos/nodejs-basics.mp4",
                "name": "Node.js基础入门讲解.mp4"
            },
            "document": {
                "url": "https://example.com/documents/nodejs-basics-slides.pdf",
                "name": "Node.js基础入门课件.pdf"
            }
        }
    }),
    __metadata("design:type", Object)
], CourseResponseDto.prototype, "contentConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '教学相关信息',
        nullable: true,
        example: [
            {
                "title": "教学目标",
                "content": [
                    "理解Node.js的基本概念和特点",
                    "掌握Node.js的安装和环境配置"
                ]
            }
        ]
    }),
    __metadata("design:type", Array)
], CourseResponseDto.prototype, "teachingInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '辅助学习资源',
        nullable: true,
        example: [
            {
                "title": "Node.js官方文档",
                "url": "https://nodejs.org/docs/",
                "description": "Node.js官方学习资源"
            }
        ]
    }),
    __metadata("design:type", Array)
], CourseResponseDto.prototype, "additionalResources", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '在系列中的顺序',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "orderIndex", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态：0=草稿，1=已发布，2=已归档',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建者ID',
        nullable: true,
        example: 123
    }),
    __metadata("design:type", Number)
], CourseResponseDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建时间',
        example: '2023-07-15T09:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '更新时间',
        example: '2023-07-15T09:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=course.dto.js.map