window.addEventListener("error",(function(e){try{if(e.message.includes("ResizeObserver"))return e.stopImmediatePropagation(),!1}catch(e){console.log(e)}}),!0),function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.GUI=r():e.GUI=r()}(self,(()=>(()=>{"use strict";var e,r,n,t,o,i={77828:(e,r,n)=>{n.r(r);var t=n(96540),o=n(40961),i=n(73125),d=n(41580);const c=(0,n(11640).default)((0,d.default)(i.default)),a="10015059";class s extends t.Component{constructor(e){super(e),this.updateProject=this.updateProject.bind(this),this.state={projectId:window.location.hash.substring(1)||a}}componentDidMount(){window.addEventListener("hashchange",this.updateProject),window.location.hash.substring(1)||(window.location.hash=a)}componentWillUnmount(){window.addEventListener("hashchange",this.updateProject)}updateProject(){this.setState({projectId:window.location.hash.substring(1)})}render(){return t.createElement("div",{style:{display:"flex"}},t.createElement(c,{isPlayerOnly:!0,isFullScreen:!1}),t.createElement("iframe",{allowFullScreen:!0,allowTransparency:!0,frameBorder:"0",height:"402",src:"https://scratch.mit.edu/projects/embed/".concat(this.state.projectId,"/?autostart=true"),width:"485"}))}}const u=document.createElement("div");document.body.appendChild(u),o.render(t.createElement(s,null),u)}},d={};function c(e){var r=d[e];if(void 0!==r)return r.exports;var n=d[e]={id:e,loaded:!1,exports:{}},t={id:e,module:n,factory:i[e],require:c};return c.i.forEach((function(e){e(t)})),n=t.module,t.factory.call(n.exports,n,n.exports,t.require),n.loaded=!0,n.exports}c.m=i,c.c=d,c.i=[],c.amdD=function(){throw new Error("define cannot be used indirect")},c.amdO={},e=[],c.O=(r,n,t,o)=>{if(!n){var i=1/0;for(u=0;u<e.length;u++){for(var[n,t,o]=e[u],d=!0,a=0;a<n.length;a++)(!1&o||i>=o)&&Object.keys(c.O).every((e=>c.O[e](n[a])))?n.splice(a--,1):(d=!1,o<i&&(i=o));if(d){e.splice(u--,1);var s=t();void 0!==s&&(r=s)}}return r}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,t,o]},c.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return c.d(r,{a:r}),r},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var o=Object.create(null);c.r(o);var i={};r=r||[null,n({}),n([]),n(n)];for(var d=2&t&&e;"object"==typeof d&&!~r.indexOf(d);d=n(d))Object.getOwnPropertyNames(d).forEach((r=>i[r]=()=>e[r]));return i.default=()=>e,c.d(o,i),o},c.d=(e,r)=>{for(var n in r)c.o(r,n)&&!c.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},c.f={},c.e=e=>Promise.all(Object.keys(c.f).reduce(((r,n)=>(c.f[n](e,r),r)),[])),c.u=e=>"static/js/"+{29:"uk-steps",122:"ja-steps",149:"fr-steps",179:"tr-steps",365:"zh_TW-steps",367:"sw-steps",509:"es-steps",665:"zh_CN-steps",875:"am-steps",884:"ar-steps",904:"pt_BR-steps",950:"zu-steps"}[e]+"."+{29:"ad8cc0c1d099b36fb652",122:"c425b7c855b44887bd5a",149:"8dcb9cbc81509f82e9fb",179:"da27b180bbd46e0b39b0",365:"7a3b2aecb25816a500ef",367:"b52015c009cc4a3ad349",509:"e3a6b4f0b3a58302c68c",665:"2669dcf583e8f55a3632",875:"54abb2724ac1d567ad8a",884:"970da08a0bcd1f6a166f",904:"f7703429f8123efdb1b1",950:"38c123d1621f65d39f90"}[e]+".js",c.hu=e=>e+"."+c.h()+".hot-update.js",c.hmrF=()=>"compatibilitytesting."+c.h()+".hot-update.json",c.h=()=>"619309e5f863873ae05b",c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),c.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t={},o="GUI:",c.l=(e,r,n,i)=>{if(t[e])t[e].push(r);else{var d,a;if(void 0!==n)for(var s=document.getElementsByTagName("script"),u=0;u<s.length;u++){var l=s[u];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==o+n){d=l;break}}d||(a=!0,(d=document.createElement("script")).charset="utf-8",d.timeout=120,c.nc&&d.setAttribute("nonce",c.nc),d.setAttribute("data-webpack",o+n),d.src=e),t[e]=[r];var f=(r,n)=>{d.onerror=d.onload=null,clearTimeout(p);var o=t[e];if(delete t[e],d.parentNode&&d.parentNode.removeChild(d),o&&o.forEach((e=>e(n))),r)return r(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:d}),12e4);d.onerror=f.bind(null,d.onerror),d.onload=f.bind(null,d.onload),a&&document.head.appendChild(d)}},c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e,r,n,t={},o=c.c,i=[],d=[],a="idle",s=0,u=[];function l(e){a=e;for(var r=[],n=0;n<d.length;n++)r[n]=d[n].call(null,e);return Promise.all(r).then((function(){}))}function f(){0==--s&&l("ready").then((function(){if(0===s){var e=u;u=[];for(var r=0;r<e.length;r++)e[r]()}}))}function p(e){if("idle"!==a)throw new Error("check() is only allowed in idle status");return l("check").then(c.hmrM).then((function(n){return n?l("prepare").then((function(){var t=[];return r=[],Promise.all(Object.keys(c.hmrC).reduce((function(e,o){return c.hmrC[o](n.c,n.r,n.m,e,r,t),e}),[])).then((function(){return r=function(){return e?m(e):l("ready").then((function(){return t}))},0===s?r():new Promise((function(e){u.push((function(){e(r())}))}));var r}))})):l(v()?"ready":"idle").then((function(){return null}))}))}function h(e){return"ready"!==a?Promise.resolve().then((function(){throw new Error("apply() is only allowed in ready status (state: "+a+")")})):m(e)}function m(e){e=e||{},v();var t=r.map((function(r){return r(e)}));r=void 0;var o=t.map((function(e){return e.error})).filter(Boolean);if(o.length>0)return l("abort").then((function(){throw o[0]}));var i=l("dispose");t.forEach((function(e){e.dispose&&e.dispose()}));var d,c=l("apply"),a=function(e){d||(d=e)},s=[];return t.forEach((function(e){if(e.apply){var r=e.apply(a);if(r)for(var n=0;n<r.length;n++)s.push(r[n])}})),Promise.all([i,c]).then((function(){return d?l("fail").then((function(){throw d})):n?m(e).then((function(e){return s.forEach((function(r){e.indexOf(r)<0&&e.push(r)})),e})):l("idle").then((function(){return s}))}))}function v(){if(n)return r||(r=[]),Object.keys(c.hmrI).forEach((function(e){n.forEach((function(n){c.hmrI[e](n,r)}))})),n=void 0,!0}c.hmrD=t,c.i.push((function(u){var m,v,b,y,g=u.module,w=function(r,n){var t=o[n];if(!t)return r;var d=function(d){if(t.hot.active){if(o[d]){var c=o[d].parents;-1===c.indexOf(n)&&c.push(n)}else i=[n],e=d;-1===t.children.indexOf(d)&&t.children.push(d)}else console.warn("[HMR] unexpected require("+d+") from disposed module "+n),i=[];return r(d)},c=function(e){return{configurable:!0,enumerable:!0,get:function(){return r[e]},set:function(n){r[e]=n}}};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&"e"!==u&&Object.defineProperty(d,u,c(u));return d.e=function(e,n){return function(e){switch(a){case"ready":l("prepare");case"prepare":return s++,e.then(f,f),e;default:return e}}(r.e(e,n))},d}(u.require,u.id);g.hot=(m=u.id,v=g,y={_acceptedDependencies:{},_acceptedErrorHandlers:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_selfInvalidated:!1,_disposeHandlers:[],_main:b=e!==m,_requireSelf:function(){i=v.parents.slice(),e=b?void 0:m,c(m)},active:!0,accept:function(e,r,n){if(void 0===e)y._selfAccepted=!0;else if("function"==typeof e)y._selfAccepted=e;else if("object"==typeof e&&null!==e)for(var t=0;t<e.length;t++)y._acceptedDependencies[e[t]]=r||function(){},y._acceptedErrorHandlers[e[t]]=n;else y._acceptedDependencies[e]=r||function(){},y._acceptedErrorHandlers[e]=n},decline:function(e){if(void 0===e)y._selfDeclined=!0;else if("object"==typeof e&&null!==e)for(var r=0;r<e.length;r++)y._declinedDependencies[e[r]]=!0;else y._declinedDependencies[e]=!0},dispose:function(e){y._disposeHandlers.push(e)},addDisposeHandler:function(e){y._disposeHandlers.push(e)},removeDisposeHandler:function(e){var r=y._disposeHandlers.indexOf(e);r>=0&&y._disposeHandlers.splice(r,1)},invalidate:function(){switch(this._selfInvalidated=!0,a){case"idle":r=[],Object.keys(c.hmrI).forEach((function(e){c.hmrI[e](m,r)})),l("ready");break;case"ready":Object.keys(c.hmrI).forEach((function(e){c.hmrI[e](m,r)}));break;case"prepare":case"check":case"dispose":case"apply":(n=n||[]).push(m)}},check:p,apply:h,status:function(e){if(!e)return a;d.push(e)},addStatusHandler:function(e){d.push(e)},removeStatusHandler:function(e){var r=d.indexOf(e);r>=0&&d.splice(r,1)},data:t[m]},e=void 0,y),g.parents=i,g.children=[],i=[],u.require=w})),c.hmrC={},c.hmrI={}})(),c.p="/logicleap/",(()=>{var e,r=c.hmrS_jsonp=c.hmrS_jsonp||{251:0};c.f.j=(e,n)=>{var t=c.o(r,e)?r[e]:void 0;if(0!==t)if(t)n.push(t[2]);else{var o=new Promise(((n,o)=>t=r[e]=[n,o]));n.push(t[2]=o);var i=c.p+c.u(e),d=new Error;c.l(i,(n=>{if(c.o(r,e)&&(0!==(t=r[e])&&(r[e]=void 0),t)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;d.message="Loading chunk "+e+" failed.\n("+o+": "+i+")",d.name="ChunkLoadError",d.type=o,d.request=i,t[1](d)}}),"chunk-"+e,e)}};var n,t,o,i,d={};function a(r,n){return e=n,new Promise(((e,n)=>{d[r]=e;var t=c.p+c.hu(r),o=new Error;c.l(t,(e=>{if(d[r]){d[r]=void 0;var t=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;o.message="Loading hot update chunk "+r+" failed.\n("+t+": "+i+")",o.name="ChunkLoadError",o.type=t,o.request=i,n(o)}}))}))}function s(e){function d(e){for(var r=[e],n={},t=r.map((function(e){return{chain:[e],id:e}}));t.length>0;){var o=t.pop(),i=o.id,d=o.chain,s=c.c[i];if(s&&(!s.hot._selfAccepted||s.hot._selfInvalidated)){if(s.hot._selfDeclined)return{type:"self-declined",chain:d,moduleId:i};if(s.hot._main)return{type:"unaccepted",chain:d,moduleId:i};for(var u=0;u<s.parents.length;u++){var l=s.parents[u],f=c.c[l];if(f){if(f.hot._declinedDependencies[i])return{type:"declined",chain:d.concat([l]),moduleId:i,parentId:l};-1===r.indexOf(l)&&(f.hot._acceptedDependencies[i]?(n[l]||(n[l]=[]),a(n[l],[i])):(delete n[l],r.push(l),t.push({chain:d.concat([l]),id:l})))}}}}return{type:"accepted",moduleId:e,outdatedModules:r,outdatedDependencies:n}}function a(e,r){for(var n=0;n<r.length;n++){var t=r[n];-1===e.indexOf(t)&&e.push(t)}}c.f&&delete c.f.jsonpHmr,n=void 0;var s={},u=[],l={},f=function(e){console.warn("[HMR] unexpected require("+e.id+") to disposed module")};for(var p in t)if(c.o(t,p)){var h=t[p],m=h?d(p):{type:"disposed",moduleId:p},v=!1,b=!1,y=!1,g="";switch(m.chain&&(g="\nUpdate propagation: "+m.chain.join(" -> ")),m.type){case"self-declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of self decline: "+m.moduleId+g));break;case"declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of declined dependency: "+m.moduleId+" in "+m.parentId+g));break;case"unaccepted":e.onUnaccepted&&e.onUnaccepted(m),e.ignoreUnaccepted||(v=new Error("Aborted because "+p+" is not accepted"+g));break;case"accepted":e.onAccepted&&e.onAccepted(m),b=!0;break;case"disposed":e.onDisposed&&e.onDisposed(m),y=!0;break;default:throw new Error("Unexception type "+m.type)}if(v)return{error:v};if(b)for(p in l[p]=h,a(u,m.outdatedModules),m.outdatedDependencies)c.o(m.outdatedDependencies,p)&&(s[p]||(s[p]=[]),a(s[p],m.outdatedDependencies[p]));y&&(a(u,[m.moduleId]),l[p]=f)}t=void 0;for(var w,E=[],_=0;_<u.length;_++){var j=u[_],O=c.c[j];O&&(O.hot._selfAccepted||O.hot._main)&&l[j]!==f&&!O.hot._selfInvalidated&&E.push({module:j,require:O.hot._requireSelf,errorHandler:O.hot._selfAccepted})}return{dispose:function(){var e;o.forEach((function(e){delete r[e]})),o=void 0;for(var n,t=u.slice();t.length>0;){var i=t.pop(),d=c.c[i];if(d){var a={},l=d.hot._disposeHandlers;for(_=0;_<l.length;_++)l[_].call(null,a);for(c.hmrD[i]=a,d.hot.active=!1,delete c.c[i],delete s[i],_=0;_<d.children.length;_++){var f=c.c[d.children[_]];f&&((e=f.parents.indexOf(i))>=0&&f.parents.splice(e,1))}}}for(var p in s)if(c.o(s,p)&&(d=c.c[p]))for(w=s[p],_=0;_<w.length;_++)n=w[_],(e=d.children.indexOf(n))>=0&&d.children.splice(e,1)},apply:function(r){for(var n in l)c.o(l,n)&&(c.m[n]=l[n]);for(var t=0;t<i.length;t++)i[t](c);for(var o in s)if(c.o(s,o)){var d=c.c[o];if(d){w=s[o];for(var a=[],f=[],p=[],h=0;h<w.length;h++){var m=w[h],v=d.hot._acceptedDependencies[m],b=d.hot._acceptedErrorHandlers[m];if(v){if(-1!==a.indexOf(v))continue;a.push(v),f.push(b),p.push(m)}}for(var y=0;y<a.length;y++)try{a[y].call(null,w)}catch(n){if("function"==typeof f[y])try{f[y](n,{moduleId:o,dependencyId:p[y]})}catch(t){e.onErrored&&e.onErrored({type:"accept-error-handler-errored",moduleId:o,dependencyId:p[y],error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"accept-errored",moduleId:o,dependencyId:p[y],error:n}),e.ignoreErrored||r(n)}}}for(var g=0;g<E.length;g++){var _=E[g],j=_.module;try{_.require(j)}catch(n){if("function"==typeof _.errorHandler)try{_.errorHandler(n,{moduleId:j,module:c.c[j]})}catch(t){e.onErrored&&e.onErrored({type:"self-accept-error-handler-errored",moduleId:j,error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"self-accept-errored",moduleId:j,error:n}),e.ignoreErrored||r(n)}}return u}}}self.webpackHotUpdateGUI=(r,n,o)=>{for(var a in n)c.o(n,a)&&(t[a]=n[a],e&&e.push(a));o&&i.push(o),d[r]&&(d[r](),d[r]=void 0)},c.hmrI.jsonp=function(e,r){t||(t={},i=[],o=[],r.push(s)),c.o(t,e)||(t[e]=c.m[e])},c.hmrC.jsonp=function(e,d,u,l,f,p){f.push(s),n={},o=d,t=u.reduce((function(e,r){return e[r]=!1,e}),{}),i=[],e.forEach((function(e){c.o(r,e)&&void 0!==r[e]?(l.push(a(e,p)),n[e]=!0):n[e]=!1})),c.f&&(c.f.jsonpHmr=function(e,r){n&&c.o(n,e)&&!n[e]&&(r.push(a(e)),n[e]=!0)})},c.hmrM=()=>{if("undefined"==typeof fetch)throw new Error("No browser support: need fetch API");return fetch(c.p+c.hmrF()).then((e=>{if(404!==e.status){if(!e.ok)throw new Error("Failed to fetch update manifest "+e.statusText);return e.json()}}))},c.O.j=e=>0===r[e];var u=(e,n)=>{var t,o,[i,d,a]=n,s=0;if(i.some((e=>0!==r[e]))){for(t in d)c.o(d,t)&&(c.m[t]=d[t]);if(a)var u=a(c)}for(e&&e(n);s<i.length;s++)o=i[s],c.o(r,o)&&r[o]&&r[o][0](),r[o]=0;return c.O(u)},l=self.webpackChunkGUI=self.webpackChunkGUI||[];l.forEach(u.bind(null,0)),l.push=u.bind(null,l.push.bind(l))})(),c.nc=void 0,c.O(void 0,[340,644,96,223],(()=>c(42440))),c.O(void 0,[340,644,96,223],(()=>c(63943)));var a=c.O(void 0,[340,644,96,223],(()=>c(77828)));return a=c.O(a)})()));