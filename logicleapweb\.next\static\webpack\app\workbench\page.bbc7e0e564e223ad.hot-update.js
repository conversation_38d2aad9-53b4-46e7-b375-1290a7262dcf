"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/NewPublishTaskModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_utils_user__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/user */ \"(app-pages-browser)/./lib/utils/user.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst NewPublishTaskModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, modalData } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        startTime: \"\",\n        endTime: \"\"\n    });\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showQuickTimeSelector, setShowQuickTimeSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 当切换到资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeTab === \"resources\" && isOpen) {\n            fetchWorks();\n        }\n    }, [\n        activeTab,\n        isOpen\n    ]);\n    // 获取教师作品列表\n    const loadTeacherWorks = async ()=>{\n        setLoadingWorks(true);\n        try {\n            const worksList = await (0,_utils__WEBPACK_IMPORTED_MODULE_4__.fetchTeacherWorks)();\n            setWorks(worksList);\n        } catch (error) {\n            console.error(\"获取作品列表失败:\", error);\n            notification.error(\"获取作品列表失败\");\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadTeacherWorks();\n        }\n    }, [\n        isOpen\n    ]);\n    // 处理任务信息输入\n    const handleTaskDataChange = (field, value)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // 处理自评项\n    const handleSelfAssessmentChange = (index, value)=>{\n        const newItems = [\n            ...taskData.selfAssessmentItems\n        ];\n        newItems[index] = value;\n        setTaskData((prev)=>({\n                ...prev,\n                selfAssessmentItems: newItems\n            }));\n    };\n    const addSelfAssessmentItem = ()=>{\n        setTaskData((prev)=>({\n                ...prev,\n                selfAssessmentItems: [\n                    ...prev.selfAssessmentItems,\n                    \"\"\n                ]\n            }));\n    };\n    const removeSelfAssessmentItem = (index)=>{\n        if (taskData.selfAssessmentItems.length > 1) {\n            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n            setTaskData((prev)=>({\n                    ...prev,\n                    selfAssessmentItems: newItems\n                }));\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        setSelectedWorkIds((prev)=>{\n            if (prev.includes(workId)) {\n                return prev.filter((id)=>id !== workId);\n            } else {\n                return [\n                    ...prev,\n                    workId\n                ];\n            }\n        });\n    };\n    // 获取作品列表\n    const fetchWorks = async ()=>{\n        if (loadingWorks) return;\n        setLoadingWorks(true);\n        try {\n            const newWorks = await (0,_utils__WEBPACK_IMPORTED_MODULE_4__.fetchTeacherWorks)();\n            console.log(\"获取到的作品数据:\", newWorks);\n            if (newWorks.length > 0) {\n                console.log(\"第一个作品的数据结构:\", newWorks[0]);\n                console.log(\"第一个作品的标题字段:\", {\n                    title: newWorks[0].title,\n                    name: newWorks[0].name,\n                    workName: newWorks[0].workName\n                });\n            }\n            setWorks(newWorks);\n        } catch (error) {\n            console.error(\"获取作品失败:\", error);\n            setWorks([]);\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 处理文件上传\n    const handleFileUpload = (event)=>{\n        const files = Array.from(event.target.files || []);\n        const validFiles = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.validateUploadFiles)(files);\n        setAttachments((prev)=>[\n                ...prev,\n                ...validFiles\n            ]);\n    };\n    // 移除附件\n    const removeAttachment = (index)=>{\n        setAttachments((prev)=>prev.filter((_, i)=>i !== index));\n    };\n    // 处理水平滚动并阻止外部滚动\n    const handleWheelScroll = (e)=>{\n        // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n        e.stopPropagation();\n        // 查找实际的滚动容器\n        const scrollContainer = e.currentTarget.querySelector(\".works-horizontal-scroll\");\n        if (!scrollContainer) {\n            return;\n        }\n        const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;\n        // 检查是否有可滚动内容\n        const hasScrollableContent = scrollWidth > clientWidth;\n        // 只有当有可滚动内容时才进行水平滚动\n        if (hasScrollableContent && e.deltaY !== 0) {\n            // 增加滚动速度倍数，让滚动更流畅\n            const scrollMultiplier = 4; // 进一步增加滚动速度\n            const deltaX = e.deltaY * scrollMultiplier;\n            // 计算新的滚动位置\n            const newScrollLeft = scrollLeft + deltaX;\n            const maxScrollLeft = scrollWidth - clientWidth;\n            // 限制滚动范围并应用平滑滚动\n            const targetScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));\n            // 使用 requestAnimationFrame 实现更平滑的滚动\n            requestAnimationFrame(()=>{\n                scrollContainer.scrollLeft = targetScrollLeft;\n            });\n        }\n    };\n    // 处理鼠标拖拽滚动\n    const handleMouseDown = (e)=>{\n        const scrollContainer = e.currentTarget.querySelector(\".works-horizontal-scroll\");\n        if (!scrollContainer) return;\n        setIsDragging(true);\n        setDragStart({\n            x: e.pageX - scrollContainer.offsetLeft,\n            scrollLeft: scrollContainer.scrollLeft\n        });\n        // 改变鼠标样式\n        e.currentTarget.style.cursor = \"grabbing\";\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault(); // 鼠标事件可以安全地使用preventDefault\n        const scrollContainer = e.currentTarget.querySelector(\".works-horizontal-scroll\");\n        if (!scrollContainer) return;\n        const x = e.pageX - scrollContainer.offsetLeft;\n        const walk = (x - dragStart.x) * 2; // 调整拖拽速度\n        scrollContainer.scrollLeft = dragStart.scrollLeft - walk;\n    };\n    const handleMouseUp = (e)=>{\n        setIsDragging(false);\n        e.currentTarget.style.cursor = \"grab\";\n    };\n    const handleMouseLeave = (e)=>{\n        setIsDragging(false);\n        e.currentTarget.style.cursor = \"grab\";\n    };\n    // 快速时间选择\n    const handleQuickTimeSelect = (option, type)=>{\n        const targetTime = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.calculateQuickTime)(option, type, taskData.startTime);\n        if (type === \"start\") {\n            setTaskData((prev)=>({\n                    ...prev,\n                    startTime: targetTime.toISOString()\n                }));\n        } else {\n            setTaskData((prev)=>({\n                    ...prev,\n                    endTime: targetTime.toISOString()\n                }));\n        }\n        setShowQuickTimeSelector(null);\n    };\n    // 检查表单是否有效\n    const isFormValid = ()=>{\n        const validation = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.validateTaskForm)(taskData);\n        return validation.isValid;\n    };\n    // 清空表单数据的通用函数\n    const clearFormData = ()=>{\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            startTime: \"\",\n            endTime: \"\"\n        });\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setActiveTab(\"task\"); // 重置到任务信息标签页\n    };\n    // 处理上一步按钮点击\n    const handleBack = ()=>{\n        clearFormData();\n        onBack();\n    };\n    // 处理弹窗关闭\n    const handleClose = ()=>{\n        clearFormData();\n        onClose();\n    };\n    // 确认发布\n    const handleConfirm = async ()=>{\n        // 验证表单\n        const validation = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.validateTaskForm)(taskData);\n        if (!validation.isValid) {\n            notification.error(validation.errorMessage || \"表单验证失败\");\n            return;\n        }\n        try {\n            // 准备任务参数，符合 publishTask 工具函数的参数格式\n            const taskValues = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription,\n                startDate: taskData.startTime,\n                endDate: taskData.endTime,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim()),\n                workIds: selectedWorkIds,\n                allowLateSubmission: false\n            };\n            // 准备班级信息\n            const classInfo = modalData.selectedClass;\n            // 获取教师ID\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_3__.getCurrentUserId)();\n            if (!teacherId) {\n                notification.error(\"无法获取用户信息，请重新登录\");\n                return;\n            }\n            // 准备文件列表（转换为工具函数期望的格式）\n            const fileList = attachments.map((file)=>({\n                    name: file.name,\n                    url: file.name,\n                    response: {\n                        url: file.name\n                    }\n                }));\n            // 调用发布任务工具函数\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_4__.publishTask)(taskValues, classInfo, teacherId, modalData.selectedStudents, [], fileList);\n            if (success) {\n                // 准备最终数据传递给父组件\n                const finalTaskData = {\n                    ...taskData,\n                    selectedWorkIds,\n                    attachments,\n                    modalData\n                };\n                onConfirm(finalTaskData);\n            }\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modal-wrapper\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-content new-publish-task-modal\",\n                onClick: ()=>setShowQuickTimeSelector(null),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: handleClose,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"step-indicator\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step completed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-number\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-label\",\n                                        children: \"选择班级\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step active\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-number\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-label\",\n                                        children: \"发布任务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"tab-switcher\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                onClick: ()=>setActiveTab(\"task\"),\n                                children: \"任务信息\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                onClick: ()=>setActiveTab(\"resources\"),\n                                children: \"资源与附件\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content-body\",\n                        children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"task-info-tab\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        className: \"form-input\",\n                                        placeholder: \"任务名称\",\n                                        value: taskData.taskName,\n                                        onChange: (e)=>setTaskData((prev)=>({\n                                                    ...prev,\n                                                    taskName: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        className: \"form-textarea\",\n                                        placeholder: \"任务描述\",\n                                        value: taskData.taskDescription,\n                                        onChange: (e)=>setTaskData((prev)=>({\n                                                    ...prev,\n                                                    taskDescription: e.target.value\n                                                })),\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"self-assessment-section\",\n                                        children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"add-self-assessment-btn\",\n                                            onClick: ()=>setTaskData((prev)=>({\n                                                        ...prev,\n                                                        selfAssessmentItems: [\n                                                            \"\"\n                                                        ]\n                                                    })),\n                                            children: \"+ 添加自评项\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"自评项\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"self-assessment-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"自评项 \".concat(index + 1),\n                                                                value: item,\n                                                                onChange: (e)=>{\n                                                                    const newItems = [\n                                                                        ...taskData.selfAssessmentItems\n                                                                    ];\n                                                                    newItems[index] = e.target.value;\n                                                                    setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            selfAssessmentItems: newItems\n                                                                        }));\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                className: \"remove-btn\",\n                                                                onClick: ()=>{\n                                                                    const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                    setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            selfAssessmentItems: newItems\n                                                                        }));\n                                                                },\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 25\n                                                    }, undefined)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"add-btn\",\n                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                ...prev,\n                                                                selfAssessmentItems: [\n                                                                    ...prev.selfAssessmentItems,\n                                                                    \"\"\n                                                                ]\n                                                            })),\n                                                    children: \"+ 添加自评项\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"form-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"time-settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"time-row\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"time-field-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"time-field \".concat(taskData.startTime ? \"has-selected-date\" : \"\"),\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                setShowQuickTimeSelector(showQuickTimeSelector === \"start\" ? null : \"start\");\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                value: (0,_utils__WEBPACK_IMPORTED_MODULE_4__.formatTimeDisplay)(taskData.startTime),\n                                                                placeholder: \"点击设置开始时间\",\n                                                                readOnly: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        showQuickTimeSelector === \"start\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"quick-time-selector\",\n                                                            onClick: (e)=>e.stopPropagation(),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"quick-time-btn\",\n                                                                onClick: ()=>handleQuickTimeSelect(\"现在\", \"start\"),\n                                                                children: \"现在\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"time-field-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"time-field \".concat(taskData.endTime ? \"has-selected-date\" : \"\"),\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                setShowQuickTimeSelector(showQuickTimeSelector === \"end\" ? null : \"end\");\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                value: (0,_utils__WEBPACK_IMPORTED_MODULE_4__.formatTimeDisplay)(taskData.endTime),\n                                                                placeholder: \"点击设置结束时间\",\n                                                                readOnly: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        showQuickTimeSelector === \"end\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"quick-time-selector\",\n                                                            onClick: (e)=>e.stopPropagation(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"quick-time-btn\",\n                                                                    onClick: ()=>handleQuickTimeSelect(\"1小时\", \"end\"),\n                                                                    children: \"1小时\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"quick-time-btn\",\n                                                                    onClick: ()=>handleQuickTimeSelect(\"6小时\", \"end\"),\n                                                                    children: \"6小时\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"quick-time-btn\",\n                                                                    onClick: ()=>handleQuickTimeSelect(\"12小时\", \"end\"),\n                                                                    children: \"12小时\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"quick-time-btn\",\n                                                                    onClick: ()=>handleQuickTimeSelect(\"1天\", \"end\"),\n                                                                    children: \"1天\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"quick-time-btn\",\n                                                                    onClick: ()=>handleQuickTimeSelect(\"7天\", \"end\"),\n                                                                    children: \"7天\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"resources-tab\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"works-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"选择作品\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"help-text\",\n                                            children: \"选择作品作为任务参考资料（可多选）\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative works-scroll-wrapper\",\n                                            onWheel: handleWheelScroll,\n                                            onMouseDown: handleMouseDown,\n                                            onMouseMove: handleMouseMove,\n                                            onMouseUp: handleMouseUp,\n                                            onMouseLeave: handleMouseLeave,\n                                            style: {\n                                                minHeight: \"200px\",\n                                                cursor: \"grab\",\n                                                userSelect: \"none\"\n                                            },\n                                            children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-container\",\n                                                style: {\n                                                    minHeight: \"200px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"加载中...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 21\n                                            }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"works-horizontal-scroll\",\n                                                children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                        onClick: ()=>handleWorkSelect(work.id),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"work-image\",\n                                                                children: [\n                                                                    work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: work.coverImage || work.screenShotImage,\n                                                                        alt: work.title,\n                                                                        onError: (e)=>{\n                                                                            const target = e.currentTarget;\n                                                                            target.style.display = \"none\";\n                                                                            const nextElement = target.nextElementSibling;\n                                                                            if (nextElement) {\n                                                                                nextElement.style.display = \"flex\";\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 31\n                                                                    }, undefined) : null,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"work-placeholder\",\n                                                                        style: {\n                                                                            display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                        },\n                                                                        children: \"作品\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"work-title\",\n                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"selected-indicator\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, work.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"empty-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"empty-text\",\n                                                    children: \"作品列表\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"attachments-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"附件上传\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"upload-area\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    multiple: true,\n                                                    accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                    onChange: handleFileUpload,\n                                                    style: {\n                                                        display: \"none\"\n                                                    },\n                                                    id: \"file-upload\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"file-upload\",\n                                                    className: \"upload-btn\",\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"file-format-info\",\n                                                    children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"attachments-list\",\n                                            children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"attachment-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"file-name\",\n                                                            children: file.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeAttachment(index),\n                                                            className: \"remove-attachment-btn\",\n                                                            children: \"\\xd7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"prev-btn\",\n                                onClick: handleBack,\n                                children: \"上一步\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"start-class-btn \".concat(isFormValid() ? \"enabled\" : \"disabled\"),\n                                onClick: handleConfirm,\n                                disabled: !isFormValid(),\n                                children: \"发布\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n            lineNumber: 374,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\NewPublishTaskModal.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, undefined);\n};\n_s(NewPublishTaskModal, \"IZu3TGLcdi4BL7ozG0hFz/FS1rs=\");\n_c = NewPublishTaskModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (NewPublishTaskModal);\nvar _c;\n$RefreshReg$(_c, \"NewPublishTaskModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.tsx\n"));

/***/ })

});