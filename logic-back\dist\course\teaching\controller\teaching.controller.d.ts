import { TeachingService } from '../service/teaching.service';
import { HttpResponseResultService } from '../../../web/http_response_result/http_response_result.service';
import { OneClickStartDto } from '../dto/one-click-start.dto';
import { GetTeachingRecordsQueryDto } from '../dto/teaching-records.dto';
export declare class TeachingController {
    private readonly teachingService;
    private readonly httpResponseResultService;
    constructor(teachingService: TeachingService, httpResponseResultService: HttpResponseResultService);
    private handleTeachingException;
    oneClickStart(dto: OneClickStartDto, currentUser: any): Promise<{
        code: any;
        msg: any;
        data: any;
    }>;
    getCourseSettings(courseId: string): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/course-settings.dto").CourseSettingsDataDto>>;
    getTeachingRecords(query: GetTeachingRecordsQueryDto): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/teaching-records.dto").TeachingRecordsDataDto>>;
}
