"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseTag = void 0;
const typeorm_1 = require("typeorm");
let CourseTag = class CourseTag {
    id;
    name;
    color;
    category;
    description;
    usageCount;
    status;
    createdAt;
    updatedAt;
};
exports.CourseTag = CourseTag;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '标签ID' }),
    __metadata("design:type", Number)
], CourseTag.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_name', { unique: true }),
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, comment: '标签名称' }),
    __metadata("design:type", String)
], CourseTag.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 7, default: '#007bff', comment: '标签颜色' }),
    __metadata("design:type", String)
], CourseTag.prototype, "color", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_category'),
    (0, typeorm_1.Index)('idx_category_status'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 3, comment: '标签分类：0=难度，1=类型，2=特色，3=其他' }),
    __metadata("design:type", Number)
], CourseTag.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '标签描述' }),
    __metadata("design:type", String)
], CourseTag.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_usage'),
    (0, typeorm_1.Column)({ type: 'int', name: 'usage_count', default: 0, comment: '使用次数统计' }),
    __metadata("design:type", Number)
], CourseTag.prototype, "usageCount", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Index)('idx_category_status'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 1, comment: '标签状态：0=禁用，1=启用' }),
    __metadata("design:type", Number)
], CourseTag.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], CourseTag.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], CourseTag.prototype, "updatedAt", void 0);
exports.CourseTag = CourseTag = __decorate([
    (0, typeorm_1.Entity)('course_tags')
], CourseTag);
//# sourceMappingURL=course-tag.entity.js.map