import { Repository } from 'typeorm';
import { CourseSeries } from '../../entities/course-series.entity';
import { Course } from '../../entities/course.entity';
import { CourseTag } from '../../entities/course-tag.entity';
import { CourseSeriesTag } from '../../entities/course-series-tag.entity';
import { GetSeriesListQueryDto, SeriesListDataDto } from '../dto/series-list.dto';
import { SeriesDetailDataDto } from '../dto/series-detail.dto';
import { CourseDetailDataDto } from '../dto/course-detail.dto';
import { GetTagsListQueryDto, TagsListDataDto } from '../dto/tags-list.dto';
import { CreateTagDto, UpdateTagDto, TagDetailDataDto } from '../dto/tag-management.dto';
export declare class MarketplaceService {
    private readonly courseSeriesRepository;
    private readonly courseRepository;
    private readonly courseTagRepository;
    private readonly courseSeriesTagRepository;
    private readonly logger;
    constructor(courseSeriesRepository: Repository<CourseSeries>, courseRepository: Repository<Course>, courseTagRepository: Repository<CourseTag>, courseSeriesTagRepository: Repository<CourseSeriesTag>);
    getSeriesList(query: GetSeriesListQueryDto): Promise<SeriesListDataDto>;
    getSeriesDetail(seriesId: number): Promise<SeriesDetailDataDto>;
    getCourseDetail(seriesId: number, courseId: number): Promise<CourseDetailDataDto>;
    getTagsList(query: GetTagsListQueryDto): Promise<TagsListDataDto>;
    createTag(createTagDto: CreateTagDto): Promise<TagDetailDataDto>;
    updateTag(id: number, updateTagDto: UpdateTagDto): Promise<TagDetailDataDto>;
    deleteTag(id: number): Promise<void>;
    getTagById(id: number): Promise<TagDetailDataDto>;
    private applyTagFilters;
    private applyFilters;
    private applySorting;
    private getSeriesTagsMap;
    private calculateContentSummary;
    private getFilterStats;
    private getCategoryLabel;
    private getStatusLabel;
    private getTagCategoryLabel;
    private getTagStatusLabel;
    private formatDuration;
}
