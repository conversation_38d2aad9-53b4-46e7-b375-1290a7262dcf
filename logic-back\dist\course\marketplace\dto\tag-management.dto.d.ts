export declare class CreateTagDto {
    name: string;
    color?: string;
    category: number;
    description?: string;
    status?: number;
}
export declare class UpdateTagDto {
    name?: string;
    color?: string;
    category?: number;
    description?: string;
    status?: number;
}
export declare class TagDetailDataDto {
    id: number;
    name: string;
    color: string;
    category: number;
    categoryLabel: string;
    description: string;
    usageCount: number;
    status: number;
    statusLabel: string;
    createdAt: string;
    updatedAt: string;
}
export declare class TagDetailResponseDto {
    code: number;
    message: string;
    data: TagDetailDataDto;
}
export declare class DeleteTagDataDto {
    success: boolean;
}
export declare class DeleteTagResponseDto {
    code: number;
    message: string;
    data: DeleteTagDataDto;
}
