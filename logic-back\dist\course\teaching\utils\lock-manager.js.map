{"version": 3, "file": "lock-manager.js", "sourceRoot": "", "sources": ["../../../../src/course/teaching/utils/lock-manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2EAAiF;AAO1E,IAAM,WAAW,mBAAjB,MAAM,WAAW;IACL,MAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IACtC,KAAK,GAAG,IAAI,GAAG,EAA8C,CAAC;IAC9D,eAAe,CAAiB;IAEjD;QAEE,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,MAAc,KAAK;QACpD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAG7C,IAAI,YAAY,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;YACtE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,OAAO,UAAU,GAAG,IAAI,CAAC,CAAC;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,OAAe;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,OAAe,EACf,SAA2B,EAC3B,MAAc,KAAK,EACnB,aAAqB,CAAC,EACtB,aAAqB,IAAI;QAEzB,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,OAAO,OAAO,GAAG,UAAU,EAAE,CAAC;YAC5B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAE1D,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,OAAO,EAAE,CAAC,CAAC;oBAC5C,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;oBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,OAAO,EAAE,CAAC,CAAC;oBAC5C,OAAO,MAAM,CAAC;gBAChB,CAAC;wBAAS,CAAC;oBACT,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,OAAO,EAAE,CAAC;YACV,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,UAAU,UAAU,OAAO,IAAI,UAAU,MAAM,OAAO,EAAE,CAAC,CAAC;gBACpF,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAGD,MAAM,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjD,MAAM,IAAI,kDAA4B,CACpC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,EACtB,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,EACrB,OAAO,CACR,CAAC;IACJ,CAAC;IAKO,mBAAmB;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,YAAY,OAAO,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAKD,aAAa;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAoD,EAAE,CAAC;QAEnE,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,MAAM,CAAC,GAAG,CAAC,GAAG;gBACZ,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS;gBACzB,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,eAAe;QACb,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;CACF,CAAA;AA1IY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;;GACA,WAAW,CA0IvB"}