'use client';

import React, { useState, useEffect } from 'react';
import { X, Upload, Plus } from 'lucide-react';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { getCurrentUserId } from '@/lib/utils/user';
import {
  fetchTeacherWorks,
  validateUploadFiles,
  formatTimeDisplay,
  calculateQuickTime,
  validateTaskForm,
  publishTask
} from '../utils';
import './NewPublishTaskModal.css';

interface WorkItem {
  id: number;
  title: string;
  name?: string;
  workName?: string;
  description?: string;
  coverImage?: string;
  screenShotImage?: string;
  type: number;
  status: number;
  createTime: string;
  updateTime: string;
}

interface NewPublishTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onConfirm: (taskData: any) => void;
  modalData: {
    selectedDistribution: string;
    energyAmount: string;
    selectedTemplate: any;
    selectedStudents: number[];
    selectedSchool?: any;
    selectedClass?: any;
  };
}

const NewPublishTaskModal: React.FC<NewPublishTaskModalProps> = ({
  isOpen,
  onClose,
  onBack,
  onConfirm,
  modalData
}) => {
  const [activeTab, setActiveTab] = useState<'task' | 'resources'>('task');
  const [taskData, setTaskData] = useState({
    taskName: '',
    taskDescription: '',
    selfAssessmentItems: [] as string[],
    startTime: '',
    endTime: ''
  });
  const [works, setWorks] = useState<WorkItem[]>([]);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [showQuickTimeSelector, setShowQuickTimeSelector] = useState<'start' | 'end' | null>(null);

  // 鼠标拖拽滚动状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  const notification = GetNotification();

  // 当切换到资源标签页时获取作品列表
  useEffect(() => {
    if (activeTab === 'resources' && isOpen) {
      fetchWorks();
    }
  }, [activeTab, isOpen]);

  // 获取教师作品列表
  const loadTeacherWorks = async () => {
    setLoadingWorks(true);
    try {
      const worksList = await fetchTeacherWorks();
      setWorks(worksList);
    } catch (error) {
      console.error('获取作品列表失败:', error);
      notification.error('获取作品列表失败');
    } finally {
      setLoadingWorks(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadTeacherWorks();
    }
  }, [isOpen]);

  // 处理任务信息输入
  const handleTaskDataChange = (field: string, value: string) => {
    setTaskData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理自评项
  const handleSelfAssessmentChange = (index: number, value: string) => {
    const newItems = [...taskData.selfAssessmentItems];
    newItems[index] = value;
    setTaskData(prev => ({
      ...prev,
      selfAssessmentItems: newItems
    }));
  };

  const addSelfAssessmentItem = () => {
    setTaskData(prev => ({
      ...prev,
      selfAssessmentItems: [...prev.selfAssessmentItems, '']
    }));
  };

  const removeSelfAssessmentItem = (index: number) => {
    if (taskData.selfAssessmentItems.length > 1) {
      const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
      setTaskData(prev => ({
        ...prev,
        selfAssessmentItems: newItems
      }));
    }
  };

  // 处理作品选择
  const handleWorkSelect = (workId: number) => {
    setSelectedWorkIds(prev => {
      if (prev.includes(workId)) {
        return prev.filter(id => id !== workId);
      } else {
        return [...prev, workId];
      }
    });
  };

  // 获取作品列表
  const fetchWorks = async () => {
    if (loadingWorks) return;

    setLoadingWorks(true);
    try {
      const newWorks = await fetchTeacherWorks();

      console.log('获取到的作品数据:', newWorks);
      if (newWorks.length > 0) {
        console.log('第一个作品的数据结构:', newWorks[0]);
        console.log('第一个作品的标题字段:', {
          title: newWorks[0].title,
          name: newWorks[0].name,
          workName: newWorks[0].workName
        });
      }
      setWorks(newWorks);
    } catch (error) {
      console.error('获取作品失败:', error);
      setWorks([]);
    } finally {
      setLoadingWorks(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = validateUploadFiles(files);
    setAttachments(prev => [...prev, ...validFiles]);
  };

  // 移除附件
  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // 处理水平滚动并阻止外部滚动
  const handleWheelScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    // 只阻止事件传播，不调用preventDefault避免被动监听器警告
    e.stopPropagation();

    // 查找实际的滚动容器
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;

    if (!scrollContainer) {
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;

    // 检查是否有可滚动内容
    const hasScrollableContent = scrollWidth > clientWidth;

    // 只有当有可滚动内容时才进行水平滚动
    if (hasScrollableContent && e.deltaY !== 0) {
      // 增加滚动速度倍数，让滚动更流畅
      const scrollMultiplier = 4; // 进一步增加滚动速度
      const deltaX = e.deltaY * scrollMultiplier;

      // 计算新的滚动位置
      const newScrollLeft = scrollLeft + deltaX;
      const maxScrollLeft = scrollWidth - clientWidth;

      // 限制滚动范围并应用平滑滚动
      const targetScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

      // 使用 requestAnimationFrame 实现更平滑的滚动
      requestAnimationFrame(() => {
        scrollContainer.scrollLeft = targetScrollLeft;
      });
    }
  };

  // 处理鼠标拖拽滚动
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    setIsDragging(true);
    setDragStart({
      x: e.pageX - scrollContainer.offsetLeft,
      scrollLeft: scrollContainer.scrollLeft
    });

    // 改变鼠标样式
    e.currentTarget.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;

    e.preventDefault(); // 鼠标事件可以安全地使用preventDefault
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    const x = e.pageX - scrollContainer.offsetLeft;
    const walk = (x - dragStart.x) * 2; // 调整拖拽速度
    scrollContainer.scrollLeft = dragStart.scrollLeft - walk;
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };



  // 快速时间选择
  const handleQuickTimeSelect = (option: string, type: 'start' | 'end') => {
    const targetTime = calculateQuickTime(option, type, taskData.startTime);

    if (type === 'start') {
      setTaskData(prev => ({ ...prev, startTime: targetTime.toISOString() }));
    } else {
      setTaskData(prev => ({ ...prev, endTime: targetTime.toISOString() }));
    }

    setShowQuickTimeSelector(null);
  };

  // 检查表单是否有效
  const isFormValid = () => {
    const validation = validateTaskForm(taskData);
    return validation.isValid;
  };

  // 清空表单数据的通用函数
  const clearFormData = () => {
    setTaskData({
      taskName: '',
      taskDescription: '',
      selfAssessmentItems: [],
      startTime: '',
      endTime: ''
    });
    setSelectedWorkIds([]);
    setAttachments([]);
    setActiveTab('task'); // 重置到任务信息标签页
  };

  // 处理上一步按钮点击
  const handleBack = () => {
    clearFormData();
    onBack();
  };

  // 处理弹窗关闭
  const handleClose = () => {
    clearFormData();
    onClose();
  };

  // 确认发布
  const handleConfirm = async () => {
    // 验证表单
    const validation = validateTaskForm(taskData);
    if (!validation.isValid) {
      notification.error(validation.errorMessage || '表单验证失败');
      return;
    }

    try {
      // 准备任务参数，符合 publishTask 工具函数的参数格式
      const taskValues = {
        taskName: taskData.taskName,
        taskDescription: taskData.taskDescription,
        startDate: taskData.startTime,
        endDate: taskData.endTime,
        selfAssessmentItems: taskData.selfAssessmentItems.filter(item => item.trim()),
        workIds: selectedWorkIds,
        allowLateSubmission: false
      };

      // 准备班级信息
      const classInfo = modalData.selectedClass;

      // 获取教师ID
      const teacherId = getCurrentUserId();
      if (!teacherId) {
        notification.error('无法获取用户信息，请重新登录');
        return;
      }

      // 准备文件列表（转换为工具函数期望的格式）
      const fileList = attachments.map(file => ({
        name: file.name,
        url: file.name, // 这里应该是上传后的URL
        response: { url: file.name }
      }));

      // 调用发布任务工具函数
      const success = await publishTask(
        taskValues,
        classInfo,
        teacherId,
        modalData.selectedStudents,
        [], // students 参数，如果 selectedStudents 为空则使用所有学生
        fileList
      );

      if (success) {
        // 准备最终数据传递给父组件
        const finalTaskData = {
          ...taskData,
          selectedWorkIds,
          attachments,
          modalData
        };

        onConfirm(finalTaskData);
      }
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error('任务发布失败，请重试');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-wrapper">
        <div
          className="modal-content new-publish-task-modal"
          onClick={() => setShowQuickTimeSelector(null)}
        >
          <button className="modal-close-btn-outside" onClick={handleClose}>
            <X size={16} />
          </button>

        {/* 步骤指示器 */}
        <div className="step-indicator">
          <div className="step completed">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          <div className="step active">
            <div className="step-number">2</div>
            <div className="step-label">发布任务</div>
          </div>
        </div>

        {/* 标签页切换 */}
        <div className="tab-switcher">
          <button 
            className={`tab-btn ${activeTab === 'task' ? 'active' : ''}`}
            onClick={() => setActiveTab('task')}
          >
            任务信息
          </button>
          <button 
            className={`tab-btn ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => setActiveTab('resources')}
          >
            资源与附件
          </button>
        </div>

        {/* 内容区域 */}
        <div className="modal-content-body">
          {activeTab === 'task' ? (
            <div className="task-info-tab">
              <div className="form-group">
                <input
                  type="text"
                  className="form-input"
                  placeholder="任务名称"
                  value={taskData.taskName}
                  onChange={(e) => setTaskData(prev => ({ ...prev, taskName: e.target.value }))}
                />
              </div>

              <div className="form-group">
                <textarea
                  className="form-textarea"
                  placeholder="任务描述"
                  value={taskData.taskDescription}
                  onChange={(e) => setTaskData(prev => ({ ...prev, taskDescription: e.target.value }))}
                  rows={4}
                />
              </div>

              <div className="form-group">
                <div className="self-assessment-section">
                  {taskData.selfAssessmentItems.length === 0 ? (
                    <button
                      type="button"
                      className="add-self-assessment-btn"
                      onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [''] }))}
                    >
                      + 添加自评项
                    </button>
                  ) : (
                    <>
                      <label className="form-label">自评项</label>
                      {taskData.selfAssessmentItems.map((item, index) => (
                        <div key={index} className="self-assessment-item">
                          <input
                            type="text"
                            className="form-input"
                            placeholder={`自评项 ${index + 1}`}
                            value={item}
                            onChange={(e) => {
                              const newItems = [...taskData.selfAssessmentItems];
                              newItems[index] = e.target.value;
                              setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                            }}
                          />
                          <button
                            type="button"
                            className="remove-btn"
                            onClick={() => {
                              const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
                              setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                            }}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        className="add-btn"
                        onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [...prev.selfAssessmentItems, ''] }))}
                      >
                        + 添加自评项
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* 时间设置 */}
              <div className="form-group">
                <div className="time-settings">
                  <div className="time-row">
                    <div className="time-field-container">
                      <div
                        className={`time-field ${taskData.startTime ? 'has-selected-date' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowQuickTimeSelector(showQuickTimeSelector === 'start' ? null : 'start');
                        }}
                      >
                        <input
                          type="text"
                          className="form-input"
                          value={formatTimeDisplay(taskData.startTime)}
                          placeholder="点击设置开始时间"
                          readOnly
                        />
                      </div>
                      {showQuickTimeSelector === 'start' && (
                        <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('现在', 'start')}
                          >
                            现在
                          </button>
                        </div>
                      )}
                    </div>
                    <div className="time-field-container">
                      <div
                        className={`time-field ${taskData.endTime ? 'has-selected-date' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowQuickTimeSelector(showQuickTimeSelector === 'end' ? null : 'end');
                        }}
                      >
                        <input
                          type="text"
                          className="form-input"
                          value={formatTimeDisplay(taskData.endTime)}
                          placeholder="点击设置结束时间"
                          readOnly
                        />
                      </div>
                      {showQuickTimeSelector === 'end' && (
                        <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('1小时', 'end')}
                          >
                            1小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('6小时', 'end')}
                          >
                            6小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('12小时', 'end')}
                          >
                            12小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('1天', 'end')}
                          >
                            1天
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('7天', 'end')}
                          >
                            7天
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="resources-tab">
              <div className="works-section">
                <h4>选择作品</h4>
                <p className="help-text">选择作品作为任务参考资料（可多选）</p>
                <div
                  className="relative works-scroll-wrapper"
                  onWheel={handleWheelScroll}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  style={{
                    minHeight: '200px',
                    cursor: 'grab',
                    userSelect: 'none'
                  }}
                >
                  {loadingWorks ? (
                    <div
                      className="loading-container"
                      style={{ minHeight: '200px' }}
                    >
                      <div className="loading-spinner"></div>
                      <span>加载中...</span>
                    </div>
                  ) : works.length > 0 ? (
                    <div className="works-horizontal-scroll">
                      {works.map((work) => (
                        <div
                          key={work.id}
                          className={`work-card ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}
                          onClick={() => handleWorkSelect(work.id)}
                        >
                          <div className="work-image">
                            {work.coverImage || work.screenShotImage ? (
                              <img
                                src={work.coverImage || work.screenShotImage}
                                alt={work.title}
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.style.display = 'none';
                                  const nextElement = target.nextElementSibling as HTMLElement;
                                  if (nextElement) {
                                    nextElement.style.display = 'flex';
                                  }
                                }}
                              />
                            ) : null}
                            <div className="work-placeholder" style={{ display: work.coverImage || work.screenShotImage ? 'none' : 'flex' }}>
                              作品
                            </div>
                          </div>
                          <div className="work-title">{work.title || work.name || work.workName || '未命名作品'}</div>
                          {selectedWorkIds.includes(work.id) && (
                            <div className="selected-indicator">✓</div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-placeholder">
                      <div className="empty-text">作品列表</div>
                    </div>
                  )}
                </div>
              </div>

              <div className="attachments-section">
                <h4>附件上传</h4>
                <div className="upload-area">
                  <input
                    type="file"
                    multiple
                    accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                    onChange={handleFileUpload}
                    style={{ display: 'none' }}
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="upload-btn">
                    +
                  </label>
                  <span className="file-format-info">
                    支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB
                  </span>
                </div>
                {attachments.length > 0 && (
                  <div className="attachments-list">
                    {attachments.map((file, index) => (
                      <div key={index} className="attachment-item">
                        <span className="file-name">{file.name}</span>
                        <button
                          onClick={() => removeAttachment(index)}
                          className="remove-attachment-btn"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="modal-footer">
          <button
            className="prev-btn"
            onClick={handleBack}
          >
            上一步
          </button>
          <button
            className={`start-class-btn ${isFormValid() ? 'enabled' : 'disabled'}`}
            onClick={handleConfirm}
            disabled={!isFormValid()}
          >
            发布
          </button>
        </div>
      </div>
      </div>
    </div>
  );
};

export default NewPublishTaskModal;
