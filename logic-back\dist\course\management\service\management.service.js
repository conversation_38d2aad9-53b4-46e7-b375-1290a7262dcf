"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const course_series_entity_1 = require("../../entities/course-series.entity");
const course_entity_1 = require("../../entities/course.entity");
const task_template_entity_1 = require("../../entities/task-template.entity");
const course_settings_entity_1 = require("../../entities/course-settings.entity");
const course_series_tag_entity_1 = require("../../entities/course-series-tag.entity");
const course_tag_entity_1 = require("../../entities/course-tag.entity");
let ManagementService = class ManagementService {
    courseSeriesRepository;
    courseRepository;
    taskTemplateRepository;
    courseSettingsRepository;
    courseSeriesTagRepository;
    courseTagRepository;
    dataSource;
    constructor(courseSeriesRepository, courseRepository, taskTemplateRepository, courseSettingsRepository, courseSeriesTagRepository, courseTagRepository, dataSource) {
        this.courseSeriesRepository = courseSeriesRepository;
        this.courseRepository = courseRepository;
        this.taskTemplateRepository = taskTemplateRepository;
        this.courseSettingsRepository = courseSettingsRepository;
        this.courseSeriesTagRepository = courseSeriesTagRepository;
        this.courseTagRepository = courseTagRepository;
        this.dataSource = dataSource;
    }
    async getMyCourseSeries(userId, page, pageSize, status, keyword) {
        const skip = (page - 1) * pageSize;
        const where = { creatorId: userId };
        if (status !== undefined) {
            where.status = status;
        }
        if (keyword) {
            where.title = (0, typeorm_2.Like)(`%${keyword}%`);
        }
        const [items, total] = await this.courseSeriesRepository.findAndCount({
            where,
            skip,
            take: pageSize,
            order: { createdAt: 'DESC' }
        });
        if (items.length === 0) {
            return {
                items: [],
                page,
                pageSize,
                total,
                totalPages: Math.ceil(total / pageSize)
            };
        }
        const seriesIds = items.map(item => item.id);
        const courseStats = await this.courseRepository.createQueryBuilder('course')
            .select('course.series_id', 'seriesId')
            .addSelect('COUNT(*)', 'totalCourses')
            .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourses')
            .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourses')
            .addSelect('SUM(COALESCE(course.resources_count, 0))', 'resourcesCount')
            .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
            .where('course.series_id IN (:...seriesIds)', { seriesIds })
            .groupBy('course.series_id')
            .getRawMany();
        const statsMap = new Map(courseStats.map(stat => [Number(stat.seriesId), stat]));
        for (const item of items) {
            const stats = statsMap.get(item.id) || {
                totalCourses: 0, videoCourses: 0, documentCourses: 0,
                resourcesCount: 0, publishedCourses: 0
            };
            const totalCourses = Number(stats.totalCourses) || 0;
            const publishedCourses = Number(stats.publishedCourses) || 0;
            const completionRate = totalCourses > 0 ? publishedCourses / totalCourses : 0;
            item['_contentSummary'] = {
                videoCourseCount: Number(stats.videoCourses) || 0,
                documentCourseCount: Number(stats.documentCourses) || 0,
                totalResourcesCount: Number(stats.resourcesCount) || 0,
                completionRate: parseFloat(completionRate.toFixed(2))
            };
        }
        return {
            items,
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
        };
    }
    async findCourseSeriesById(id) {
        const series = await this.courseSeriesRepository.findOne({ where: { id } });
        if (!series) {
            throw new common_1.NotFoundException(`系列ID为${id}的课程系列不存在`);
        }
        return series;
    }
    async createCourseSeries(courseSeriesData) {
        return await this.dataSource.transaction(async (manager) => {
            try {
                if (!courseSeriesData.title || courseSeriesData.title.trim() === '') {
                    throw new common_1.BadRequestException('系列名称不能为空');
                }
                if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
                    const existingTags = await manager.find(course_tag_entity_1.CourseTag, {
                        where: { id: (0, typeorm_2.In)(courseSeriesData.tagIds) }
                    });
                    if (existingTags.length !== courseSeriesData.tagIds.length) {
                        throw new common_1.BadRequestException('部分标签不存在');
                    }
                }
                const newSeries = manager.create(course_series_entity_1.CourseSeries, {
                    title: courseSeriesData.title,
                    description: courseSeriesData.description,
                    coverImage: courseSeriesData.coverImage,
                    category: courseSeriesData.category || 0,
                    status: 0,
                    projectMembers: courseSeriesData.projectMembers,
                    creatorId: courseSeriesData.creatorId,
                    totalCourses: 0,
                    totalStudents: 0
                });
                const savedSeries = await manager.save(course_series_entity_1.CourseSeries, newSeries);
                if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
                    const seriesTagRelations = courseSeriesData.tagIds.map(tagId => ({
                        seriesId: savedSeries.id,
                        tagId: tagId
                    }));
                    await manager.save(course_series_tag_entity_1.CourseSeriesTag, seriesTagRelations);
                }
                return savedSeries;
            }
            catch (error) {
                if (error instanceof common_1.BadRequestException) {
                    throw error;
                }
                throw new common_1.BadRequestException(`创建系列课程失败: ${error.message}`);
            }
        });
    }
    async updateCourseSeries(id, updateData, userId) {
        return await this.dataSource.transaction(async (manager) => {
            const series = await this.findCourseSeriesById(id);
            if (userId && series.creatorId !== userId) {
                throw new common_1.ForbiddenException('无权限更新此课程系列');
            }
            const tagIds = updateData.tagIds;
            delete updateData.tagIds;
            delete updateData.id;
            delete updateData.createdAt;
            delete updateData.updatedAt;
            delete updateData.creatorId;
            if (tagIds && tagIds.length > 0) {
                const existingTags = await manager.find(course_tag_entity_1.CourseTag, {
                    where: { id: (0, typeorm_2.In)(tagIds) }
                });
                if (existingTags.length !== tagIds.length) {
                    throw new common_1.BadRequestException('部分标签不存在');
                }
            }
            Object.assign(series, updateData);
            const updatedSeries = await manager.save(course_series_entity_1.CourseSeries, series);
            if (tagIds !== undefined) {
                await manager.delete(course_series_tag_entity_1.CourseSeriesTag, { seriesId: id });
                if (tagIds && tagIds.length > 0) {
                    const seriesTagRelations = tagIds.map((tagId) => ({
                        seriesId: id,
                        tagId: tagId
                    }));
                    await manager.save(course_series_tag_entity_1.CourseSeriesTag, seriesTagRelations);
                }
            }
            return updatedSeries;
        });
    }
    async removeCourseSeries(id, userId) {
        const series = await this.findCourseSeriesById(id);
        if (userId && series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限删除此课程系列');
        }
        await this.courseSeriesRepository.remove(series);
        return {
            success: true,
            message: `ID为${id}的课程系列已成功删除`
        };
    }
    async publishCourseSeries(seriesId, userId) {
        const series = await this.findCourseSeriesById(seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限发布此课程系列');
        }
        const courseCount = await this.courseRepository.count({ where: { seriesId } });
        if (courseCount <= 0) {
            throw new common_1.BadRequestException('发布失败：课程系列中至少需要包含一个课程');
        }
        const publishStats = await this.getCourseSeriesPublishStats(seriesId);
        series.status = 1;
        await this.courseSeriesRepository.save(series);
        return {
            success: true,
            message: '课程系列发布成功',
            data: series,
            publishStats
        };
    }
    async getCourseSeriesPublishStats(seriesId) {
        const courseStats = await this.courseRepository.createQueryBuilder('course')
            .select('COUNT(*)', 'totalCourses')
            .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
            .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourseCount')
            .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourseCount')
            .addSelect('SUM(course.video_duration)', 'totalVideoDuration')
            .addSelect('SUM(COALESCE(JSON_LENGTH(course.additional_resources), 0))', 'totalResourcesCount')
            .where('course.series_id = :seriesId', { seriesId })
            .getRawOne();
        return {
            videoCourseCount: Number(courseStats.videoCourseCount) || 0,
            documentCourseCount: Number(courseStats.documentCourseCount) || 0,
            totalVideoDuration: Number(courseStats.totalVideoDuration) || 0,
            totalResourcesCount: Number(courseStats.totalResourcesCount) || 0,
            publishedCourses: Number(courseStats.publishedCourses) || 0,
            totalCourses: Number(courseStats.totalCourses) || 0
        };
    }
    async getSeriesCourses(seriesId, status, page = 1, pageSize = 20, userId) {
        const series = await this.findCourseSeriesById(seriesId);
        if (userId && series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限查看此系列课程');
        }
        const where = { seriesId };
        if (status !== undefined) {
            where.status = status;
        }
        const total = await this.courseRepository.count({ where });
        const skip = (page - 1) * pageSize;
        const courses = await this.courseRepository.find({
            where,
            order: { orderIndex: 'ASC' },
            skip,
            take: pageSize
        });
        return {
            seriesId,
            courses,
            total,
            page,
            pageSize
        };
    }
    async createCourse(courseData) {
        try {
            if (!courseData.title || courseData.title.trim() === '') {
                throw new common_1.BadRequestException('课程标题不能为空');
            }
            if (!courseData.seriesId) {
                throw new common_1.BadRequestException('必须指定所属系列ID');
            }
            await this.findCourseSeriesById(courseData.seriesId);
            const maxOrderIndex = await this.courseRepository
                .createQueryBuilder('course')
                .select('MAX(course.order_index)', 'maxOrder')
                .where('course.series_id = :seriesId', { seriesId: courseData.seriesId })
                .getRawOne();
            const nextOrderIndex = (maxOrderIndex?.maxOrder || 0) + 1;
            return await this.courseRepository.manager.transaction(async (manager) => {
                const newCourse = this.courseRepository.create({
                    seriesId: courseData.seriesId,
                    title: courseData.title,
                    description: courseData.description,
                    coverImage: courseData.coverImage,
                    hasVideo: courseData.hasVideo || 0,
                    hasDocument: courseData.hasDocument || 0,
                    hasAudio: courseData.hasAudio || 0,
                    videoDuration: courseData.videoDuration || 0,
                    contentConfig: courseData.contentConfig || {},
                    teachingInfo: courseData.teachingInfo || [],
                    additionalResources: courseData.additionalResources || [],
                    orderIndex: courseData.orderIndex || nextOrderIndex,
                    status: 0,
                    creatorId: courseData.creatorId
                });
                const savedCourse = await manager.save(course_entity_1.Course, newCourse);
                await this.updateSeriesCourseCountWithManager(courseData.seriesId, manager);
                return savedCourse;
            });
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException || error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`创建课程失败: ${error.message}`);
        }
    }
    async updateSeriesCourseCount(seriesId) {
        const courseCount = await this.courseRepository.count({ where: { seriesId } });
        await this.courseSeriesRepository.update(seriesId, { totalCourses: courseCount });
    }
    async updateSeriesCourseCountWithManager(seriesId, manager) {
        const courseCount = await manager.count(course_entity_1.Course, { where: { seriesId } });
        await manager.update(course_series_entity_1.CourseSeries, seriesId, { totalCourses: courseCount });
    }
    async setCourseSettings(courseId, settingsData, userId) {
        const course = await this.courseRepository.findOne({ where: { id: courseId } });
        if (!course) {
            throw new common_1.NotFoundException(`课程ID为${courseId}的课程不存在`);
        }
        const series = await this.findCourseSeriesById(course.seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限设置此课程配置');
        }
        let settings = await this.courseSettingsRepository.findOne({ where: { courseId } });
        if (!settings) {
            settings = this.courseSettingsRepository.create({
                courseId,
                templateId: settingsData.templateId || null,
                requiredPoints: settingsData.requiredPoints || 0,
                autoCreateTasks: settingsData.autoCreateTasks !== undefined ? settingsData.autoCreateTasks : 0
            });
        }
        else {
            if (settingsData.templateId !== undefined) {
                settings.templateId = settingsData.templateId;
            }
            if (settingsData.requiredPoints !== undefined) {
                settings.requiredPoints = settingsData.requiredPoints;
            }
            if (settingsData.autoCreateTasks !== undefined) {
                settings.autoCreateTasks = settingsData.autoCreateTasks;
            }
        }
        try {
            await this.courseSettingsRepository.save(settings);
            return {
                success: true,
                message: '课程配置设置成功',
                settings
            };
        }
        catch (error) {
            if (error.message && error.message.includes('foreign key constraint fails')) {
                throw new common_1.BadRequestException('设置失败：指定的模板ID不存在，请输入有效的模板ID');
            }
            throw error;
        }
    }
    async addTaskTemplate(courseId, templateData, userId) {
        const course = await this.courseRepository.findOne({ where: { id: courseId } });
        if (!course) {
            throw new common_1.NotFoundException(`课程ID为${courseId}的课程不存在`);
        }
        const series = await this.findCourseSeriesById(course.seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限为此课程添加任务模板');
        }
        if (!templateData.taskName || templateData.taskName.trim() === '') {
            throw new common_1.BadRequestException('任务名称不能为空');
        }
        const newTemplate = this.taskTemplateRepository.create({
            courseId,
            taskName: templateData.taskName,
            taskDescription: templateData.taskDescription,
            durationDays: templateData.durationDays || 7,
            attachments: templateData.attachments || [],
            workIdsStr: templateData.workIdsStr || '',
            selfAssessmentItems: templateData.selfAssessmentItems || [],
            status: 1
        });
        return await this.taskTemplateRepository.save(newTemplate);
    }
    async findCourseById(id, userId) {
        const course = await this.courseRepository.findOne({ where: { id } });
        if (!course) {
            throw new common_1.NotFoundException(`课程ID为${id}的课程不存在`);
        }
        if (userId) {
            const series = await this.findCourseSeriesById(course.seriesId);
            if (series.creatorId !== userId) {
                throw new common_1.ForbiddenException('无权限查看此课程');
            }
        }
        return course;
    }
    async updateCourse(id, updateData, userId) {
        const course = await this.findCourseById(id);
        const series = await this.findCourseSeriesById(course.seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限更新此课程');
        }
        delete updateData.id;
        delete updateData.createdAt;
        delete updateData.updatedAt;
        delete updateData.creatorId;
        delete updateData.seriesId;
        Object.assign(course, updateData);
        return await this.courseRepository.save(course);
    }
    async removeCourse(id, userId) {
        const course = await this.findCourseById(id);
        const series = await this.findCourseSeriesById(course.seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限删除此课程');
        }
        return await this.courseRepository.manager.transaction(async (manager) => {
            await manager.remove(course_entity_1.Course, course);
            await this.updateSeriesCourseCountWithManager(course.seriesId, manager);
            return {
                success: true,
                message: `ID为${id}的课程已成功删除`
            };
        });
    }
    async updateCourseOrders(seriesId, courseOrders, userId) {
        const series = await this.findCourseSeriesById(seriesId);
        if (series.creatorId !== userId) {
            throw new common_1.ForbiddenException('无权限调整此系列课程排序');
        }
        const courseIds = courseOrders.map(item => item.courseId);
        const courses = await this.courseRepository.find({
            where: { id: (0, typeorm_2.In)(courseIds), seriesId }
        });
        if (courses.length !== courseIds.length) {
            throw new common_1.BadRequestException('部分课程ID不存在或不属于该系列');
        }
        await this.courseRepository.manager.transaction(async (manager) => {
            for (const order of courseOrders) {
                await manager.update(course_entity_1.Course, { id: order.courseId }, { orderIndex: order.orderIndex });
            }
        });
        return {
            success: true,
            message: `系列${seriesId}的课程排序已成功更新`
        };
    }
};
exports.ManagementService = ManagementService;
exports.ManagementService = ManagementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(course_series_entity_1.CourseSeries)),
    __param(1, (0, typeorm_1.InjectRepository)(course_entity_1.Course)),
    __param(2, (0, typeorm_1.InjectRepository)(task_template_entity_1.TaskTemplate)),
    __param(3, (0, typeorm_1.InjectRepository)(course_settings_entity_1.CourseSettings)),
    __param(4, (0, typeorm_1.InjectRepository)(course_series_tag_entity_1.CourseSeriesTag)),
    __param(5, (0, typeorm_1.InjectRepository)(course_tag_entity_1.CourseTag)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], ManagementService);
//# sourceMappingURL=management.service.js.map