{"version": 3, "file": "task-template.dto.js", "sourceRoot": "", "sources": ["../../../../src/course/management/dto/task-template.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAAmE;AACnE,qDAAgG;AAChG,yDAAyC;AAEzC,MAAM,aAAa;IAIjB,KAAK,CAAS;IAKd,GAAG,CAAS;IAKZ,IAAI,CAAS;CACd;AAXC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACrD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4CACC;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACzF,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;0CACD;AAKZ;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACrD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACA;AAGf,MAAM,qBAAqB;IAIzB,OAAO,CAAS;IAIhB,QAAQ,CAAS;CAClB;AALC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;IACpE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;sDACG;AAIhB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACjD,IAAA,0BAAQ,GAAE;;uDACM;AAGnB,MAAa,qBAAqB;IAOhC,QAAQ,CAAS;IAQjB,eAAe,CAAS;IAUxB,YAAY,CAAS;IAqBrB,WAAW,CAAmB;IAQ9B,UAAU,CAAU;IAuBpB,mBAAmB,CAA2B;CAC/C;AA9ED,sDA8EC;AAvEC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,aAAa;KACvB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;uDACnB;AAQjB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,mCAAmC;KAC7C,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;;8DACZ;AAUxB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,4BAAU,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;2DACjB;AAqBrB;IAnBC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,MAAM;QACnB,IAAI,EAAE,CAAC,aAAa,CAAC;QACrB,OAAO,EAAE;YACP;gBACE,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,yCAAyC;gBAChD,MAAM,EAAE,MAAM;aACf;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0CAA0C;gBACjD,MAAM,EAAE,UAAU;aACnB;SACF;KACF,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC;;0DACI;AAQ9B;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,aAAa;KACvB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;yDACO;AAuBpB;IArBC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,OAAO;QACpB,IAAI,EAAE,CAAC,qBAAqB,CAAC;QAC7B,OAAO,EAAE;YACP;gBACE,SAAS,EAAE,oBAAoB;gBAC/B,UAAU,EAAE,CAAC;aACd;YACD;gBACE,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE,CAAC;aACd;SACF;KACF,CAAC;IACD,IAAA,yBAAO,GAAE;IACT,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;;kEACY;AAGhD,MAAa,uBAAuB;IAKlC,EAAE,CAAS;IAMX,QAAQ,CAAS;IAMjB,QAAQ,CAAS;IAOjB,eAAe,CAAS;IAMxB,YAAY,CAAS;IAkBrB,WAAW,CAAQ;IAOnB,UAAU,CAAS;IAoBnB,mBAAmB,CAAQ;IAM3B,MAAM,CAAS;IAMf,gBAAgB,CAAS;IAMzB,oBAAoB,CAAS;IAO7B,mBAAmB,CAAS;IAM5B,SAAS,CAAO;IAMhB,SAAS,CAAO;CACjB;AAjHD,0DAiHC;AA5GC;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;KACX,CAAC;;mDACS;AAMX;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,EAAE;KACZ,CAAC;;yDACe;AAMjB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,aAAa;KACvB,CAAC;;yDACe;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,mCAAmC;KAC7C,CAAC;;gEACsB;AAMxB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;KACX,CAAC;;6DACmB;AAkBrB;IAhBC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE;YACP;gBACE,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,yCAAyC;gBAChD,MAAM,EAAE,MAAM;aACf;YACD;gBACE,OAAO,EAAE,MAAM;gBACf,KAAK,EAAE,0CAA0C;gBACjD,MAAM,EAAE,UAAU;aACnB;SACF;KACF,CAAC;;4DACiB;AAOnB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,aAAa;KACvB,CAAC;;2DACiB;AAoBnB;IAlBC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,OAAO;QACpB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE;YACP;gBACE,SAAS,EAAE,oBAAoB;gBAC/B,UAAU,EAAE,CAAC;aACd;YACD;gBACE,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE,CAAC;aACd;YACD;gBACE,SAAS,EAAE,sBAAsB;gBACjC,UAAU,EAAE,CAAC;aACd;SACF;KACF,CAAC;;oEACyB;AAM3B;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,CAAC;KACX,CAAC;;uDACa;AAMf;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,CAAC;KACX,CAAC;;iEACuB;AAMzB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,OAAO;QACpB,OAAO,EAAE,CAAC;KACX,CAAC;;qEAC2B;AAO7B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,MAAM;KAChB,CAAC;;oEAC0B;AAM5B;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,sBAAsB;KAChC,CAAC;8BACS,IAAI;0DAAC;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,sBAAsB;KAChC,CAAC;8BACS,IAAI;0DAAC"}