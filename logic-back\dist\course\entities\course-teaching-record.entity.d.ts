import { Course } from './course.entity';
export declare enum TeachingStatus {
    IN_PROGRESS = 0,
    SUCCESS = 1,
    FAILED = 2,
    PARTIAL_SUCCESS = 3
}
export declare enum TemplateAppliedStatus {
    NO = 0,
    YES = 1
}
export declare class TeachingStatusUtils {
    static getStatusLabel(status: TeachingStatus): string;
    static getTemplateAppliedLabel(applied: TemplateAppliedStatus): string;
    static isSuccess(status: TeachingStatus): boolean;
    static isFailed(status: TeachingStatus): boolean;
    static isInProgress(status: TeachingStatus): boolean;
}
export declare class CourseTeachingRecord {
    id: number;
    courseId: number;
    classId: number;
    teacherId: number;
    status: TeachingStatus;
    pointsAllocated: number;
    tasksCreated: number;
    templateApplied: TemplateAppliedStatus;
    errorMessage: string;
    executionDetails: Record<string, any>;
    lockAcquireTime: number;
    totalExecutionTime: number;
    createdDate: Date;
    createdAt: Date;
    updatedAt: Date;
    course: Course;
}
