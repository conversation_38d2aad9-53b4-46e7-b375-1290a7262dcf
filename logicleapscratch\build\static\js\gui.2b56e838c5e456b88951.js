window.addEventListener("error",(function(e){try{if(e.message.includes("ResizeObserver"))return e.stopImmediatePropagation(),!1}catch(e){console.log(e)}}),!0),function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.GUI=r():e.GUI=r()}(self,(()=>(()=>{var e,r,n,t,o,a={41199:(e,r,n)=>{"use strict";n.r(r);n(94092),n(28128),n(38978),n(50218);var t=n(96540),o=n(40961),a=n(11640),i=n(8993),d=n(75584),c=n(74866);const s=document.createElement("div");if(s.className=c.default.app,document.body.appendChild(s),"undefined"!=typeof window){const e=window.console.error;window.console.error=function(){for(var r=arguments.length,n=new Array(r),t=0;t<r;t++)n[t]=arguments[t];n[0]&&"string"==typeof n[0]&&n[0].includes("ResizeObserver")||e.apply(window.console,n)},window.addEventListener("error",(e=>{if(e.message&&e.message.includes("ResizeObserver"))return e.preventDefault(),e.stopPropagation(),!1}),!0),window.addEventListener("unhandledrejection",(e=>{if(e.reason&&e.reason.message&&e.reason.message.includes("ResizeObserver"))return e.preventDefault(),e.stopPropagation(),!1}),!0)}if("undefined"!=typeof window&&window.addEventListener("load",(()=>{setTimeout((()=>{if(console.log("🔧 LogicLeaping 内存监视器启动中..."),!window.performance||!window.performance.memory)return void console.warn("⚠️ 浏览器不支持内存监控，请使用 Chrome 或 Edge");console.log("✅ 浏览器支持内存监控");const e=setInterval((()=>{const e=window.performance.memory,r=Math.round(e.usedJSHeapSize/1024/1024*100)/100,n=Math.round(e.totalJSHeapSize/1024/1024*100)/100,t=Math.round(e.jsHeapSizeLimit/1024/1024*100)/100,o=Math.round(e.usedJSHeapSize/e.jsHeapSizeLimit*100);let a="🟢";o>=85?a="🔴":o>=70&&(a="🟡"),console.log("".concat(a," [").concat((new Date).toLocaleTimeString(),"] 💾 ").concat(r,"MB/").concat(t,"MB (").concat(o,"%) | 堆:").concat(n,"MB")),o>=85?console.warn("🚨 内存使用率过高！建议刷新页面"):o>=70&&console.warn("⚠️ 内存使用率较高，请注意")}),5e3);console.log("🚀 内存监控已启动！每5秒自动打印"),console.log("🛑 停止监控: clearInterval("+e+")"),window.memoryTimerId=e,window.stopMemoryMonitor=()=>{window.memoryTimerId&&(clearInterval(window.memoryTimerId),window.memoryTimerId=null,console.log("🛑 内存监控已停止"))}}),2e3)})),(0,d.default)())n(86877).default(s);else{i.default.setAppElement(s);const e=(0,a.default)(i.default,!0),r=()=>{};o.render(t.createElement(e,{onBack:r}),s)}},86877:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>m});var t=n(96540),o=n(40961),a=n(77108),i=n(11640),d=n(73125),c=n(41580),s=n(84133);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)({}).hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},l.apply(null,arguments)}const u=()=>{window.location="https://scratch.mit.edu"},f=()=>{(0,s.default)("User canceled telemetry modal")},p=()=>{(0,s.default)("User opted into telemetry")},h=()=>{(0,s.default)("User opted out of telemetry")},m=e=>{d.default.setAppElement(e);const r=(0,a.compose)(i.default,c.default)(class extends t.Component{constructor(e){super(e),this.state={loading:!0,resourcesLoaded:!1}}componentDidMount(){new PerformanceObserver((e=>{const r=e.getEntries(),n=r.length,t=r.filter((e=>e.duration>0)).length;if(window.baseLoadingComplete){const e=Math.min(95,60+Math.floor(t/Math.max(n,1)*35));window.updateLoaderProgress&&window.updateLoaderProgress(e),e>=95&&!this.state.resourcesLoaded&&this.setState({resourcesLoaded:!0})}})).observe({entryTypes:["resource"]}),setTimeout((()=>{this.state.resourcesLoaded||(this.setState({resourcesLoaded:!0}),window.updateLoaderProgress&&window.updateLoaderProgress(100))}),1e4)}componentDidUpdate(e,r){!r.resourcesLoaded&&this.state.resourcesLoaded&&(window.updateLoaderProgress&&window.updateLoaderProgress(100),this.setState({loading:!1}))}render(){return t.createElement(d.default,l({canEditTitle:!0,backpackVisible:!0,showComingSoon:!0,canSave:!1,onClickLogo:u,onTelemetryModalCancel:f,onTelemetryModalOptIn:p,onTelemetryModalOptOut:h},this.props))}}),n=window.location.href.match(/[?&]backpack_host=([^&]*)&?/),s=n?n[1]:null,m=window.location.href.match(/[?&]isScratchDesktop=([^&]+)/);let v;if(m)try{v=JSON.parse(m[1])}catch(e){v=m[1]}o.render(v?t.createElement(r,l({isScratchDesktop:!0,showTelemetryModal:!0},window.scratchConfig)):t.createElement(r,l({backpackHost:s},window.scratchConfig)),e)}},78339:(e,r,n)=>{var t=n(54991),o=n(76314)(t);o.push([e.id,"html,\nbody,\n.index_app_ZUgsL {\n    width: 100%; \n    height: 100%;\n    margin: 0;\n    min-width: 1024px;\n    min-height: 640px;\n    /* overflow: hidden; */  /* 注释掉这行，或改为 overflow: auto */\n}\n\n* { \n    box-sizing: border-box;\n    scrollbar-width: none; /* Firefox */\n    -ms-overflow-style: none; /* IE and Edge */\n}\n\n*::-webkit-scrollbar {\n    display: none; /* Chrome, Safari and Opera */\n}\n","",{version:3,sources:["webpack://./src/playground/index.css"],names:[],mappings:"AAAA;;;IAGI,WAAW;IACX,YAAY;IACZ,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,sBAAsB,GAAG,6BAA6B;AAC1D;;AAEA;IACI,sBAAsB;IACtB,qBAAqB,EAAE,YAAY;IACnC,wBAAwB,EAAE,gBAAgB;AAC9C;;AAEA;IACI,aAAa,EAAE,6BAA6B;AAChD",sourcesContent:["html,\nbody,\n.app {\n    width: 100%; \n    height: 100%;\n    margin: 0;\n    min-width: 1024px;\n    min-height: 640px;\n    /* overflow: hidden; */  /* 注释掉这行，或改为 overflow: auto */\n}\n\n* { \n    box-sizing: border-box;\n    scrollbar-width: none; /* Firefox */\n    -ms-overflow-style: none; /* IE and Edge */\n}\n\n*::-webkit-scrollbar {\n    display: none; /* Chrome, Safari and Opera */\n}\n"],sourceRoot:""}]),o.locals={app:"index_app_ZUgsL"},e.exports=o},74866:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>E});var t=n(85072),o=n.n(t),a=n(97825),i=n.n(a),d=n(77659),c=n.n(d),s=n(55056),l=n.n(s),u=n(10540),f=n.n(u),p=n(41113),h=n.n(p),m=n(78339),v=n.n(m),w={};for(const e in m)"default"!==e&&(w[e]=()=>m[e]);n.d(r,w);var b={};b.styleTagTransform=h(),b.setAttributes=l(),b.insert=c().bind(null,"head"),b.domAPI=i(),b.insertStyleElement=f();var y=o()(v(),b);if(!v().locals||e.hot.invalidate){var g=!v().locals,A=g?m:v().locals;e.hot.accept(78339,(r=>{m=n(78339),v=n.n(m),function(e,r,n){if(!e&&r||e&&!r)return!1;var t;for(t in e)if((!n||"default"!==t)&&e[t]!==r[t])return!1;for(t in r)if(!(n&&"default"===t||e[t]))return!1;return!0}(A,g?m:v().locals,g)?(A=g?m:v().locals,y(v())):e.hot.invalidate()}))}e.hot.dispose((function(){y()}));const E=v()&&v().locals?v().locals:void 0},45905:()=>{}},i={};function d(e){var r=i[e];if(void 0!==r)return r.exports;var n=i[e]={id:e,loaded:!1,exports:{}},t={id:e,module:n,factory:a[e],require:d};return d.i.forEach((function(e){e(t)})),n=t.module,t.factory.call(n.exports,n,n.exports,t.require),n.loaded=!0,n.exports}d.m=a,d.c=i,d.i=[],d.amdD=function(){throw new Error("define cannot be used indirect")},d.amdO={},e=[],d.O=(r,n,t,o)=>{if(!n){var a=1/0;for(l=0;l<e.length;l++){for(var[n,t,o]=e[l],i=!0,c=0;c<n.length;c++)(!1&o||a>=o)&&Object.keys(d.O).every((e=>d.O[e](n[c])))?n.splice(c--,1):(i=!1,o<a&&(a=o));if(i){e.splice(l--,1);var s=t();void 0!==s&&(r=s)}}return r}o=o||0;for(var l=e.length;l>0&&e[l-1][2]>o;l--)e[l]=e[l-1];e[l]=[n,t,o]},d.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return d.d(r,{a:r}),r},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,d.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var o=Object.create(null);d.r(o);var a={};r=r||[null,n({}),n([]),n(n)];for(var i=2&t&&e;"object"==typeof i&&!~r.indexOf(i);i=n(i))Object.getOwnPropertyNames(i).forEach((r=>a[r]=()=>e[r]));return a.default=()=>e,d.d(o,a),o},d.d=(e,r)=>{for(var n in r)d.o(r,n)&&!d.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},d.f={},d.e=e=>Promise.all(Object.keys(d.f).reduce(((r,n)=>(d.f[n](e,r),r)),[])),d.u=e=>"static/js/"+{29:"uk-steps",122:"ja-steps",149:"fr-steps",179:"tr-steps",365:"zh_TW-steps",367:"sw-steps",509:"es-steps",665:"zh_CN-steps",875:"am-steps",884:"ar-steps",904:"pt_BR-steps",950:"zu-steps"}[e]+"."+{29:"ad8cc0c1d099b36fb652",122:"c425b7c855b44887bd5a",149:"8dcb9cbc81509f82e9fb",179:"da27b180bbd46e0b39b0",365:"7a3b2aecb25816a500ef",367:"b52015c009cc4a3ad349",509:"e3a6b4f0b3a58302c68c",665:"2669dcf583e8f55a3632",875:"54abb2724ac1d567ad8a",884:"970da08a0bcd1f6a166f",904:"f7703429f8123efdb1b1",950:"38c123d1621f65d39f90"}[e]+".js",d.hu=e=>e+"."+d.h()+".hot-update.js",d.hmrF=()=>"gui."+d.h()+".hot-update.json",d.h=()=>"619309e5f863873ae05b",d.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),d.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),d.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t={},o="GUI:",d.l=(e,r,n,a)=>{if(t[e])t[e].push(r);else{var i,c;if(void 0!==n)for(var s=document.getElementsByTagName("script"),l=0;l<s.length;l++){var u=s[l];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==o+n){i=u;break}}i||(c=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,d.nc&&i.setAttribute("nonce",d.nc),i.setAttribute("data-webpack",o+n),i.src=e),t[e]=[r];var f=(r,n)=>{i.onerror=i.onload=null,clearTimeout(p);var o=t[e];if(delete t[e],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),r)return r(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),c&&document.head.appendChild(i)}},d.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},d.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e,r,n,t={},o=d.c,a=[],i=[],c="idle",s=0,l=[];function u(e){c=e;for(var r=[],n=0;n<i.length;n++)r[n]=i[n].call(null,e);return Promise.all(r).then((function(){}))}function f(){0==--s&&u("ready").then((function(){if(0===s){var e=l;l=[];for(var r=0;r<e.length;r++)e[r]()}}))}function p(e){if("idle"!==c)throw new Error("check() is only allowed in idle status");return u("check").then(d.hmrM).then((function(n){return n?u("prepare").then((function(){var t=[];return r=[],Promise.all(Object.keys(d.hmrC).reduce((function(e,o){return d.hmrC[o](n.c,n.r,n.m,e,r,t),e}),[])).then((function(){return r=function(){return e?m(e):u("ready").then((function(){return t}))},0===s?r():new Promise((function(e){l.push((function(){e(r())}))}));var r}))})):u(v()?"ready":"idle").then((function(){return null}))}))}function h(e){return"ready"!==c?Promise.resolve().then((function(){throw new Error("apply() is only allowed in ready status (state: "+c+")")})):m(e)}function m(e){e=e||{},v();var t=r.map((function(r){return r(e)}));r=void 0;var o=t.map((function(e){return e.error})).filter(Boolean);if(o.length>0)return u("abort").then((function(){throw o[0]}));var a=u("dispose");t.forEach((function(e){e.dispose&&e.dispose()}));var i,d=u("apply"),c=function(e){i||(i=e)},s=[];return t.forEach((function(e){if(e.apply){var r=e.apply(c);if(r)for(var n=0;n<r.length;n++)s.push(r[n])}})),Promise.all([a,d]).then((function(){return i?u("fail").then((function(){throw i})):n?m(e).then((function(e){return s.forEach((function(r){e.indexOf(r)<0&&e.push(r)})),e})):u("idle").then((function(){return s}))}))}function v(){if(n)return r||(r=[]),Object.keys(d.hmrI).forEach((function(e){n.forEach((function(n){d.hmrI[e](n,r)}))})),n=void 0,!0}d.hmrD=t,d.i.push((function(l){var m,v,w,b,y=l.module,g=function(r,n){var t=o[n];if(!t)return r;var i=function(i){if(t.hot.active){if(o[i]){var d=o[i].parents;-1===d.indexOf(n)&&d.push(n)}else a=[n],e=i;-1===t.children.indexOf(i)&&t.children.push(i)}else console.warn("[HMR] unexpected require("+i+") from disposed module "+n),a=[];return r(i)},d=function(e){return{configurable:!0,enumerable:!0,get:function(){return r[e]},set:function(n){r[e]=n}}};for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&"e"!==l&&Object.defineProperty(i,l,d(l));return i.e=function(e,n){return function(e){switch(c){case"ready":u("prepare");case"prepare":return s++,e.then(f,f),e;default:return e}}(r.e(e,n))},i}(l.require,l.id);y.hot=(m=l.id,v=y,b={_acceptedDependencies:{},_acceptedErrorHandlers:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_selfInvalidated:!1,_disposeHandlers:[],_main:w=e!==m,_requireSelf:function(){a=v.parents.slice(),e=w?void 0:m,d(m)},active:!0,accept:function(e,r,n){if(void 0===e)b._selfAccepted=!0;else if("function"==typeof e)b._selfAccepted=e;else if("object"==typeof e&&null!==e)for(var t=0;t<e.length;t++)b._acceptedDependencies[e[t]]=r||function(){},b._acceptedErrorHandlers[e[t]]=n;else b._acceptedDependencies[e]=r||function(){},b._acceptedErrorHandlers[e]=n},decline:function(e){if(void 0===e)b._selfDeclined=!0;else if("object"==typeof e&&null!==e)for(var r=0;r<e.length;r++)b._declinedDependencies[e[r]]=!0;else b._declinedDependencies[e]=!0},dispose:function(e){b._disposeHandlers.push(e)},addDisposeHandler:function(e){b._disposeHandlers.push(e)},removeDisposeHandler:function(e){var r=b._disposeHandlers.indexOf(e);r>=0&&b._disposeHandlers.splice(r,1)},invalidate:function(){switch(this._selfInvalidated=!0,c){case"idle":r=[],Object.keys(d.hmrI).forEach((function(e){d.hmrI[e](m,r)})),u("ready");break;case"ready":Object.keys(d.hmrI).forEach((function(e){d.hmrI[e](m,r)}));break;case"prepare":case"check":case"dispose":case"apply":(n=n||[]).push(m)}},check:p,apply:h,status:function(e){if(!e)return c;i.push(e)},addStatusHandler:function(e){i.push(e)},removeStatusHandler:function(e){var r=i.indexOf(e);r>=0&&i.splice(r,1)},data:t[m]},e=void 0,b),y.parents=a,y.children=[],a=[],l.require=g})),d.hmrC={},d.hmrI={}})(),d.p="/logicleap/",(()=>{var e,r=d.hmrS_jsonp=d.hmrS_jsonp||{568:0};d.f.j=(e,n)=>{var t=d.o(r,e)?r[e]:void 0;if(0!==t)if(t)n.push(t[2]);else{var o=new Promise(((n,o)=>t=r[e]=[n,o]));n.push(t[2]=o);var a=d.p+d.u(e),i=new Error;d.l(a,(n=>{if(d.o(r,e)&&(0!==(t=r[e])&&(r[e]=void 0),t)){var o=n&&("load"===n.type?"missing":n.type),a=n&&n.target&&n.target.src;i.message="Loading chunk "+e+" failed.\n("+o+": "+a+")",i.name="ChunkLoadError",i.type=o,i.request=a,t[1](i)}}),"chunk-"+e,e)}};var n,t,o,a,i={};function c(r,n){return e=n,new Promise(((e,n)=>{i[r]=e;var t=d.p+d.hu(r),o=new Error;d.l(t,(e=>{if(i[r]){i[r]=void 0;var t=e&&("load"===e.type?"missing":e.type),a=e&&e.target&&e.target.src;o.message="Loading hot update chunk "+r+" failed.\n("+t+": "+a+")",o.name="ChunkLoadError",o.type=t,o.request=a,n(o)}}))}))}function s(e){function i(e){for(var r=[e],n={},t=r.map((function(e){return{chain:[e],id:e}}));t.length>0;){var o=t.pop(),a=o.id,i=o.chain,s=d.c[a];if(s&&(!s.hot._selfAccepted||s.hot._selfInvalidated)){if(s.hot._selfDeclined)return{type:"self-declined",chain:i,moduleId:a};if(s.hot._main)return{type:"unaccepted",chain:i,moduleId:a};for(var l=0;l<s.parents.length;l++){var u=s.parents[l],f=d.c[u];if(f){if(f.hot._declinedDependencies[a])return{type:"declined",chain:i.concat([u]),moduleId:a,parentId:u};-1===r.indexOf(u)&&(f.hot._acceptedDependencies[a]?(n[u]||(n[u]=[]),c(n[u],[a])):(delete n[u],r.push(u),t.push({chain:i.concat([u]),id:u})))}}}}return{type:"accepted",moduleId:e,outdatedModules:r,outdatedDependencies:n}}function c(e,r){for(var n=0;n<r.length;n++){var t=r[n];-1===e.indexOf(t)&&e.push(t)}}d.f&&delete d.f.jsonpHmr,n=void 0;var s={},l=[],u={},f=function(e){console.warn("[HMR] unexpected require("+e.id+") to disposed module")};for(var p in t)if(d.o(t,p)){var h=t[p],m=h?i(p):{type:"disposed",moduleId:p},v=!1,w=!1,b=!1,y="";switch(m.chain&&(y="\nUpdate propagation: "+m.chain.join(" -> ")),m.type){case"self-declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of self decline: "+m.moduleId+y));break;case"declined":e.onDeclined&&e.onDeclined(m),e.ignoreDeclined||(v=new Error("Aborted because of declined dependency: "+m.moduleId+" in "+m.parentId+y));break;case"unaccepted":e.onUnaccepted&&e.onUnaccepted(m),e.ignoreUnaccepted||(v=new Error("Aborted because "+p+" is not accepted"+y));break;case"accepted":e.onAccepted&&e.onAccepted(m),w=!0;break;case"disposed":e.onDisposed&&e.onDisposed(m),b=!0;break;default:throw new Error("Unexception type "+m.type)}if(v)return{error:v};if(w)for(p in u[p]=h,c(l,m.outdatedModules),m.outdatedDependencies)d.o(m.outdatedDependencies,p)&&(s[p]||(s[p]=[]),c(s[p],m.outdatedDependencies[p]));b&&(c(l,[m.moduleId]),u[p]=f)}t=void 0;for(var g,A=[],E=0;E<l.length;E++){var I=l[E],O=d.c[I];O&&(O.hot._selfAccepted||O.hot._main)&&u[I]!==f&&!O.hot._selfInvalidated&&A.push({module:I,require:O.hot._requireSelf,errorHandler:O.hot._selfAccepted})}return{dispose:function(){var e;o.forEach((function(e){delete r[e]})),o=void 0;for(var n,t=l.slice();t.length>0;){var a=t.pop(),i=d.c[a];if(i){var c={},u=i.hot._disposeHandlers;for(E=0;E<u.length;E++)u[E].call(null,c);for(d.hmrD[a]=c,i.hot.active=!1,delete d.c[a],delete s[a],E=0;E<i.children.length;E++){var f=d.c[i.children[E]];f&&((e=f.parents.indexOf(a))>=0&&f.parents.splice(e,1))}}}for(var p in s)if(d.o(s,p)&&(i=d.c[p]))for(g=s[p],E=0;E<g.length;E++)n=g[E],(e=i.children.indexOf(n))>=0&&i.children.splice(e,1)},apply:function(r){for(var n in u)d.o(u,n)&&(d.m[n]=u[n]);for(var t=0;t<a.length;t++)a[t](d);for(var o in s)if(d.o(s,o)){var i=d.c[o];if(i){g=s[o];for(var c=[],f=[],p=[],h=0;h<g.length;h++){var m=g[h],v=i.hot._acceptedDependencies[m],w=i.hot._acceptedErrorHandlers[m];if(v){if(-1!==c.indexOf(v))continue;c.push(v),f.push(w),p.push(m)}}for(var b=0;b<c.length;b++)try{c[b].call(null,g)}catch(n){if("function"==typeof f[b])try{f[b](n,{moduleId:o,dependencyId:p[b]})}catch(t){e.onErrored&&e.onErrored({type:"accept-error-handler-errored",moduleId:o,dependencyId:p[b],error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"accept-errored",moduleId:o,dependencyId:p[b],error:n}),e.ignoreErrored||r(n)}}}for(var y=0;y<A.length;y++){var E=A[y],I=E.module;try{E.require(I)}catch(n){if("function"==typeof E.errorHandler)try{E.errorHandler(n,{moduleId:I,module:d.c[I]})}catch(t){e.onErrored&&e.onErrored({type:"self-accept-error-handler-errored",moduleId:I,error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"self-accept-errored",moduleId:I,error:n}),e.ignoreErrored||r(n)}}return l}}}self.webpackHotUpdateGUI=(r,n,o)=>{for(var c in n)d.o(n,c)&&(t[c]=n[c],e&&e.push(c));o&&a.push(o),i[r]&&(i[r](),i[r]=void 0)},d.hmrI.jsonp=function(e,r){t||(t={},a=[],o=[],r.push(s)),d.o(t,e)||(t[e]=d.m[e])},d.hmrC.jsonp=function(e,i,l,u,f,p){f.push(s),n={},o=i,t=l.reduce((function(e,r){return e[r]=!1,e}),{}),a=[],e.forEach((function(e){d.o(r,e)&&void 0!==r[e]?(u.push(c(e,p)),n[e]=!0):n[e]=!1})),d.f&&(d.f.jsonpHmr=function(e,r){n&&d.o(n,e)&&!n[e]&&(r.push(c(e)),n[e]=!0)})},d.hmrM=()=>{if("undefined"==typeof fetch)throw new Error("No browser support: need fetch API");return fetch(d.p+d.hmrF()).then((e=>{if(404!==e.status){if(!e.ok)throw new Error("Failed to fetch update manifest "+e.statusText);return e.json()}}))},d.O.j=e=>0===r[e];var l=(e,n)=>{var t,o,[a,i,c]=n,s=0;if(a.some((e=>0!==r[e]))){for(t in i)d.o(i,t)&&(d.m[t]=i[t]);if(c)var l=c(d)}for(e&&e(n);s<a.length;s++)o=a[s],d.o(r,o)&&r[o]&&r[o][0](),r[o]=0;return d.O(l)},u=self.webpackChunkGUI=self.webpackChunkGUI||[];u.forEach(l.bind(null,0)),u.push=l.bind(null,u.push.bind(u))})(),d.nc=void 0,d.O(void 0,[340,644,96,223],(()=>d(42440))),d.O(void 0,[340,644,96,223],(()=>d(63943)));var c=d.O(void 0,[340,644,96,223],(()=>d(41199)));return c=d.O(c)})()));