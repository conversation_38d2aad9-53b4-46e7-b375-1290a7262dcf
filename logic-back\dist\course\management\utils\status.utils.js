"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusUtils = void 0;
class StatusUtils {
    static getCourseStatusLabel(status) {
        switch (status) {
            case 0: return '草稿';
            case 1: return '已发布';
            case 2: return '已归档';
            default: return '未知';
        }
    }
    static getCategoryLabel(category) {
        switch (category) {
            case 0: return '官方';
            case 1: return '社区';
            default: return '未知';
        }
    }
}
exports.StatusUtils = StatusUtils;
//# sourceMappingURL=status.utils.js.map