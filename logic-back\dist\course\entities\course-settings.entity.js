"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSettings = void 0;
const typeorm_1 = require("typeorm");
const course_entity_1 = require("./course.entity");
let CourseSettings = class CourseSettings {
    id;
    courseId;
    templateId;
    requiredPoints;
    autoCreateTasks;
    createdAt;
    updatedAt;
    course;
};
exports.CourseSettings = CourseSettings;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '设置ID' }),
    __metadata("design:type", Number)
], CourseSettings.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_course', { unique: true }),
    (0, typeorm_1.Column)({ type: 'int', name: 'course_id', comment: '对应课程ID' }),
    __metadata("design:type", Number)
], CourseSettings.prototype, "courseId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_template'),
    (0, typeorm_1.Column)({ type: 'int', name: 'template_id', nullable: true, comment: '积木模板ID' }),
    __metadata("design:type", Object)
], CourseSettings.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'required_points', default: 0, comment: '所需积分' }),
    __metadata("design:type", Number)
], CourseSettings.prototype, "requiredPoints", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_auto_tasks'),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'auto_create_tasks', default: 0, comment: '是否自动创建任务：0=否，1=是' }),
    __metadata("design:type", Number)
], CourseSettings.prototype, "autoCreateTasks", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], CourseSettings.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], CourseSettings.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => course_entity_1.Course, course => course.id),
    (0, typeorm_1.JoinColumn)({ name: 'course_id' }),
    __metadata("design:type", course_entity_1.Course)
], CourseSettings.prototype, "course", void 0);
exports.CourseSettings = CourseSettings = __decorate([
    (0, typeorm_1.Entity)('course_settings')
], CourseSettings);
//# sourceMappingURL=course-settings.entity.js.map