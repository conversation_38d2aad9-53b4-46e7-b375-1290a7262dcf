{"app\\workbench\\components\\ClassDetail.tsx -> logic-common/dist/components/Notification": {"id": "app\\workbench\\components\\ClassDetail.tsx -> logic-common/dist/components/Notification", "files": []}, "app\\workbench\\utils\\classProjectsUtils.ts -> @/lib/utils/view-work-modal": {"id": "app\\workbench\\utils\\classProjectsUtils.ts -> @/lib/utils/view-work-modal", "files": []}, "app\\workbench\\utils\\classUtils.ts -> @/lib/api/class": {"id": "app\\workbench\\utils\\classUtils.ts -> @/lib/api/class", "files": []}, "app\\workbench\\utils\\classUtils.ts -> @/lib/api/points": {"id": "app\\workbench\\utils\\classUtils.ts -> @/lib/api/points", "files": []}, "app\\workbench\\utils\\classUtils.ts -> @/lib/api/school": {"id": "app\\workbench\\utils\\classUtils.ts -> @/lib/api/school", "files": []}, "app\\workbench\\utils\\pointsUtils.ts -> ../../../lib/api/key_package": {"id": "app\\workbench\\utils\\pointsUtils.ts -> ../../../lib/api/key_package", "files": []}, "app\\workbench\\utils\\pointsUtils.ts -> xlsx": {"id": "app\\workbench\\utils\\pointsUtils.ts -> xlsx", "files": ["static/chunks/_app-pages-browser_node_modules_xlsx_xlsx_mjs.js"]}}