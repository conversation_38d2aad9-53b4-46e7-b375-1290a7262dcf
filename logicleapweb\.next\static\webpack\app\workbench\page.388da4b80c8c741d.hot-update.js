"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx":
/*!*************************************************************!*\
  !*** ./app/workbench/components/TemplateSelectionModal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _TemplatePickerModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplatePickerModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_task__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 导入API\n\n\n\n// 导入工具函数\n\nconst TemplateSelectionModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, actionType, selectedSchool, selectedClass } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredOption, setHoveredOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [focusedInput, setFocusedInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // 跟踪哪个输入框有焦点\n    // 输入框引用\n    const assignInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const distributeInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 移除教师能量相关状态，因为不需要检查教师能量池\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 学生相关状态\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentPointsMap, setStudentPointsMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [loadingStudentPoints, setLoadingStudentPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 存储模态框数据的状态\n    const [modalData, setModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"none\",\n        assignEnergyAmount: \"\",\n        distributeEnergyAmount: \"\",\n        selectedTemplate: null,\n        selectedStudents: []\n    });\n    // 输入验证错误状态\n    const [inputErrors, setInputErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        assignEnergyError: \"\",\n        distributeEnergyError: \"\"\n    });\n    // 当前步骤状态\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"template\");\n    // 发布任务相关状态\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        duration: \"1小时\",\n        startTime: \"\",\n        endTime: \"\"\n    });\n    // 持续时间选择器状态\n    const [showDurationSelector, setShowDurationSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 点击外部关闭持续时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (showDurationSelector && !target.closest(\".duration-field-container\")) {\n                setShowDurationSelector(false);\n            }\n        };\n        if (showDurationSelector) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationSelector\n    ]);\n    // 持续时间选择函数\n    const handleDurationSelect = (duration)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                duration\n            }));\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布任务加载状态\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const pageSize = 10;\n    // 阻止背景页面滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            // 保存原始的 overflow 样式\n            const originalStyle = window.getComputedStyle(document.body).overflow;\n            // 阻止背景滚动\n            document.body.style.overflow = \"hidden\";\n            return ()=>{\n                // 恢复原始样式\n                document.body.style.overflow = originalStyle;\n            };\n        }\n    }, [\n        isOpen\n    ]);\n    // 获取作品列表（分页懒加载）\n    const fetchWorksData = async function() {\n        let pageNum = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, isLoadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const currentState = {\n            works,\n            selectedWorkIds,\n            page,\n            hasMore,\n            loadingWorks,\n            loadingMore\n        };\n        const setState = (newState)=>{\n            if (newState.works !== undefined) setWorks(newState.works);\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n            if (newState.page !== undefined) setPage(newState.page);\n            if (newState.hasMore !== undefined) setHasMore(newState.hasMore);\n            if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);\n            if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);\n        };\n        await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchWorks)(pageNum, pageSize, isLoadMore, currentState, setState);\n    };\n    // 加载更多作品\n    const loadMoreWorksData = ()=>{\n        if (!loadingMore && hasMore) {\n            const nextPage = page + 1;\n            fetchWorksData(nextPage, true);\n        }\n    };\n    // 选择作品（支持多选）\n    const handleSelectWorkData = (workId)=>{\n        const setState = (newState)=>{\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleSelectWork)(workId, selectedWorkIds, setState);\n    };\n    // 创建鼠标处理函数的包装器\n    const handleMouseDownWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseDown)(e, setDragState);\n    };\n    const handleMouseMoveWrapper = (e)=>{\n        const dragState = {\n            isDragging,\n            dragStart\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseMove)(e, dragState);\n    };\n    const handleMouseUpWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseUp)(e, setDragState);\n    };\n    const handleMouseLeaveWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseLeave)(e, setDragState);\n    };\n    // 当切换到发布任务步骤且选择资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStep === \"publish\" && activeTab === \"resources\" && mounted) {\n            // 重置作品状态\n            setWorks([]);\n            setPage(1);\n            setHasMore(true);\n            setSelectedWorkIds([]);\n            setLoadingMore(false);\n            fetchWorksData(1, false);\n        }\n    }, [\n        currentStep,\n        activeTab,\n        mounted\n    ]);\n    // 清空所有数据的函数\n    const clearAllData = ()=>{\n        // 重置步骤状态\n        setCurrentStep(\"template\");\n        // 重置模态框数据\n        setModalData({\n            selectedDistribution: \"\",\n            assignEnergyAmount: \"\",\n            distributeEnergyAmount: \"\",\n            selectedTemplate: null,\n            selectedStudents: []\n        });\n        // 重置错误状态\n        setInputErrors({\n            assignEnergyError: \"\",\n            distributeEnergyError: \"\"\n        });\n        // 重置任务数据\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            duration: \"1小时\",\n            startTime: \"\",\n            endTime: \"\"\n        });\n        // 重置其他状态\n        setActiveTab(\"task\");\n        setWorks([]);\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setHoveredOption(\"\");\n        setIsTemplatePickerOpen(false);\n        setIsBatchUseKeyPackageModalOpen(false);\n        setPage(1);\n        setHasMore(true);\n        setLoadingMore(false);\n        // 重置学生相关数据\n        setStudents([]);\n        setStudentPointsMap(new Map());\n    };\n    // 监听模态框关闭，清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            clearAllData();\n        }\n    }, [\n        isOpen\n    ]);\n    // 组件卸载时清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearAllData();\n        };\n    }, []);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 移除获取教师可分配能量的函数，因为不需要检查教师能量池\n    // 获取班级学生列表和能量信息（并行处理）\n    const fetchClassStudentsData = async ()=>{\n        if (!(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || !mounted) return;\n        setLoadingStudentPoints(true); // 提前设置能量加载状态\n        try {\n            const studentsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchClassStudentsWithNotification)(selectedClass.id, notification);\n            setStudents(studentsData);\n            // 立即并行获取学生能量信息，不等待学生列表完全处理完\n            if (studentsData.length > 0) {\n                // 不等待，立即开始获取能量信息\n                const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(studentsData.map((s)=>s.userId), notification);\n                setStudentPointsMap(pointsMap);\n            }\n            setLoadingStudentPoints(false);\n        } catch (error) {\n            console.error(\"获取学生列表失败:\", error);\n            setStudents([]);\n            setLoadingStudentPoints(false);\n        }\n    };\n    // 移除获取教师能量的 useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedClass) {\n            fetchClassStudentsData();\n            // 重置模态框数据\n            setModalData({\n                selectedDistribution: isBlocksOnlyMode ? \"none\" : \"none\",\n                assignEnergyAmount: \"\",\n                distributeEnergyAmount: \"\",\n                selectedTemplate: null,\n                selectedStudents: []\n            });\n            // 重置错误状态\n            setInputErrors({\n                assignEnergyError: \"\",\n                distributeEnergyError: \"\"\n            });\n            // 禁用body滚动\n            document.body.style.overflow = \"hidden\";\n        } else {\n            // 恢复body滚动\n            document.body.style.overflow = \"\";\n        }\n        // 清理函数：组件卸载时恢复滚动\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen,\n        selectedClass,\n        mounted\n    ]);\n    // 当学生列表加载完成后，自动选择所有学生\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (students.length > 0) {\n            setModalData((prev)=>({\n                    ...prev,\n                    selectedStudents: students.map((s)=>s.userId)\n                }));\n        }\n    }, [\n        students\n    ]);\n    const distributionOptions = [\n        {\n            id: \"none\",\n            label: \"不分配\",\n            description: \"保持原有设置\",\n            hasInput: false\n        },\n        {\n            id: \"assign\",\n            label: \"分配\",\n            description: \"分配给学生\",\n            hasInput: true\n        },\n        {\n            id: \"distribute\",\n            label: \"分配至\",\n            description: \"分配到指定位置\",\n            hasInput: true\n        }\n    ];\n    // 判断是否为纯积木分配模式（不涉及能量）\n    const isBlocksOnlyMode = actionType === \"分配积木\";\n    // 判断是否为纯能量分配模式（不涉及积木）\n    const isEnergyOnlyMode = actionType === \"分配能量\";\n    const handleDistributionSelect = (optionId)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedDistribution: optionId\n            }));\n        // 自动聚焦到对应的输入框\n        setTimeout(()=>{\n            if (optionId === \"assign\" && assignInputRef.current) {\n                assignInputRef.current.focus();\n            } else if (optionId === \"distribute\" && distributeInputRef.current) {\n                distributeInputRef.current.focus();\n            }\n        }, 100); // 延迟一点确保输入框已经渲染\n    };\n    const handleTemplateSelect = (template)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: template\n            }));\n    };\n    const handleCancelTemplate = ()=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: null\n            }));\n    };\n    const handleTemplatePickerOpen = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    const handleTemplatePickerClose = ()=>{\n        setIsTemplatePickerOpen(false);\n    };\n    // 处理批量兑换密钥模态框\n    const handleBatchUseKeyPackageModalOpen = ()=>{\n        setIsBatchUseKeyPackageModalOpen(true);\n    };\n    const handleBatchUseKeyPackageModalClose = ()=>{\n        setIsBatchUseKeyPackageModalOpen(false);\n    };\n    const handleBatchUseKeyPackageSuccess = async ()=>{\n        // 兑换成功后重新获取学生能量信息\n        if (students.length > 0) {\n            const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(students.map((s)=>s.userId), notification);\n            setStudentPointsMap(pointsMap);\n        }\n        notification.success(\"密钥兑换成功！\");\n    };\n    // 获取当前选中分配方式对应的能量数值\n    const getCurrentEnergyAmount = ()=>{\n        if (modalData.selectedDistribution === \"assign\") {\n            return modalData.assignEnergyAmount;\n        } else if (modalData.selectedDistribution === \"distribute\") {\n            return modalData.distributeEnergyAmount;\n        }\n        return \"\";\n    };\n    // 计算所有学生的最低可分配能量\n    const getMinAvailablePoints = ()=>{\n        if (modalData.selectedStudents.length === 0) return 0;\n        const selectedStudentPoints = modalData.selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n        return Math.min(...selectedStudentPoints);\n    };\n    // 获取当前分配方式的提示信息\n    const getEnergyDisplayInfo = ()=>{\n        if (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") {\n            return {\n                label: \"最低可分配能量\",\n                value: getMinAvailablePoints()\n            };\n        }\n        return {\n            label: \"\",\n            value: 0\n        };\n    };\n    const handleNext = ()=>{\n        console.log(\"选择的分配方式:\", modalData.selectedDistribution);\n        console.log(\"选择的模板:\", modalData.selectedTemplate);\n        // 在纯积木分配模式时，跳过能量验证\n        if (!isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\")) {\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            console.log(\"分配能量数量:\", currentEnergyAmount);\n            // 检查是否有输入错误\n            const errorKey = modalData.selectedDistribution === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n            if (inputErrors[errorKey]) {\n                notification.error(\"请修正输入错误后再继续\");\n                return;\n            }\n            // 检查能量数量是否有效\n            const energyAmountNum = Number(currentEnergyAmount);\n            if (!currentEnergyAmount || energyAmountNum <= 0) {\n                notification.error(\"请输入有效的分配能量数量\");\n                return;\n            }\n            if (modalData.selectedDistribution === \"assign\") {\n                // \"分配\"按钮：检查选中学生的可分配能量是否足够\n                const insufficientStudents = modalData.selectedStudents.filter((studentId)=>{\n                    const studentAvailablePoints = studentPointsMap.get(studentId);\n                    return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;\n                });\n                if (insufficientStudents.length > 0) {\n                    const insufficientNames = insufficientStudents.map((studentId)=>{\n                        const student = students.find((s)=>s.userId === studentId);\n                        const availablePoints = studentPointsMap.get(studentId) || 0;\n                        return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(可分配: \").concat(availablePoints, \")\");\n                    }).join(\"、\");\n                    notification.error(\"积分不足：以下学生的可分配能量不足 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                    return;\n                }\n            } else if (modalData.selectedDistribution === \"distribute\") {\n                // \"分配至\"按钮：检查需要补充能量的学生\n                const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                    const currentPoints = studentPointsMap.get(studentId) || 0;\n                    return currentPoints < energyAmountNum;\n                });\n                if (studentsNeedingEnergy.length > 0) {\n                    // 检查这些学生是否有足够的可分配能量来达到目标值\n                    const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = energyAmountNum - currentPoints;\n                        const studentAvailablePoints = studentPointsMap.get(studentId);\n                        return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                    });\n                    if (insufficientStudents.length > 0) {\n                        const insufficientNames = insufficientStudents.map((studentId)=>{\n                            const student = students.find((s)=>s.userId === studentId);\n                            const currentPoints = studentPointsMap.get(studentId) || 0;\n                            const neededPoints = energyAmountNum - currentPoints;\n                            const availablePoints = studentPointsMap.get(studentId) || 0;\n                            return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(需要: \").concat(neededPoints, \", 可分配: \").concat(availablePoints, \")\");\n                        }).join(\"、\");\n                        notification.error(\"积分不足：以下学生无法达到目标能量值 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                        return;\n                    }\n                }\n            }\n        }\n        // 根据操作类型决定下一步\n        if (actionType === \"发布任务\" || actionType === \"快速上课\") {\n            // 发布任务或快速上课：切换到发布任务步骤\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n        } else {\n            // 分配积木或分配能量：直接执行分配操作并关闭弹窗\n            console.log(\"\".concat(actionType, \"操作执行\"), {\n                selectedDistribution: modalData.selectedDistribution,\n                selectedTemplate: modalData.selectedTemplate,\n                selectedStudents: modalData.selectedStudents,\n                energyAmount: getCurrentEnergyAmount()\n            });\n            // 这里可以调用相应的API来执行分配操作\n            // TODO: 实现分配积木和分配能量的API调用\n            notification.success(\"\".concat(actionType, \"成功！\"));\n            clearAllData();\n            onClose();\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep === \"publish\") {\n            // 从发布任务步骤返回时重置持续时间\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\"\n                }));\n            setCurrentStep(\"template\");\n        } else {\n            onBack();\n        }\n    };\n    // 确认发布（与NewPublishTaskModal保持一致）\n    const handleConfirm = ()=>{\n        if (!taskData.taskName.trim()) {\n            notification.error(\"请输入任务名称\");\n            return;\n        }\n        const times = (0,_utils__WEBPACK_IMPORTED_MODULE_11__.getTaskTimes)(taskData.duration);\n        const finalTaskData = {\n            ...taskData,\n            startTime: times.startTime,\n            endTime: times.endTime,\n            selectedWorkIds,\n            attachments,\n            modalData\n        };\n        // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑\n        if (onConfirm) {\n            onConfirm(finalTaskData);\n        } else {\n            // 保留原有的发布逻辑作为后备\n            handlePublishTaskOk();\n        }\n    };\n    const handlePublishTaskOk = async ()=>{\n        // 防止重复点击\n        if (isPublishing) {\n            return;\n        }\n        try {\n            var _modalData_selectedTemplate;\n            setIsPublishing(true);\n            // 验证必填字段\n            if (!taskData.taskName.trim()) {\n                notification.error(\"请输入任务名称\");\n                setIsPublishing(false);\n                return;\n            }\n            console.log(\"发布任务:\", taskData);\n            console.log(\"选中的作品ID:\", selectedWorkIds);\n            console.log(\"模态框数据:\", modalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const user = userData ? JSON.parse(userData) : null;\n            const teacherId = user === null || user === void 0 ? void 0 : user.userId;\n            if (!teacherId) {\n                notification.error(\"未找到用户信息\");\n                setIsPublishing(false);\n                return;\n            }\n            // 处理时间\n            const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();\n            const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n            // 构建任务发布参数\n            const taskParams = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskType: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.TaskType.GRAPHIC,\n                priority: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.Priority.NORMAL,\n                startDate: startDate,\n                endDate: endDate,\n                taskContent: taskData.taskDescription || \"\",\n                attachments: attachments.map((file)=>file.name) || [],\n                isPublic: 1,\n                allowLateSubmission: false,\n                studentIds: modalData.selectedStudents,\n                classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                templateId: (_modalData_selectedTemplate = modalData.selectedTemplate) === null || _modalData_selectedTemplate === void 0 ? void 0 : _modalData_selectedTemplate.id,\n                workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim() !== \"\") || [] // 过滤空的自评项\n            };\n            console.log(\"任务发布参数:\", taskParams);\n            console.log(\"作品ID数组:\", taskParams.workIds);\n            // 准备并行请求数组\n            const requests = [];\n            // 1. 任务发布请求（必须执行）\n            requests.push(_lib_api_task__WEBPACK_IMPORTED_MODULE_9__[\"default\"].publishTask(taskParams));\n            // 2. 能量分配请求（如果需要）\n            let energyRequest = null;\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            if ((modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && currentEnergyAmount) {\n                const targetAmount = Number(currentEnergyAmount);\n                const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n                if (modalData.selectedDistribution === \"assign\") {\n                    // \"分配\"按钮：给每个学生分配固定数量的能量\n                    const studentExpiries = {};\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        studentExpiries[studentId] = defaultExpireTime;\n                    });\n                    energyRequest = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                        availablePoints: targetAmount,\n                        studentExpiries,\n                        remark: \"任务发布 - \".concat(taskData.taskName)\n                    });\n                    requests.push(energyRequest);\n                } else if (modalData.selectedDistribution === \"distribute\") {\n                    // \"分配至\"按钮：将学生能量补充到目标值\n                    const energyRequests = [];\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = targetAmount - currentPoints;\n                        // 只有当学生当前能量小于目标值时才分配\n                        if (neededPoints > 0) {\n                            const studentExpiries = {};\n                            studentExpiries[studentId] = defaultExpireTime;\n                            const request = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                                availablePoints: neededPoints,\n                                studentExpiries,\n                                remark: \"任务发布 - \".concat(taskData.taskName, \" (补充至\").concat(targetAmount, \")\")\n                            });\n                            energyRequests.push(request);\n                        }\n                    });\n                    // 将所有能量分配请求添加到主请求列表\n                    requests.push(...energyRequests);\n                }\n            }\n            // 3. 模板分配请求（如果需要）\n            let templateRequest = null;\n            if (modalData.selectedTemplate) {\n                const users = modalData.selectedStudents.map((studentId)=>({\n                        userId: studentId,\n                        roleId: 1,\n                        templateId: modalData.selectedTemplate.id,\n                        originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id\n                    }));\n                templateRequest = (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_10__.batchAddUserJoinRole)({\n                    users\n                });\n                requests.push(templateRequest);\n            }\n            // 并行执行所有请求\n            const results = await Promise.allSettled(requests);\n            // 处理任务发布结果\n            const taskResult = results[0];\n            if (taskResult.status === \"fulfilled\" && taskResult.value.data.code === 200) {\n                // 显示成功发布任务的提示\n                notification.success(\"\\uD83C\\uDF89 任务发布成功！学生可以开始学习了\");\n            } else {\n                const errorMsg = taskResult.status === \"fulfilled\" ? taskResult.value.data.message || \"任务发布失败\" : \"任务发布失败\";\n                notification.error(errorMsg);\n                setIsPublishing(false);\n                return; // 任务发布失败则直接返回\n            }\n            // 处理能量分配结果\n            let resultIndex = 1;\n            if (energyRequest) {\n                const energyResult = results[resultIndex];\n                if (energyResult.status === \"fulfilled\" && energyResult.value.data.code === 200) {\n                    notification.success(\"能量分配完成！\");\n                } else {\n                    console.error(\"能量分配失败:\", energyResult);\n                    notification.warning(\"能量分配失败\");\n                }\n                resultIndex++;\n            }\n            // 处理模板分配结果\n            if (templateRequest) {\n                const templateResult = results[resultIndex];\n                if (templateResult.status === \"fulfilled\" && templateResult.value.data.code === 200) {\n                    notification.success(\"模板分配完成！\");\n                } else {\n                    console.error(\"模板分配失败:\", templateResult);\n                    notification.warning(\"模板分配失败\");\n                }\n            }\n            // 延迟关闭弹窗，让用户能看到成功提示\n            setTimeout(()=>{\n                setIsPublishing(false);\n                clearAllData();\n                onClose();\n            }, 800);\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n            setIsPublishing(false);\n        }\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted || !isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        onWheel: (e)=>{\n            // 只阻止事件传播，依赖CSS控制滚动行为\n            e.stopPropagation();\n        },\n        onTouchMove: (e)=>{\n            // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: ()=>{\n                            clearAllData();\n                            onClose();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content template-selection-modal\",\n                        \"data-step\": currentStep,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step completed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"选择班级\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"template\" ? \"active\" : \"completed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: actionType === \"分配积木\" ? \"分配积木\" : actionType === \"分配能量\" ? \"分配能量\" : \"能量和积木\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    actionType === \"快速上课\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    actionType === \"发布任务\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content-body\",\n                                children: [\n                                    currentStep === \"template\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-title\",\n                                                    children: \"为学生分配能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: [\n                                                    !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"distribution-options\",\n                                                        children: distributionOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"distribution-card \".concat(modalData.selectedDistribution === option.id ? \"selected\" : \"\"),\n                                                                onClick: ()=>handleDistributionSelect(option.id),\n                                                                onMouseEnter: ()=>setHoveredOption(option.id),\n                                                                onMouseLeave: ()=>setHoveredOption(\"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"distribution-label\",\n                                                                        children: (()=>{\n                                                                            const currentAmount = option.id === \"assign\" ? modalData.assignEnergyAmount : option.id === \"distribute\" ? modalData.distributeEnergyAmount : \"\";\n                                                                            return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id ? \"\".concat(option.label, \" \").concat(currentAmount, \"能量\") : option.label;\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 884,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-container\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ref: option.id === \"assign\" ? assignInputRef : distributeInputRef,\n                                                                                type: \"number\",\n                                                                                className: \"energy-input\",\n                                                                                placeholder: option.id === \"assign\" ? \"输入能量\" : \"输入目标值\",\n                                                                                value: option.id === \"assign\" ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount,\n                                                                                min: \"1\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    const updateKey = option.id === \"assign\" ? \"assignEnergyAmount\" : \"distributeEnergyAmount\";\n                                                                                    const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                    // 清除之前的错误\n                                                                                    setInputErrors((prev)=>({\n                                                                                            ...prev,\n                                                                                            [errorKey]: \"\"\n                                                                                        }));\n                                                                                    // 允许空值或正整数\n                                                                                    if (value === \"\") {\n                                                                                        setModalData((prev)=>({\n                                                                                                ...prev,\n                                                                                                [updateKey]: value\n                                                                                            }));\n                                                                                    } else {\n                                                                                        const numValue = Number(value);\n                                                                                        if (Number.isInteger(numValue)) {\n                                                                                            if (numValue < 1) {\n                                                                                                // 设置错误提示\n                                                                                                setInputErrors((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [errorKey]: \"输入能量不能低于1\"\n                                                                                                    }));\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                            } else {\n                                                                                                // 有效输入\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                                // 输入数字时自动选中当前悬停的分配按钮\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        selectedDistribution: option.id\n                                                                                                    }));\n                                                                                            }\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    // 点击输入框时自动选中当前悬停的分配按钮\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onFocus: ()=>{\n                                                                                    setFocusedInput(option.id);\n                                                                                    // 聚焦时也自动选中分配选项\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    setFocusedInput(\"\");\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 895,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            (()=>{\n                                                                                const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                const errorMessage = inputErrors[errorKey];\n                                                                                return errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: \"#ef4444\",\n                                                                                        fontSize: \"12px\",\n                                                                                        marginTop: \"4px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: errorMessage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 948,\n                                                                                    columnNumber: 29\n                                                                                }, undefined);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, option.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 19\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && modalData.selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-available-energy\",\n                                                        children: loadingStudentPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#6b7280\",\n                                                                fontStyle: \"italic\"\n                                                            },\n                                                            children: \"⏳ 正在获取能量信息...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 21\n                                                        }, undefined) : (()=>{\n                                                            const displayInfo = getEnergyDisplayInfo();\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#64748b\",\n                                                                            fontSize: \"12px\"\n                                                                        },\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            displayInfo.label,\n                                                                            \": \",\n                                                                            displayInfo.value\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && getCurrentEnergyAmount() && !loadingStudentPoints && (()=>{\n                                                        const energyAmountNum = Number(getCurrentEnergyAmount());\n                                                        // 根据分配方式进行不同的验证\n                                                        let shouldShowError = false;\n                                                        let errorMessage = \"\";\n                                                        if (modalData.selectedDistribution === \"assign\") {\n                                                            const minAvailable = getMinAvailablePoints();\n                                                            if (energyAmountNum > minAvailable) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"可分配积分不足\";\n                                                            }\n                                                        } else if (modalData.selectedDistribution === \"distribute\") {\n                                                            // 对于\"分配至\"，检查是否有学生无法达到目标值\n                                                            const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                return currentPoints < energyAmountNum;\n                                                            });\n                                                            const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                const neededPoints = energyAmountNum - currentPoints;\n                                                                const studentAvailablePoints = studentPointsMap.get(studentId);\n                                                                return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                                                            });\n                                                            if (insufficientStudents.length > 0) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"部分学生积分不足以达到目标值\";\n                                                            }\n                                                        }\n                                                        if (shouldShowError) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: \"#fef2f2\",\n                                                                    border: \"1px solid #fecaca\",\n                                                                    borderRadius: \"8px\",\n                                                                    padding: \"12px 16px\",\n                                                                    marginTop: \"8px\",\n                                                                    marginBottom: \"12px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"space-between\",\n                                                                    gap: \"12px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: \"8px\",\n                                                                            flex: 1\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: \"500\"\n                                                                            },\n                                                                            children: [\n                                                                                \"⚠️ \",\n                                                                                errorMessage\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1041,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1035,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        style: {\n                                                                            background: \"#f97316\",\n                                                                            color: \"white\",\n                                                                            border: \"none\",\n                                                                            borderRadius: \"6px\",\n                                                                            padding: \"6px 12px\",\n                                                                            fontSize: \"12px\",\n                                                                            fontWeight: \"500\",\n                                                                            cursor: \"pointer\",\n                                                                            transition: \"all 0.2s ease\",\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.background = \"#ea580c\";\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.background = \"#f97316\";\n                                                                        },\n                                                                        onClick: ()=>{\n                                                                            handleBatchUseKeyPackageModalOpen();\n                                                                        },\n                                                                        children: \"兑换密钥\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1049,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1023,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        }\n                                                        return null;\n                                                    })(),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"section-title\",\n                                                        children: \"为学生分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-selection-area\",\n                                                        children: modalData.selectedTemplate ? // 已选择模板时显示模板信息\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-selected\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-icon\",\n                                                                    children: \"⭐\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1092,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-name\",\n                                                                            children: modalData.selectedTemplate.templateName || modalData.selectedTemplate.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1096,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-label\",\n                                                                            children: \"已选择模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1099,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"change-template-btn\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: \"更换\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"cancel-template-btn\",\n                                                                    onClick: handleCancelTemplate,\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1091,\n                                                            columnNumber: 21\n                                                        }, undefined) : // 未选择模板时显示选择选项\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-options\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-none selected\",\n                                                                    onClick: ()=>{},\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"不分配模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1123,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"保持原有设置\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1122,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1117,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-select\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"\\uD83E\\uDDE9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"选择模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1133,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"为用户提供积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1134,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1132,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1127,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1116,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : // 发布任务步骤的内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"publish-task-content\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"tab-switcher\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"task\"),\n                                                        children: \"任务信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"resources\"),\n                                                        children: \"资源与附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1154,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1147,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"task-info-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"任务名称\",\n                                                                value: taskData.taskName,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskName: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1165,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                className: \"form-textarea\",\n                                                                placeholder: \"任务描述\",\n                                                                value: taskData.taskDescription,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskDescription: e.target.value\n                                                                        })),\n                                                                rows: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1176,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"self-assessment-section\",\n                                                                children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"add-self-assessment-btn\",\n                                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                                ...prev,\n                                                                                selfAssessmentItems: [\n                                                                                    \"\"\n                                                                                ]\n                                                                            })),\n                                                                    children: \"添加自评项\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1188,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label\",\n                                                                            children: \"自评项\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1197,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"self-assessment-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        className: \"form-input\",\n                                                                                        placeholder: \"自评项 \".concat(index + 1),\n                                                                                        value: item,\n                                                                                        onChange: (e)=>{\n                                                                                            const newItems = [\n                                                                                                ...taskData.selfAssessmentItems\n                                                                                            ];\n                                                                                            newItems[index] = e.target.value;\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1200,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"remove-btn\",\n                                                                                        onClick: ()=>{\n                                                                                            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        },\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1211,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1199,\n                                                                                columnNumber: 31\n                                                                            }, undefined)),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"add-btn\",\n                                                                            onClick: ()=>setTaskData((prev)=>({\n                                                                                        ...prev,\n                                                                                        selfAssessmentItems: [\n                                                                                            ...prev.selfAssessmentItems,\n                                                                                            \"\"\n                                                                                        ]\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"14px\"\n                                                                                    },\n                                                                                    children: \"➕\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1228,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"添加自评项\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1223,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1186,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1185,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"time-settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"form-label\",\n                                                                        children: \"任务持续时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1239,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"duration-options\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1小时\"),\n                                                                                children: \"1小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1241,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"3小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"3小时\"),\n                                                                                children: \"3小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1247,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1天\"),\n                                                                                children: \"1天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1253,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"7天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"7天\"),\n                                                                                children: \"7天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1259,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1个月\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1个月\"),\n                                                                                children: \"1个月\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1265,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1240,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"duration-hint\",\n                                                                        children: \"任务将从创建时开始，持续所选时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1164,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"resources-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"works-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"选择作品\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1279,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"help-text\",\n                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative works-scroll-wrapper\",\n                                                                    onWheel: _utils__WEBPACK_IMPORTED_MODULE_11__.handleWheelScroll,\n                                                                    onMouseDown: handleMouseDownWrapper,\n                                                                    onMouseMove: handleMouseMoveWrapper,\n                                                                    onMouseUp: handleMouseUpWrapper,\n                                                                    onMouseLeave: handleMouseLeaveWrapper,\n                                                                    style: {\n                                                                        minHeight: \"200px\",\n                                                                        cursor: \"grab\",\n                                                                        userSelect: \"none\"\n                                                                    },\n                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"loading-container\",\n                                                                        style: {\n                                                                            minHeight: \"200px\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"loading-spinner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1299,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"加载中...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1300,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1295,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"works-horizontal-scroll\",\n                                                                        children: [\n                                                                            works.sort((a, b)=>{\n                                                                                const aSelected = selectedWorkIds.includes(a.id);\n                                                                                const bSelected = selectedWorkIds.includes(b.id);\n                                                                                // 已选中的排在前面\n                                                                                if (aSelected && !bSelected) return -1;\n                                                                                if (!aSelected && bSelected) return 1;\n                                                                                return 0;\n                                                                            }).map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                    onClick: ()=>handleSelectWorkData(work.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-preview\",\n                                                                                            children: [\n                                                                                                work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fixImageUrl)(work.coverImage || work.screenShotImage),\n                                                                                                    alt: work.title,\n                                                                                                    className: \"work-image\",\n                                                                                                    onError: (e)=>{\n                                                                                                        e.currentTarget.style.display = \"none\";\n                                                                                                        const nextElement = e.currentTarget.nextElementSibling;\n                                                                                                        if (nextElement) {\n                                                                                                            nextElement.style.display = \"flex\";\n                                                                                                        }\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1323,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined) : null,\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"work-placeholder\",\n                                                                                                    style: {\n                                                                                                        display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"24\",\n                                                                                                        height: \"24\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1338,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z\",\n                                                                                                                fill: \"currentColor\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1339,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M21 15L16 10L11 15H21Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\",\n                                                                                                                strokeLinejoin: \"round\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1340,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1337,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1336,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"selection-indicator \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"12\",\n                                                                                                        height: \"12\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M9 12L11 14L15 10\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                            lineNumber: 1346,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1345,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1344,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1321,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-info\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"work-title\",\n                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1352,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1351,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, work.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1315,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)),\n                                                                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"load-more-container\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"load-more-btn\",\n                                                                                    onClick: loadMoreWorksData,\n                                                                                    disabled: loadingMore,\n                                                                                    children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"loading-spinner-small\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1366,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载中...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1367,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载更多\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1371,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                width: \"16\",\n                                                                                                height: \"16\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                fill: \"none\",\n                                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M12 5V19M5 12L12 19L19 12\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    strokeWidth: \"2\",\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1373,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1372,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1359,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1358,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1303,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-text\",\n                                                                            children: \"作品列表\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1383,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1382,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1281,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"attachments-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"附件上传\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1390,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            multiple: true,\n                                                                            accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                                            onChange: (e)=>{\n                                                                                if (e.target.files) {\n                                                                                    const files = Array.from(e.target.files);\n                                                                                    const validFiles = [];\n                                                                                    const invalidFiles = [];\n                                                                                    // 支持的文件格式\n                                                                                    const allowedTypes = [\n                                                                                        \"image/jpeg\",\n                                                                                        \"image/jpg\",\n                                                                                        \"image/png\",\n                                                                                        \"image/gif\",\n                                                                                        \"application/pdf\",\n                                                                                        \"application/msword\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                                                                                        \"application/vnd.ms-excel\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                                                                                        \"application/vnd.ms-powerpoint\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                                                                                        \"text/plain\"\n                                                                                    ];\n                                                                                    // 文件扩展名检查（作为备用验证）\n                                                                                    const allowedExtensions = [\n                                                                                        \".jpg\",\n                                                                                        \".jpeg\",\n                                                                                        \".png\",\n                                                                                        \".gif\",\n                                                                                        \".pdf\",\n                                                                                        \".doc\",\n                                                                                        \".docx\",\n                                                                                        \".xls\",\n                                                                                        \".xlsx\",\n                                                                                        \".ppt\",\n                                                                                        \".pptx\",\n                                                                                        \".txt\"\n                                                                                    ];\n                                                                                    files.forEach((file)=>{\n                                                                                        var _file_name_split_pop;\n                                                                                        // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）\n                                                                                        if (file.size > 10 * 1024 * 1024) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：文件大小超过10MB\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        // 检查文件类型\n                                                                                        const fileExtension = \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n                                                                                        const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);\n                                                                                        if (!isValidType) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：不支持的文件格式\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        validFiles.push(file);\n                                                                                    });\n                                                                                    // 添加有效文件\n                                                                                    if (validFiles.length > 0) {\n                                                                                        setAttachments((prev)=>[\n                                                                                                ...prev,\n                                                                                                ...validFiles\n                                                                                            ]);\n                                                                                    }\n                                                                                    // 显示错误信息\n                                                                                    if (invalidFiles.length > 0) {\n                                                                                        alert(\"以下文件无法上传：\\n\".concat(invalidFiles.join(\"\\n\")));\n                                                                                    }\n                                                                                    // 重置input的value，确保可以重复选择相同文件\n                                                                                    e.target.value = \"\";\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                display: \"none\"\n                                                                            },\n                                                                            id: \"file-upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1392,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"file-upload\",\n                                                                            className: \"upload-btn\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"file-format-info\",\n                                                                            children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1454,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1391,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachments-list\",\n                                                                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"file-name\",\n                                                                                    children: file.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1462,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setAttachments((prev)=>prev.filter((_, i)=>i !== index)),\n                                                                                    className: \"remove-attachment-btn\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1463,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1461,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1459,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1389,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1162,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"prev-btn\",\n                                                onClick: handlePrevious,\n                                                children: \"上一步\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1482,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"next-btn \".concat(currentStep === \"template\" && (isEnergyOnlyMode ? modalData.selectedDistribution : isBlocksOnlyMode ? modalData.selectedTemplate : modalData.selectedTemplate || modalData.selectedDistribution) || currentStep === \"publish\" && taskData.taskName.trim() && !isPublishing ? \"enabled\" : \"disabled\", \" \").concat(isPublishing ? \"publishing\" : \"\"),\n                                                onClick: currentStep === \"template\" ? handleNext : handleConfirm,\n                                                disabled: currentStep === \"template\" ? isEnergyOnlyMode ? !modalData.selectedDistribution : isBlocksOnlyMode ? !modalData.selectedTemplate : !modalData.selectedTemplate && !modalData.selectedDistribution : !taskData.taskName.trim() || isPublishing,\n                                                children: currentStep === \"template\" ? actionType === \"发布任务\" || actionType === \"快速上课\" ? \"下一步\" : \"分配\" : isPublishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"publishing-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1513,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"发布中...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1512,\n                                                    columnNumber: 19\n                                                }, undefined) : \"开始上课\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1481,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 824,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: handleTemplatePickerClose,\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1525,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalOpen,\n                selectedStudentIds: modalData.selectedStudents,\n                students: students,\n                onClose: handleBatchUseKeyPackageModalClose,\n                onSuccess: handleBatchUseKeyPackageSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1532,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n        lineNumber: 813,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateSelectionModal, \"3fkWfUC8OKkJ1JgoGu0y/qNfEA0=\");\n_c = TemplateSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"TemplateSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9UZW1wbGF0ZVNlbGVjdGlvbk1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUUyRDtBQUNLO0FBQ1k7QUFDcEI7QUFDWTtBQUVqQztBQUNBO0FBQ0c7QUFFdEMsUUFBUTtBQUNxQztBQUNnQjtBQUNQO0FBRXRELFNBQVM7QUFnQlM7QUFjbEIsTUFBTXdCLHlCQUFnRTtRQUFDLEVBQ3JFQyxNQUFNLEVBQ05DLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxTQUFTLEVBQ1RDLFVBQVUsRUFDVkMsY0FBYyxFQUNkQyxhQUFhLEVBQ2Q7O0lBQ0MsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQyxlQUFlQyxpQkFBaUIsR0FBR2xDLCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ21DLGNBQWNDLGdCQUFnQixHQUFHcEMsK0NBQVFBLENBQVMsS0FBSyxhQUFhO0lBRTNFLFFBQVE7SUFDUixNQUFNcUMsaUJBQWlCbkMsNkNBQU1BLENBQW1CO0lBQ2hELE1BQU1vQyxxQkFBcUJwQyw2Q0FBTUEsQ0FBbUI7SUFDcEQsMEJBQTBCO0lBQzFCLE1BQU0sQ0FBQ3FDLHNCQUFzQkMsd0JBQXdCLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUVqRSxNQUFNLENBQUN5QywrQkFBK0JDLGlDQUFpQyxHQUFHMUMsK0NBQVFBLENBQUM7SUFFbkYsU0FBUztJQUNULE1BQU0sQ0FBQzJDLFVBQVVDLFlBQVksR0FBRzVDLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDNkMsa0JBQWtCQyxvQkFBb0IsR0FBRzlDLCtDQUFRQSxDQUFzQixJQUFJK0M7SUFDbEYsTUFBTSxDQUFDQyxzQkFBc0JDLHdCQUF3QixHQUFHakQsK0NBQVFBLENBQUM7SUFFakUsYUFBYTtJQUNiLE1BQU0sQ0FBQ2tELFdBQVdDLGFBQWEsR0FBR25ELCtDQUFRQSxDQUFDO1FBQ3pDb0Qsc0JBQXNCO1FBQ3RCQyxvQkFBb0I7UUFDcEJDLHdCQUF3QjtRQUN4QkMsa0JBQWtCO1FBQ2xCQyxrQkFBa0IsRUFBRTtJQUN0QjtJQUVBLFdBQVc7SUFDWCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzFELCtDQUFRQSxDQUFDO1FBQzdDMkQsbUJBQW1CO1FBQ25CQyx1QkFBdUI7SUFDekI7SUFFQSxTQUFTO0lBQ1QsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUc5RCwrQ0FBUUEsQ0FBeUI7SUFFdkUsV0FBVztJQUNYLE1BQU0sQ0FBQytELFVBQVVDLFlBQVksR0FBR2hFLCtDQUFRQSxDQUFDO1FBQ3ZDaUUsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLHFCQUFxQixFQUFFO1FBQ3ZCQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsU0FBUztJQUNYO0lBRUEsWUFBWTtJQUNaLE1BQU0sQ0FBQ0Msc0JBQXNCQyx3QkFBd0IsR0FBR3hFLCtDQUFRQSxDQUFDO0lBRWpFLGdCQUFnQjtJQUNoQkMsZ0RBQVNBLENBQUM7UUFDUixNQUFNd0UscUJBQXFCLENBQUNDO1lBQzFCLE1BQU1DLFNBQVNELE1BQU1DLE1BQU07WUFDM0IsSUFBSUosd0JBQXdCLENBQUNJLE9BQU9DLE9BQU8sQ0FBQyw4QkFBOEI7Z0JBQ3hFSix3QkFBd0I7WUFDMUI7UUFDRjtRQUVBLElBQUlELHNCQUFzQjtZQUN4Qk0sU0FBU0MsZ0JBQWdCLENBQUMsYUFBYUw7WUFDdkMsT0FBTyxJQUFNSSxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhTjtRQUN6RDtJQUNGLEdBQUc7UUFBQ0Y7S0FBcUI7SUFJekIsV0FBVztJQUNYLE1BQU1TLHVCQUF1QixDQUFDWjtRQUM1QkosWUFBWWlCLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRWI7WUFBUztJQUMzQztJQUtBLE1BQU0sQ0FBQ2MsV0FBV0MsYUFBYSxHQUFHbkYsK0NBQVFBLENBQXVCO0lBQ2pFLE1BQU0sQ0FBQ29GLE9BQU9DLFNBQVMsR0FBR3JGLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDc0YsaUJBQWlCQyxtQkFBbUIsR0FBR3ZGLCtDQUFRQSxDQUFXLEVBQUU7SUFDbkUsTUFBTSxDQUFDd0YsYUFBYUMsZUFBZSxHQUFHekYsK0NBQVFBLENBQVMsRUFBRTtJQUN6RCxNQUFNLENBQUMwRixjQUFjQyxnQkFBZ0IsR0FBRzNGLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQzRGLE1BQU1DLFFBQVEsR0FBRzdGLCtDQUFRQSxDQUFDO0lBQ2pDLE1BQU0sQ0FBQzhGLFNBQVNDLFdBQVcsR0FBRy9GLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2dHLGFBQWFDLGVBQWUsR0FBR2pHLCtDQUFRQSxDQUFDO0lBRS9DLFdBQVc7SUFDWCxNQUFNLENBQUNrRyxjQUFjQyxnQkFBZ0IsR0FBR25HLCtDQUFRQSxDQUFDO0lBRWpELFdBQVc7SUFDWCxNQUFNLENBQUNvRyxZQUFZQyxjQUFjLEdBQUdyRywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNzRyxXQUFXQyxhQUFhLEdBQUd2RywrQ0FBUUEsQ0FBQztRQUFFd0csR0FBRztRQUFHQyxZQUFZO0lBQUU7SUFDakUsTUFBTUMsV0FBVztJQUVqQixXQUFXO0lBQ1h6RyxnREFBU0EsQ0FBQztRQUNSLElBQUl1QixRQUFRO1lBQ1Ysb0JBQW9CO1lBQ3BCLE1BQU1tRixnQkFBZ0JDLE9BQU9DLGdCQUFnQixDQUFDaEMsU0FBU2lDLElBQUksRUFBRUMsUUFBUTtZQUNyRSxTQUFTO1lBQ1RsQyxTQUFTaUMsSUFBSSxDQUFDRSxLQUFLLENBQUNELFFBQVEsR0FBRztZQUUvQixPQUFPO2dCQUNMLFNBQVM7Z0JBQ1RsQyxTQUFTaUMsSUFBSSxDQUFDRSxLQUFLLENBQUNELFFBQVEsR0FBR0o7WUFDakM7UUFDRjtJQUNGLEdBQUc7UUFBQ25GO0tBQU87SUFJWCxnQkFBZ0I7SUFDaEIsTUFBTXlGLGlCQUFpQjtZQUFPQywyRUFBa0IsR0FBR0MsOEVBQXNCO1FBQ3ZFLE1BQU1DLGVBQTJCO1lBQy9CaEM7WUFDQUU7WUFDQU07WUFDQUU7WUFDQUo7WUFDQU07UUFDRjtRQUVBLE1BQU1xQixXQUFXLENBQUNDO1lBQ2hCLElBQUlBLFNBQVNsQyxLQUFLLEtBQUttQyxXQUFXbEMsU0FBU2lDLFNBQVNsQyxLQUFLO1lBQ3pELElBQUlrQyxTQUFTaEMsZUFBZSxLQUFLaUMsV0FBV2hDLG1CQUFtQitCLFNBQVNoQyxlQUFlO1lBQ3ZGLElBQUlnQyxTQUFTMUIsSUFBSSxLQUFLMkIsV0FBVzFCLFFBQVF5QixTQUFTMUIsSUFBSTtZQUN0RCxJQUFJMEIsU0FBU3hCLE9BQU8sS0FBS3lCLFdBQVd4QixXQUFXdUIsU0FBU3hCLE9BQU87WUFDL0QsSUFBSXdCLFNBQVM1QixZQUFZLEtBQUs2QixXQUFXNUIsZ0JBQWdCMkIsU0FBUzVCLFlBQVk7WUFDOUUsSUFBSTRCLFNBQVN0QixXQUFXLEtBQUt1QixXQUFXdEIsZUFBZXFCLFNBQVN0QixXQUFXO1FBQzdFO1FBRUEsTUFBTWpGLG1EQUFVQSxDQUFDbUcsU0FBU1IsVUFBVVMsWUFBWUMsY0FBY0M7SUFDaEU7SUFFQSxTQUFTO0lBQ1QsTUFBTUcsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ3hCLGVBQWVGLFNBQVM7WUFDM0IsTUFBTTJCLFdBQVc3QixPQUFPO1lBQ3hCcUIsZUFBZVEsVUFBVTtRQUMzQjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU1DLHVCQUF1QixDQUFDQztRQUM1QixNQUFNTixXQUFXLENBQUNDO1lBQ2hCLElBQUlBLFNBQVNoQyxlQUFlLEtBQUtpQyxXQUFXaEMsbUJBQW1CK0IsU0FBU2hDLGVBQWU7UUFDekY7UUFFQXRFLHlEQUFnQkEsQ0FBQzJHLFFBQVFyQyxpQkFBaUIrQjtJQUM1QztJQUlBLGVBQWU7SUFDZixNQUFNTyx5QkFBeUIsQ0FBQ0M7UUFDOUIsTUFBTUMsZUFBZSxDQUFDUjtZQUNwQixJQUFJQSxTQUFTbEIsVUFBVSxLQUFLbUIsV0FBV2xCLGNBQWNpQixTQUFTbEIsVUFBVTtZQUN4RSxJQUFJa0IsU0FBU2hCLFNBQVMsS0FBS2lCLFdBQVdoQixhQUFhZSxTQUFTaEIsU0FBUztRQUN2RTtRQUNBcEYsd0RBQWVBLENBQUMyRyxHQUFHQztJQUNyQjtJQUVBLE1BQU1DLHlCQUF5QixDQUFDRjtRQUM5QixNQUFNRyxZQUF1QjtZQUFFNUI7WUFBWUU7UUFBVTtRQUNyRG5GLHdEQUFlQSxDQUFDMEcsR0FBR0c7SUFDckI7SUFFQSxNQUFNQyx1QkFBdUIsQ0FBQ0o7UUFDNUIsTUFBTUMsZUFBZSxDQUFDUjtZQUNwQixJQUFJQSxTQUFTbEIsVUFBVSxLQUFLbUIsV0FBV2xCLGNBQWNpQixTQUFTbEIsVUFBVTtZQUN4RSxJQUFJa0IsU0FBU2hCLFNBQVMsS0FBS2lCLFdBQVdoQixhQUFhZSxTQUFTaEIsU0FBUztRQUN2RTtRQUNBbEYsc0RBQWFBLENBQUN5RyxHQUFHQztJQUNuQjtJQUVBLE1BQU1JLDBCQUEwQixDQUFDTDtRQUMvQixNQUFNQyxlQUFlLENBQUNSO1lBQ3BCLElBQUlBLFNBQVNsQixVQUFVLEtBQUttQixXQUFXbEIsY0FBY2lCLFNBQVNsQixVQUFVO1lBQ3hFLElBQUlrQixTQUFTaEIsU0FBUyxLQUFLaUIsV0FBV2hCLGFBQWFlLFNBQVNoQixTQUFTO1FBQ3ZFO1FBQ0FqRix5REFBZ0JBLENBQUN3RyxHQUFHQztJQUN0QjtJQUVBLDRCQUE0QjtJQUM1QjdILGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTRELGdCQUFnQixhQUFhcUIsY0FBYyxlQUFlbkQsU0FBUztZQUNyRSxTQUFTO1lBQ1RzRCxTQUFTLEVBQUU7WUFDWFEsUUFBUTtZQUNSRSxXQUFXO1lBQ1hSLG1CQUFtQixFQUFFO1lBQ3JCVSxlQUFlO1lBRWZnQixlQUFlLEdBQUc7UUFDcEI7SUFDRixHQUFHO1FBQUNwRDtRQUFhcUI7UUFBV25EO0tBQVE7SUFFcEMsWUFBWTtJQUNaLE1BQU1vRyxlQUFlO1FBQ25CLFNBQVM7UUFDVHJFLGVBQWU7UUFFZixVQUFVO1FBQ1ZYLGFBQWE7WUFDWEMsc0JBQXNCO1lBQ3RCQyxvQkFBb0I7WUFDcEJDLHdCQUF3QjtZQUN4QkMsa0JBQWtCO1lBQ2xCQyxrQkFBa0IsRUFBRTtRQUN0QjtRQUVBLFNBQVM7UUFDVEUsZUFBZTtZQUNiQyxtQkFBbUI7WUFDbkJDLHVCQUF1QjtRQUN6QjtRQUVBLFNBQVM7UUFDVEksWUFBWTtZQUNWQyxVQUFVO1lBQ1ZDLGlCQUFpQjtZQUNqQkMscUJBQXFCLEVBQUU7WUFDdkJDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxTQUFTO1FBQ1g7UUFFQSxTQUFTO1FBQ1RhLGFBQWE7UUFDYkUsU0FBUyxFQUFFO1FBQ1hFLG1CQUFtQixFQUFFO1FBQ3JCRSxlQUFlLEVBQUU7UUFDakJ2RCxpQkFBaUI7UUFDakJNLHdCQUF3QjtRQUN4QkUsaUNBQWlDO1FBQ2pDbUQsUUFBUTtRQUNSRSxXQUFXO1FBQ1hFLGVBQWU7UUFFZixXQUFXO1FBQ1hyRCxZQUFZLEVBQUU7UUFDZEUsb0JBQW9CLElBQUlDO0lBQzFCO0lBRUEsZUFBZTtJQUNmOUMsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN1QixRQUFRO1lBQ1gyRztRQUNGO0lBQ0YsR0FBRztRQUFDM0c7S0FBTztJQUVYLFlBQVk7SUFDWnZCLGdEQUFTQSxDQUFDO1FBQ1IsT0FBTztZQUNMa0k7UUFDRjtJQUNGLEdBQUcsRUFBRTtJQUlMLE1BQU1DLGVBQWVoSSwwRkFBZUE7SUFFcEMsU0FBUztJQUNUSCxnREFBU0EsQ0FBQztRQUNSK0IsV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLDhCQUE4QjtJQUU5QixzQkFBc0I7SUFDdEIsTUFBTXFHLHlCQUF5QjtRQUM3QixJQUFJLEVBQUN2RywwQkFBQUEsb0NBQUFBLGNBQWV3RyxFQUFFLEtBQUksQ0FBQ3ZHLFNBQVM7UUFFcENrQix3QkFBd0IsT0FBTyxhQUFhO1FBRTVDLElBQUk7WUFDRixNQUFNc0YsZUFBZSxNQUFNM0gsMkVBQWtDQSxDQUFDa0IsY0FBY3dHLEVBQUUsRUFBRUY7WUFDaEZ4RixZQUFZMkY7WUFFWiw0QkFBNEI7WUFDNUIsSUFBSUEsYUFBYUMsTUFBTSxHQUFHLEdBQUc7Z0JBQzNCLGlCQUFpQjtnQkFDakIsTUFBTUMsWUFBWSxNQUFNNUgsMkRBQWtCQSxDQUFDMEgsYUFBYUcsR0FBRyxDQUFDLENBQUNDLElBQWVBLEVBQUVDLE1BQU0sR0FBR1I7Z0JBQ3ZGdEYsb0JBQW9CMkY7WUFDdEI7WUFDQXhGLHdCQUF3QjtRQUMxQixFQUFFLE9BQU80RixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxhQUFhQTtZQUMzQmpHLFlBQVksRUFBRTtZQUNkSyx3QkFBd0I7UUFDMUI7SUFDRjtJQUlBLHNCQUFzQjtJQUV0QmhELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXVCLFVBQVVNLGVBQWU7WUFDM0J1RztZQUNBLFVBQVU7WUFDVmxGLGFBQWE7Z0JBQ1hDLHNCQUFzQjJGLG1CQUFtQixTQUFTO2dCQUNsRDFGLG9CQUFvQjtnQkFDcEJDLHdCQUF3QjtnQkFDeEJDLGtCQUFrQjtnQkFDbEJDLGtCQUFrQixFQUFFO1lBQ3RCO1lBRUEsU0FBUztZQUNURSxlQUFlO2dCQUNiQyxtQkFBbUI7Z0JBQ25CQyx1QkFBdUI7WUFDekI7WUFFQSxXQUFXO1lBQ1hpQixTQUFTaUMsSUFBSSxDQUFDRSxLQUFLLENBQUNELFFBQVEsR0FBRztRQUNqQyxPQUFPO1lBQ0wsV0FBVztZQUNYbEMsU0FBU2lDLElBQUksQ0FBQ0UsS0FBSyxDQUFDRCxRQUFRLEdBQUc7UUFDakM7UUFFQSxpQkFBaUI7UUFDakIsT0FBTztZQUNMbEMsU0FBU2lDLElBQUksQ0FBQ0UsS0FBSyxDQUFDRCxRQUFRLEdBQUc7UUFDakM7SUFDRixHQUFHO1FBQUN2RjtRQUFRTTtRQUFlQztLQUFRO0lBRW5DLHNCQUFzQjtJQUN0QjlCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTBDLFNBQVM2RixNQUFNLEdBQUcsR0FBRztZQUN2QnJGLGFBQWE4QixDQUFBQSxPQUFTO29CQUNwQixHQUFHQSxJQUFJO29CQUNQekIsa0JBQWtCYixTQUFTK0YsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNO2dCQUM5QztRQUNGO0lBQ0YsR0FBRztRQUFDakc7S0FBUztJQUViLE1BQU1xRyxzQkFBc0I7UUFDMUI7WUFBRVYsSUFBSTtZQUFRVyxPQUFPO1lBQU9DLGFBQWE7WUFBVUMsVUFBVTtRQUFNO1FBQ25FO1lBQUViLElBQUk7WUFBVVcsT0FBTztZQUFNQyxhQUFhO1lBQVNDLFVBQVU7UUFBSztRQUNsRTtZQUFFYixJQUFJO1lBQWNXLE9BQU87WUFBT0MsYUFBYTtZQUFXQyxVQUFVO1FBQUs7S0FDMUU7SUFFRCxzQkFBc0I7SUFDdEIsTUFBTUosbUJBQW1CbkgsZUFBZTtJQUV4QyxzQkFBc0I7SUFDdEIsTUFBTXdILG1CQUFtQnhILGVBQWU7SUFFeEMsTUFBTXlILDJCQUEyQixDQUFDQztRQUNoQ25HLGFBQWE4QixDQUFBQSxPQUFTO2dCQUNwQixHQUFHQSxJQUFJO2dCQUNQN0Isc0JBQXNCa0c7WUFDeEI7UUFFQSxjQUFjO1FBQ2RDLFdBQVc7WUFDVCxJQUFJRCxhQUFhLFlBQVlqSCxlQUFlbUgsT0FBTyxFQUFFO2dCQUNuRG5ILGVBQWVtSCxPQUFPLENBQUNDLEtBQUs7WUFDOUIsT0FBTyxJQUFJSCxhQUFhLGdCQUFnQmhILG1CQUFtQmtILE9BQU8sRUFBRTtnQkFDbEVsSCxtQkFBbUJrSCxPQUFPLENBQUNDLEtBQUs7WUFDbEM7UUFDRixHQUFHLE1BQU0sZ0JBQWdCO0lBQzNCO0lBRUEsTUFBTUMsdUJBQXVCLENBQUNDO1FBQzVCeEcsYUFBYThCLENBQUFBLE9BQVM7Z0JBQ3BCLEdBQUdBLElBQUk7Z0JBQ1AxQixrQkFBa0JvRztZQUNwQjtJQUNGO0lBRUEsTUFBTUMsdUJBQXVCO1FBQzNCekcsYUFBYThCLENBQUFBLE9BQVM7Z0JBQ3BCLEdBQUdBLElBQUk7Z0JBQ1AxQixrQkFBa0I7WUFFcEI7SUFDRjtJQUVBLE1BQU1zRywyQkFBMkI7UUFDL0JySCx3QkFBd0I7SUFDMUI7SUFFQSxNQUFNc0gsNEJBQTRCO1FBQ2hDdEgsd0JBQXdCO0lBQzFCO0lBRUEsY0FBYztJQUNkLE1BQU11SCxvQ0FBb0M7UUFDeENySCxpQ0FBaUM7SUFDbkM7SUFFQSxNQUFNc0gscUNBQXFDO1FBQ3pDdEgsaUNBQWlDO0lBQ25DO0lBRUEsTUFBTXVILGtDQUFrQztRQUN0QyxrQkFBa0I7UUFDbEIsSUFBSXRILFNBQVM2RixNQUFNLEdBQUcsR0FBRztZQUN2QixNQUFNQyxZQUFZLE1BQU01SCwyREFBa0JBLENBQUM4QixTQUFTK0YsR0FBRyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNLEdBQUdSO1lBQ3hFdEYsb0JBQW9CMkY7UUFDdEI7UUFDQUwsYUFBYThCLE9BQU8sQ0FBQztJQUN2QjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNQyx5QkFBeUI7UUFDN0IsSUFBSWpILFVBQVVFLG9CQUFvQixLQUFLLFVBQVU7WUFDL0MsT0FBT0YsVUFBVUcsa0JBQWtCO1FBQ3JDLE9BQU8sSUFBSUgsVUFBVUUsb0JBQW9CLEtBQUssY0FBYztZQUMxRCxPQUFPRixVQUFVSSxzQkFBc0I7UUFDekM7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxpQkFBaUI7SUFDakIsTUFBTThHLHdCQUF3QjtRQUM1QixJQUFJbEgsVUFBVU0sZ0JBQWdCLENBQUNnRixNQUFNLEtBQUssR0FBRyxPQUFPO1FBRXBELE1BQU02Qix3QkFBd0JuSCxVQUFVTSxnQkFBZ0IsQ0FBQ2tGLEdBQUcsQ0FBQzRCLENBQUFBLFlBQzNEekgsaUJBQWlCMEgsR0FBRyxDQUFDRCxjQUFjO1FBR3JDLE9BQU9FLEtBQUtDLEdBQUcsSUFBSUo7SUFDckI7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTUssdUJBQXVCO1FBQzNCLElBQUl4SCxVQUFVRSxvQkFBb0IsS0FBSyxZQUFZRixVQUFVRSxvQkFBb0IsS0FBSyxjQUFjO1lBQ2xHLE9BQU87Z0JBQ0w2RixPQUFPO2dCQUNQMEIsT0FBT1A7WUFDVDtRQUNGO1FBQ0EsT0FBTztZQUFFbkIsT0FBTztZQUFJMEIsT0FBTztRQUFFO0lBQy9CO0lBRUEsTUFBTUMsYUFBYTtRQUNqQjlCLFFBQVErQixHQUFHLENBQUMsWUFBWTNILFVBQVVFLG9CQUFvQjtRQUN0RDBGLFFBQVErQixHQUFHLENBQUMsVUFBVTNILFVBQVVLLGdCQUFnQjtRQUVoRCxtQkFBbUI7UUFDbkIsSUFBSSxDQUFDd0Ysb0JBQXFCN0YsQ0FBQUEsVUFBVUUsb0JBQW9CLEtBQUssWUFBWUYsVUFBVUUsb0JBQW9CLEtBQUssWUFBVyxHQUFJO1lBQ3pILE1BQU0wSCxzQkFBc0JYO1lBQzVCckIsUUFBUStCLEdBQUcsQ0FBQyxXQUFXQztZQUV2QixZQUFZO1lBQ1osTUFBTUMsV0FBVzdILFVBQVVFLG9CQUFvQixLQUFLLFdBQVcsc0JBQXNCO1lBQ3JGLElBQUlLLFdBQVcsQ0FBQ3NILFNBQXFDLEVBQUU7Z0JBQ3JEM0MsYUFBYVMsS0FBSyxDQUFDO2dCQUNuQjtZQUNGO1lBRUEsYUFBYTtZQUNiLE1BQU1tQyxrQkFBa0JDLE9BQU9IO1lBQy9CLElBQUksQ0FBQ0EsdUJBQXVCRSxtQkFBbUIsR0FBRztnQkFDaEQ1QyxhQUFhUyxLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxJQUFJM0YsVUFBVUUsb0JBQW9CLEtBQUssVUFBVTtnQkFDL0MsMEJBQTBCO2dCQUMxQixNQUFNOEgsdUJBQXVCaEksVUFBVU0sZ0JBQWdCLENBQUMySCxNQUFNLENBQUNiLENBQUFBO29CQUM3RCxNQUFNYyx5QkFBeUJ2SSxpQkFBaUIwSCxHQUFHLENBQUNEO29CQUNwRCxPQUFPYywyQkFBMkI3RCxhQUFhNkQseUJBQXlCSjtnQkFDMUU7Z0JBRUEsSUFBSUUscUJBQXFCMUMsTUFBTSxHQUFHLEdBQUc7b0JBQ25DLE1BQU02QyxvQkFBb0JILHFCQUFxQnhDLEdBQUcsQ0FBQzRCLENBQUFBO3dCQUNqRCxNQUFNZ0IsVUFBVTNJLFNBQVM0SSxJQUFJLENBQUM1QyxDQUFBQSxJQUFLQSxFQUFFQyxNQUFNLEtBQUswQjt3QkFDaEQsTUFBTWtCLGtCQUFrQjNJLGlCQUFpQjBILEdBQUcsQ0FBQ0QsY0FBYzt3QkFDM0QsT0FBTyxHQUFpRGtCLE9BQTlDRixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNHLFFBQVEsS0FBSSxLQUFlLE9BQVZuQixZQUFZLFVBQXdCLE9BQWhCa0IsaUJBQWdCO29CQUMxRSxHQUFHRSxJQUFJLENBQUM7b0JBRVJ0RCxhQUFhUyxLQUFLLENBQUMscUJBQXdDd0MsT0FBbkJMLGlCQUFnQixLQUFxQixPQUFsQks7b0JBQzNEO2dCQUNGO1lBQ0YsT0FBTyxJQUFJbkksVUFBVUUsb0JBQW9CLEtBQUssY0FBYztnQkFDMUQsc0JBQXNCO2dCQUN0QixNQUFNdUksd0JBQXdCekksVUFBVU0sZ0JBQWdCLENBQUMySCxNQUFNLENBQUNiLENBQUFBO29CQUM5RCxNQUFNc0IsZ0JBQWdCL0ksaUJBQWlCMEgsR0FBRyxDQUFDRCxjQUFjO29CQUN6RCxPQUFPc0IsZ0JBQWdCWjtnQkFDekI7Z0JBRUEsSUFBSVcsc0JBQXNCbkQsTUFBTSxHQUFHLEdBQUc7b0JBQ3BDLDBCQUEwQjtvQkFDMUIsTUFBTTBDLHVCQUF1QlMsc0JBQXNCUixNQUFNLENBQUNiLENBQUFBO3dCQUN4RCxNQUFNc0IsZ0JBQWdCL0ksaUJBQWlCMEgsR0FBRyxDQUFDRCxjQUFjO3dCQUN6RCxNQUFNdUIsZUFBZWIsa0JBQWtCWTt3QkFDdkMsTUFBTVIseUJBQXlCdkksaUJBQWlCMEgsR0FBRyxDQUFDRDt3QkFDcEQsT0FBT2MsMkJBQTJCN0QsYUFBYTZELHlCQUF5QlM7b0JBQzFFO29CQUVBLElBQUlYLHFCQUFxQjFDLE1BQU0sR0FBRyxHQUFHO3dCQUNuQyxNQUFNNkMsb0JBQW9CSCxxQkFBcUJ4QyxHQUFHLENBQUM0QixDQUFBQTs0QkFDakQsTUFBTWdCLFVBQVUzSSxTQUFTNEksSUFBSSxDQUFDNUMsQ0FBQUEsSUFBS0EsRUFBRUMsTUFBTSxLQUFLMEI7NEJBQ2hELE1BQU1zQixnQkFBZ0IvSSxpQkFBaUIwSCxHQUFHLENBQUNELGNBQWM7NEJBQ3pELE1BQU11QixlQUFlYixrQkFBa0JZOzRCQUN2QyxNQUFNSixrQkFBa0IzSSxpQkFBaUIwSCxHQUFHLENBQUNELGNBQWM7NEJBQzNELE9BQU8sR0FBZ0R1QixPQUE3Q1AsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTRyxRQUFRLEtBQUksS0FBZSxPQUFWbkIsWUFBWSxTQUE2QmtCLE9BQXRCSyxjQUFhLFdBQXlCLE9BQWhCTCxpQkFBZ0I7d0JBQy9GLEdBQUdFLElBQUksQ0FBQzt3QkFFUnRELGFBQWFTLEtBQUssQ0FBQyxzQkFBeUN3QyxPQUFuQkwsaUJBQWdCLEtBQXFCLE9BQWxCSzt3QkFDNUQ7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsY0FBYztRQUNkLElBQUl6SixlQUFlLFVBQVVBLGVBQWUsUUFBUTtZQUNsRCxzQkFBc0I7WUFDdEJvQyxZQUFZaUIsQ0FBQUEsT0FBUztvQkFDbkIsR0FBR0EsSUFBSTtvQkFDUGIsVUFBVSxNQUFNLFlBQVk7Z0JBQzlCO1lBQ0FOLGVBQWU7UUFDakIsT0FBTztZQUNMLDBCQUEwQjtZQUMxQmdGLFFBQVErQixHQUFHLENBQUMsR0FBYyxPQUFYakosWUFBVyxTQUFPO2dCQUMvQndCLHNCQUFzQkYsVUFBVUUsb0JBQW9CO2dCQUNwREcsa0JBQWtCTCxVQUFVSyxnQkFBZ0I7Z0JBQzVDQyxrQkFBa0JOLFVBQVVNLGdCQUFnQjtnQkFDNUNzSSxjQUFjM0I7WUFDaEI7WUFFQSxzQkFBc0I7WUFDdEIsMEJBQTBCO1lBRTFCL0IsYUFBYThCLE9BQU8sQ0FBQyxHQUFjLE9BQVh0SSxZQUFXO1lBQ25DdUc7WUFDQTFHO1FBQ0Y7SUFDRjtJQUVBLE1BQU1zSyxpQkFBaUI7UUFDckIsSUFBSWxJLGdCQUFnQixXQUFXO1lBQzdCLG1CQUFtQjtZQUNuQkcsWUFBWWlCLENBQUFBLE9BQVM7b0JBQ25CLEdBQUdBLElBQUk7b0JBQ1BiLFVBQVU7Z0JBQ1o7WUFDQU4sZUFBZTtRQUNqQixPQUFPO1lBQ0xwQztRQUNGO0lBQ0Y7SUFJQSxpQ0FBaUM7SUFDakMsTUFBTXNLLGdCQUFnQjtRQUNwQixJQUFJLENBQUNqSSxTQUFTRSxRQUFRLENBQUNnSSxJQUFJLElBQUk7WUFDN0I3RCxhQUFhUyxLQUFLLENBQUM7WUFDbkI7UUFDRjtRQUVBLE1BQU1xRCxRQUFRNUsscURBQVlBLENBQUN5QyxTQUFTSyxRQUFRO1FBQzVDLE1BQU0rSCxnQkFBZ0I7WUFDcEIsR0FBR3BJLFFBQVE7WUFDWE0sV0FBVzZILE1BQU03SCxTQUFTO1lBQzFCQyxTQUFTNEgsTUFBTTVILE9BQU87WUFDdEJnQjtZQUNBRTtZQUNBdEM7UUFDRjtRQUVBLGlDQUFpQztRQUNqQyxJQUFJdkIsV0FBVztZQUNiQSxVQUFVd0s7UUFDWixPQUFPO1lBQ0wsZ0JBQWdCO1lBQ2hCQztRQUNGO0lBQ0Y7SUFFQSxNQUFNQSxzQkFBc0I7UUFDMUIsU0FBUztRQUNULElBQUlsRyxjQUFjO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJO2dCQTJDWWhEO1lBMUNkaUQsZ0JBQWdCO1lBRWhCLFNBQVM7WUFDVCxJQUFJLENBQUNwQyxTQUFTRSxRQUFRLENBQUNnSSxJQUFJLElBQUk7Z0JBQzdCN0QsYUFBYVMsS0FBSyxDQUFDO2dCQUNuQjFDLGdCQUFnQjtnQkFDaEI7WUFDRjtZQUVBMkMsUUFBUStCLEdBQUcsQ0FBQyxTQUFTOUc7WUFDckIrRSxRQUFRK0IsR0FBRyxDQUFDLFlBQVl2RjtZQUN4QndELFFBQVErQixHQUFHLENBQUMsVUFBVTNIO1lBRXRCLFdBQVc7WUFDWCxNQUFNbUosV0FBV0MsYUFBYUMsT0FBTyxDQUFDO1lBQ3RDLE1BQU1DLE9BQU9ILFdBQVdJLEtBQUtDLEtBQUssQ0FBQ0wsWUFBWTtZQUMvQyxNQUFNTSxZQUFZSCxpQkFBQUEsMkJBQUFBLEtBQU01RCxNQUFNO1lBRTlCLElBQUksQ0FBQytELFdBQVc7Z0JBQ2R2RSxhQUFhUyxLQUFLLENBQUM7Z0JBQ25CMUMsZ0JBQWdCO2dCQUNoQjtZQUNGO1lBRUEsT0FBTztZQUNQLE1BQU15RyxZQUFZN0ksU0FBU00sU0FBUyxHQUFHLElBQUl3SSxLQUFLOUksU0FBU00sU0FBUyxJQUFJLElBQUl3STtZQUMxRSxNQUFNQyxVQUFVL0ksU0FBU08sT0FBTyxHQUFHLElBQUl1SSxLQUFLOUksU0FBU08sT0FBTyxJQUFJLElBQUl1SSxLQUFLQSxLQUFLRSxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssS0FBSztZQUV6RyxXQUFXO1lBQ1gsTUFBTUMsYUFBYTtnQkFDakIvSSxVQUFVRixTQUFTRSxRQUFRO2dCQUMzQkMsaUJBQWlCSCxTQUFTRyxlQUFlLElBQUk7Z0JBQzdDK0ksVUFBVXhNLG1EQUFRQSxDQUFDeU0sT0FBTztnQkFDMUJDLFVBQVV6TSxtREFBUUEsQ0FBQzBNLE1BQU07Z0JBQ3pCUixXQUFXQTtnQkFDWEUsU0FBU0E7Z0JBQ1RPLGFBQWF0SixTQUFTRyxlQUFlLElBQUk7Z0JBQ3pDc0IsYUFBYUEsWUFBWWtELEdBQUcsQ0FBQzRFLENBQUFBLE9BQVFBLEtBQUtDLElBQUksS0FBSyxFQUFFO2dCQUNyREMsVUFBVTtnQkFDVkMscUJBQXFCO2dCQUNyQkMsWUFBWXhLLFVBQVVNLGdCQUFnQjtnQkFDdENtSyxPQUFPLEVBQUU3TCwwQkFBQUEsb0NBQUFBLGNBQWV3RyxFQUFFO2dCQUMxQnNGLFVBQVUsR0FBRTFLLDhCQUFBQSxVQUFVSyxnQkFBZ0IsY0FBMUJMLGtEQUFBQSw0QkFBNEJvRixFQUFFO2dCQUMxQ3VGLFNBQVN2SSxnQkFBZ0JrRCxNQUFNLEdBQUcsSUFBSWxELGtCQUFrQmlDO2dCQUN4RHBELHFCQUFxQkosU0FBU0ksbUJBQW1CLENBQUNnSCxNQUFNLENBQUMyQyxDQUFBQSxPQUFRQSxLQUFLN0IsSUFBSSxPQUFPLE9BQU8sRUFBRSxDQUFDLFVBQVU7WUFDdkc7WUFFQW5ELFFBQVErQixHQUFHLENBQUMsV0FBV21DO1lBQ3ZCbEUsUUFBUStCLEdBQUcsQ0FBQyxXQUFXbUMsV0FBV2EsT0FBTztZQUV6QyxXQUFXO1lBQ1gsTUFBTUUsV0FBMkIsRUFBRTtZQUVuQyxrQkFBa0I7WUFDbEJBLFNBQVNDLElBQUksQ0FBQ3hOLHFEQUFPQSxDQUFDeU4sV0FBVyxDQUFDakI7WUFFbEMsa0JBQWtCO1lBQ2xCLElBQUlrQixnQkFBcUM7WUFDekMsTUFBTXBELHNCQUFzQlg7WUFDNUIsSUFBSSxDQUFDakgsVUFBVUUsb0JBQW9CLEtBQUssWUFBWUYsVUFBVUUsb0JBQW9CLEtBQUssWUFBVyxLQUFNMEgscUJBQXFCO2dCQUMzSCxNQUFNcUQsZUFBZWxELE9BQU9IO2dCQUM1QixNQUFNc0Qsb0JBQW9CLElBQUl2QixLQUFLQSxLQUFLRSxHQUFHLEtBQUssS0FBSyxLQUFLLEtBQUssS0FBSyxNQUFNc0IsV0FBVztnQkFFckYsSUFBSW5MLFVBQVVFLG9CQUFvQixLQUFLLFVBQVU7b0JBQy9DLHdCQUF3QjtvQkFDeEIsTUFBTWtMLGtCQUFtRCxDQUFDO29CQUMxRHBMLFVBQVVNLGdCQUFnQixDQUFDK0ssT0FBTyxDQUFDakUsQ0FBQUE7d0JBQ2pDZ0UsZUFBZSxDQUFDaEUsVUFBVSxHQUFHOEQ7b0JBQy9CO29CQUVBRixnQkFBZ0IzTixzREFBU0EsQ0FBQ2lPLHFCQUFxQixDQUFDO3dCQUM5Q2hELGlCQUFpQjJDO3dCQUNqQkc7d0JBQ0FHLFFBQVEsVUFBNEIsT0FBbEIxSyxTQUFTRSxRQUFRO29CQUNyQztvQkFDQThKLFNBQVNDLElBQUksQ0FBQ0U7Z0JBQ2hCLE9BQU8sSUFBSWhMLFVBQVVFLG9CQUFvQixLQUFLLGNBQWM7b0JBQzFELHNCQUFzQjtvQkFDdEIsTUFBTXNMLGlCQUFpQyxFQUFFO29CQUV6Q3hMLFVBQVVNLGdCQUFnQixDQUFDK0ssT0FBTyxDQUFDakUsQ0FBQUE7d0JBQ2pDLE1BQU1zQixnQkFBZ0IvSSxpQkFBaUIwSCxHQUFHLENBQUNELGNBQWM7d0JBQ3pELE1BQU11QixlQUFlc0MsZUFBZXZDO3dCQUVwQyxxQkFBcUI7d0JBQ3JCLElBQUlDLGVBQWUsR0FBRzs0QkFDcEIsTUFBTXlDLGtCQUFtRCxDQUFDOzRCQUMxREEsZUFBZSxDQUFDaEUsVUFBVSxHQUFHOEQ7NEJBRTdCLE1BQU1PLFVBQVVwTyxzREFBU0EsQ0FBQ2lPLHFCQUFxQixDQUFDO2dDQUM5Q2hELGlCQUFpQks7Z0NBQ2pCeUM7Z0NBQ0FHLFFBQVEsVUFBbUNOLE9BQXpCcEssU0FBU0UsUUFBUSxFQUFDLFNBQW9CLE9BQWJrSyxjQUFhOzRCQUMxRDs0QkFDQU8sZUFBZVYsSUFBSSxDQUFDVzt3QkFDdEI7b0JBQ0Y7b0JBRUEsb0JBQW9CO29CQUNwQlosU0FBU0MsSUFBSSxJQUFJVTtnQkFDbkI7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQixJQUFJRSxrQkFBdUM7WUFDM0MsSUFBSTFMLFVBQVVLLGdCQUFnQixFQUFFO2dCQUM5QixNQUFNc0wsUUFBUTNMLFVBQVVNLGdCQUFnQixDQUFDa0YsR0FBRyxDQUFDNEIsQ0FBQUEsWUFBYzt3QkFDekQxQixRQUFRMEI7d0JBQ1J3RSxRQUFRO3dCQUNSbEIsWUFBWTFLLFVBQVVLLGdCQUFnQixDQUFDK0UsRUFBRTt3QkFDekN5RyxvQkFBb0I3TCxVQUFVSyxnQkFBZ0IsQ0FBQ3dMLGtCQUFrQixJQUFJN0wsVUFBVUssZ0JBQWdCLENBQUMrRSxFQUFFO29CQUNwRztnQkFFQXNHLGtCQUFrQmpPLG9FQUFvQkEsQ0FBQztvQkFBRWtPO2dCQUFNO2dCQUMvQ2QsU0FBU0MsSUFBSSxDQUFDWTtZQUNoQjtZQUVBLFdBQVc7WUFDWCxNQUFNSSxVQUFVLE1BQU1DLFFBQVFDLFVBQVUsQ0FBQ25CO1lBRXpDLFdBQVc7WUFDWCxNQUFNb0IsYUFBYUgsT0FBTyxDQUFDLEVBQUU7WUFDN0IsSUFBSUcsV0FBV0MsTUFBTSxLQUFLLGVBQWVELFdBQVd4RSxLQUFLLENBQUMwRSxJQUFJLENBQUNDLElBQUksS0FBSyxLQUFLO2dCQUMzRSxjQUFjO2dCQUNkbEgsYUFBYThCLE9BQU8sQ0FBQztZQUN2QixPQUFPO2dCQUNMLE1BQU1xRixXQUFXSixXQUFXQyxNQUFNLEtBQUssY0FDbkNELFdBQVd4RSxLQUFLLENBQUMwRSxJQUFJLENBQUNHLE9BQU8sSUFBSSxXQUNqQztnQkFDSnBILGFBQWFTLEtBQUssQ0FBQzBHO2dCQUNuQnBKLGdCQUFnQjtnQkFDaEIsUUFBUSxjQUFjO1lBQ3hCO1lBRUEsV0FBVztZQUNYLElBQUlzSixjQUFjO1lBQ2xCLElBQUl2QixlQUFlO2dCQUNqQixNQUFNd0IsZUFBZVYsT0FBTyxDQUFDUyxZQUFZO2dCQUN6QyxJQUFJQyxhQUFhTixNQUFNLEtBQUssZUFBZU0sYUFBYS9FLEtBQUssQ0FBQzBFLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7b0JBQy9FbEgsYUFBYThCLE9BQU8sQ0FBQztnQkFDdkIsT0FBTztvQkFDTHBCLFFBQVFELEtBQUssQ0FBQyxXQUFXNkc7b0JBQ3pCdEgsYUFBYXVILE9BQU8sQ0FBQztnQkFDdkI7Z0JBQ0FGO1lBQ0Y7WUFFQSxXQUFXO1lBQ1gsSUFBSWIsaUJBQWlCO2dCQUNuQixNQUFNZ0IsaUJBQWlCWixPQUFPLENBQUNTLFlBQVk7Z0JBQzNDLElBQUlHLGVBQWVSLE1BQU0sS0FBSyxlQUFlUSxlQUFlakYsS0FBSyxDQUFDMEUsSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztvQkFDbkZsSCxhQUFhOEIsT0FBTyxDQUFDO2dCQUN2QixPQUFPO29CQUNMcEIsUUFBUUQsS0FBSyxDQUFDLFdBQVcrRztvQkFDekJ4SCxhQUFhdUgsT0FBTyxDQUFDO2dCQUN2QjtZQUNGO1lBRUEsb0JBQW9CO1lBQ3BCcEcsV0FBVztnQkFDVHBELGdCQUFnQjtnQkFDaEJnQztnQkFDQTFHO1lBQ0YsR0FBRztRQUNMLEVBQUUsT0FBT29ILE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLFdBQVdBO1lBQ3pCVCxhQUFhUyxLQUFLLENBQUM7WUFDbkIxQyxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixJQUFJLENBQUNwRSxXQUFXLENBQUNQLFFBQVEsT0FBTztJQUVoQyxxQkFDRSw4REFBQ3FPO1FBQ0NDLFdBQVU7UUFDVkMsU0FBUyxDQUFDbEk7WUFDUixzQkFBc0I7WUFDdEJBLEVBQUVtSSxlQUFlO1FBQ25CO1FBQ0FDLGFBQWEsQ0FBQ3BJO1lBQ1oscUNBQXFDO1lBQ3JDQSxFQUFFbUksZUFBZTtRQUNuQjs7MEJBRUEsOERBQUNIO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0k7d0JBQU9KLFdBQVU7d0JBQTBCSyxTQUFTOzRCQUNuRGhJOzRCQUNBMUc7d0JBQ0Y7a0NBQ0UsNEVBQUN0Qiw4RUFBQ0E7NEJBQUNpUSxNQUFNOzs7Ozs7Ozs7OztrQ0FFWCw4REFBQ1A7d0JBQUlDLFdBQVU7d0JBQXlDTyxhQUFXeE07OzBDQUduRSw4REFBQ2dNO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFBYzs7Ozs7OzBEQUM3Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQWE7Ozs7Ozs7Ozs7OztrREFFOUIsOERBQUNEO3dDQUFJQyxXQUFXLFFBQTRELE9BQXBEak0sZ0JBQWdCLGFBQWEsV0FBVzs7MERBQzlELDhEQUFDZ007Z0RBQUlDLFdBQVU7MERBQWM7Ozs7OzswREFDN0IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNabE8sZUFBZSxTQUFTLFNBQ3hCQSxlQUFlLFNBQVMsU0FDeEI7Ozs7Ozs7Ozs7OztvQ0FHSkEsZUFBZSx3QkFDZCw4REFBQ2lPO3dDQUFJQyxXQUFXLFFBQWtELE9BQTFDak0sZ0JBQWdCLFlBQVksV0FBVzs7MERBQzdELDhEQUFDZ007Z0RBQUlDLFdBQVU7MERBQWM7Ozs7OzswREFDN0IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFhOzs7Ozs7Ozs7Ozs7b0NBRy9CbE8sZUFBZSx3QkFDZCw4REFBQ2lPO3dDQUFJQyxXQUFXLFFBQWtELE9BQTFDak0sZ0JBQWdCLFlBQVksV0FBVzs7MERBQzdELDhEQUFDZ007Z0RBQUlDLFdBQVU7MERBQWM7Ozs7OzswREFDN0IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTWxDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1pqTSxnQkFBZ0IsMkJBQ2Y7OzRDQUVHLENBQUNrRixrQ0FDQSw4REFBQzhHO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBR1IsV0FBVTs4REFBZ0I7Ozs7Ozs7Ozs7OzBEQUlsQyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O29EQUVaLENBQUMvRyxrQ0FDQSw4REFBQzhHO3dEQUFJQyxXQUFVO2tFQUNoQjlHLG9CQUFvQk4sR0FBRyxDQUFDLENBQUM2SCx1QkFDeEIsOERBQUNWO2dFQUVDQyxXQUFXLHFCQUFvRixPQUEvRDVNLFVBQVVFLG9CQUFvQixLQUFLbU4sT0FBT2pJLEVBQUUsR0FBRyxhQUFhO2dFQUM1RjZILFNBQVMsSUFBTTlHLHlCQUF5QmtILE9BQU9qSSxFQUFFO2dFQUNqRGtJLGNBQWMsSUFBTXRPLGlCQUFpQnFPLE9BQU9qSSxFQUFFO2dFQUM5Q21JLGNBQWMsSUFBTXZPLGlCQUFpQjs7a0ZBRXJDLDhEQUFDMk47d0VBQUlDLFdBQVU7a0ZBQ1osQ0FBQzs0RUFDQSxNQUFNWSxnQkFBZ0JILE9BQU9qSSxFQUFFLEtBQUssV0FBV3BGLFVBQVVHLGtCQUFrQixHQUN2RGtOLE9BQU9qSSxFQUFFLEtBQUssZUFBZXBGLFVBQVVJLHNCQUFzQixHQUFHOzRFQUNwRixPQUFPaU4sT0FBT3BILFFBQVEsSUFBSXVILGlCQUFpQnpGLE9BQU95RixpQkFBaUIsS0FBS3hOLFVBQVVFLG9CQUFvQixLQUFLbU4sT0FBT2pJLEVBQUUsR0FDaEgsR0FBbUJvSSxPQUFoQkgsT0FBT3RILEtBQUssRUFBQyxLQUFpQixPQUFkeUgsZUFBYyxRQUNqQ0gsT0FBT3RILEtBQUs7d0VBQ2xCOzs7Ozs7b0VBRURzSCxPQUFPcEgsUUFBUSxJQUFLakcsQ0FBQUEsVUFBVUUsb0JBQW9CLEtBQUttTixPQUFPakksRUFBRSxJQUFJbkcsaUJBQWlCb08sT0FBT2pJLEVBQUUsbUJBQzdGLDhEQUFDdUg7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDYTtnRkFDQ0MsS0FBS0wsT0FBT2pJLEVBQUUsS0FBSyxXQUFXakcsaUJBQWlCQztnRkFDL0N1TyxNQUFLO2dGQUNMZixXQUFVO2dGQUNWZ0IsYUFBYVAsT0FBT2pJLEVBQUUsS0FBSyxXQUFXLFNBQVM7Z0ZBQy9DcUMsT0FBTzRGLE9BQU9qSSxFQUFFLEtBQUssV0FBV3BGLFVBQVVHLGtCQUFrQixHQUFHSCxVQUFVSSxzQkFBc0I7Z0ZBQy9GbUgsS0FBSTtnRkFDSnNHLFVBQVUsQ0FBQ2xKO29GQUNULE1BQU04QyxRQUFROUMsRUFBRWxELE1BQU0sQ0FBQ2dHLEtBQUs7b0ZBQzVCLE1BQU1xRyxZQUFZVCxPQUFPakksRUFBRSxLQUFLLFdBQVcsdUJBQXVCO29GQUNsRSxNQUFNeUMsV0FBV3dGLE9BQU9qSSxFQUFFLEtBQUssV0FBVyxzQkFBc0I7b0ZBRWhFLFVBQVU7b0ZBQ1Y1RSxlQUFldUIsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFLENBQUM4RixTQUFTLEVBQUU7d0ZBQUc7b0ZBRWxELFdBQVc7b0ZBQ1gsSUFBSUosVUFBVSxJQUFJO3dGQUNoQnhILGFBQWE4QixDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUUsQ0FBQytMLFVBQVUsRUFBRXJHOzRGQUFNO29GQUN0RCxPQUFPO3dGQUNMLE1BQU1zRyxXQUFXaEcsT0FBT047d0ZBQ3hCLElBQUlNLE9BQU9pRyxTQUFTLENBQUNELFdBQVc7NEZBQzlCLElBQUlBLFdBQVcsR0FBRztnR0FDaEIsU0FBUztnR0FDVHZOLGVBQWV1QixDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUUsQ0FBQzhGLFNBQVMsRUFBRTtvR0FBWTtnR0FDM0Q1SCxhQUFhOEIsQ0FBQUEsT0FBUzt3R0FBRSxHQUFHQSxJQUFJO3dHQUFFLENBQUMrTCxVQUFVLEVBQUVyRztvR0FBTTs0RkFDdEQsT0FBTztnR0FDTCxPQUFPO2dHQUNQeEgsYUFBYThCLENBQUFBLE9BQVM7d0dBQUUsR0FBR0EsSUFBSTt3R0FBRSxDQUFDK0wsVUFBVSxFQUFFckc7b0dBQU07Z0dBQ3BELHFCQUFxQjtnR0FDckJ4SCxhQUFhOEIsQ0FBQUEsT0FBUzt3R0FBRSxHQUFHQSxJQUFJO3dHQUFFN0Isc0JBQXNCbU4sT0FBT2pJLEVBQUU7b0dBQUM7NEZBQ25FO3dGQUNGO29GQUNGO2dGQUNGO2dGQUNBNkgsU0FBUyxDQUFDdEk7b0ZBQ1JBLEVBQUVtSSxlQUFlO29GQUNqQixzQkFBc0I7b0ZBQ3RCN00sYUFBYThCLENBQUFBLE9BQVM7NEZBQUUsR0FBR0EsSUFBSTs0RkFBRTdCLHNCQUFzQm1OLE9BQU9qSSxFQUFFO3dGQUFDO2dGQUNuRTtnRkFDQTZJLFNBQVM7b0ZBQ1AvTyxnQkFBZ0JtTyxPQUFPakksRUFBRTtvRkFDekIsZUFBZTtvRkFDZm5GLGFBQWE4QixDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUU3QixzQkFBc0JtTixPQUFPakksRUFBRTt3RkFBQztnRkFDbkU7Z0ZBQ0E4SSxRQUFRO29GQUNOaFAsZ0JBQWdCO2dGQUNsQjs7Ozs7OzRFQUdBO2dGQUNBLE1BQU0ySSxXQUFXd0YsT0FBT2pJLEVBQUUsS0FBSyxXQUFXLHNCQUFzQjtnRkFDaEUsTUFBTStJLGVBQWU1TixXQUFXLENBQUNzSCxTQUFxQztnRkFDdEUsT0FBT3NHLDhCQUNMLDhEQUFDeEI7b0ZBQUk3SSxPQUFPO3dGQUNWc0ssT0FBTzt3RkFDUEMsVUFBVTt3RkFDVkMsV0FBVzt3RkFDWEMsV0FBVztvRkFDYjs4RkFDR0o7Ozs7Ozs0RUFHUDs7Ozs7Ozs7K0RBL0VDZCxPQUFPakksRUFBRTs7Ozs7Ozs7OztvREF3Rm5CLENBQUNTLG9CQUFxQjdGLENBQUFBLFVBQVVFLG9CQUFvQixLQUFLLFlBQVlGLFVBQVVFLG9CQUFvQixLQUFLLFlBQVcsS0FBTUYsVUFBVU0sZ0JBQWdCLENBQUNnRixNQUFNLEdBQUcsbUJBQzVKLDhEQUFDcUg7d0RBQUlDLFdBQVU7a0VBQ1o5TSxxQ0FDQyw4REFBQzBPOzREQUFLMUssT0FBTztnRUFBRXNLLE9BQU87Z0VBQVdLLFdBQVc7NERBQVM7c0VBQUc7Ozs7O3dFQUl4RCxDQUFDOzREQUNDLE1BQU1DLGNBQWNsSDs0REFDcEIscUJBQ0U7O2tGQUNFLDhEQUFDZ0g7d0VBQUsxSyxPQUFPOzRFQUFFc0ssT0FBTzs0RUFBV0MsVUFBVTt3RUFBTztrRkFBRzs7Ozs7O2tGQUNyRCw4REFBQ0c7OzRFQUFNRSxZQUFZM0ksS0FBSzs0RUFBQzs0RUFBRzJJLFlBQVlqSCxLQUFLOzs7Ozs7Ozs7d0RBR25EOzs7Ozs7b0RBTUwsQ0FBQzVCLG9CQUFxQjdGLENBQUFBLFVBQVVFLG9CQUFvQixLQUFLLFlBQVlGLFVBQVVFLG9CQUFvQixLQUFLLFlBQVcsS0FBTStHLDRCQUE0QixDQUFDbkgsd0JBQ3JKLENBQUM7d0RBQ0MsTUFBTWdJLGtCQUFrQkMsT0FBT2Q7d0RBRS9CLGdCQUFnQjt3REFDaEIsSUFBSTBILGtCQUFrQjt3REFDdEIsSUFBSVIsZUFBZTt3REFFbkIsSUFBSW5PLFVBQVVFLG9CQUFvQixLQUFLLFVBQVU7NERBQy9DLE1BQU0wTyxlQUFlMUg7NERBQ3JCLElBQUlZLGtCQUFrQjhHLGNBQWM7Z0VBQ2xDRCxrQkFBa0I7Z0VBQ2xCUixlQUFlOzREQUNqQjt3REFDRixPQUFPLElBQUluTyxVQUFVRSxvQkFBb0IsS0FBSyxjQUFjOzREQUMxRCx5QkFBeUI7NERBQ3pCLE1BQU11SSx3QkFBd0J6SSxVQUFVTSxnQkFBZ0IsQ0FBQzJILE1BQU0sQ0FBQ2IsQ0FBQUE7Z0VBQzlELE1BQU1zQixnQkFBZ0IvSSxpQkFBaUIwSCxHQUFHLENBQUNELGNBQWM7Z0VBQ3pELE9BQU9zQixnQkFBZ0JaOzREQUN6Qjs0REFFQSxNQUFNRSx1QkFBdUJTLHNCQUFzQlIsTUFBTSxDQUFDYixDQUFBQTtnRUFDeEQsTUFBTXNCLGdCQUFnQi9JLGlCQUFpQjBILEdBQUcsQ0FBQ0QsY0FBYztnRUFDekQsTUFBTXVCLGVBQWViLGtCQUFrQlk7Z0VBQ3ZDLE1BQU1SLHlCQUF5QnZJLGlCQUFpQjBILEdBQUcsQ0FBQ0Q7Z0VBQ3BELE9BQU9jLDJCQUEyQjdELGFBQWE2RCx5QkFBeUJTOzREQUMxRTs0REFFQSxJQUFJWCxxQkFBcUIxQyxNQUFNLEdBQUcsR0FBRztnRUFDbkNxSixrQkFBa0I7Z0VBQ2xCUixlQUFlOzREQUNqQjt3REFDRjt3REFFQSxJQUFJUSxpQkFBaUI7NERBQ25CLHFCQUNFLDhEQUFDaEM7Z0VBQUk3SSxPQUFPO29FQUNWK0ssWUFBWTtvRUFDWkMsUUFBUTtvRUFDUkMsY0FBYztvRUFDZEMsU0FBUztvRUFDVFYsV0FBVztvRUFDWFcsY0FBYztvRUFDZEMsU0FBUztvRUFDVEMsWUFBWTtvRUFDWkMsZ0JBQWdCO29FQUNoQkMsS0FBSztnRUFDUDs7a0ZBQ0UsOERBQUMxQzt3RUFBSTdJLE9BQU87NEVBQ1ZvTCxTQUFTOzRFQUNUQyxZQUFZOzRFQUNaRSxLQUFLOzRFQUNMQyxNQUFNO3dFQUNSO2tGQUNFLDRFQUFDZDs0RUFBSzFLLE9BQU87Z0ZBQ1hzSyxPQUFPO2dGQUNQQyxVQUFVO2dGQUNWa0IsWUFBWTs0RUFDZDs7Z0ZBQUc7Z0ZBQ0dwQjs7Ozs7Ozs7Ozs7O2tGQUdSLDhEQUFDbkI7d0VBQ0NsSixPQUFPOzRFQUNMK0ssWUFBWTs0RUFDWlQsT0FBTzs0RUFDUFUsUUFBUTs0RUFDUkMsY0FBYzs0RUFDZEMsU0FBUzs0RUFDVFgsVUFBVTs0RUFDVmtCLFlBQVk7NEVBQ1pDLFFBQVE7NEVBQ1JDLFlBQVk7NEVBQ1pDLFlBQVk7d0VBQ2Q7d0VBQ0FwQyxjQUFjLENBQUMzSTs0RUFDYkEsRUFBRWdMLGFBQWEsQ0FBQzdMLEtBQUssQ0FBQytLLFVBQVUsR0FBRzt3RUFDckM7d0VBQ0F0QixjQUFjLENBQUM1STs0RUFDYkEsRUFBRWdMLGFBQWEsQ0FBQzdMLEtBQUssQ0FBQytLLFVBQVUsR0FBRzt3RUFDckM7d0VBQ0E1QixTQUFTOzRFQUNQcEc7d0VBQ0Y7a0ZBQ0Q7Ozs7Ozs7Ozs7Ozt3REFLUDt3REFDQSxPQUFPO29EQUNUO29EQUlELENBQUNYLGtDQUNBLDhEQUFDa0g7d0RBQUdSLFdBQVU7a0VBQWdCOzs7Ozs7b0RBSS9CLENBQUMxRyxrQ0FDQSw4REFBQ3lHO3dEQUFJQyxXQUFVO2tFQUNaNU0sVUFBVUssZ0JBQWdCLEdBQ3pCLGVBQWU7c0VBQ2YsOERBQUNzTTs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUFnQjs7Ozs7OzhFQUcvQiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDNEI7NEVBQUs1QixXQUFVO3NGQUNiNU0sVUFBVUssZ0JBQWdCLENBQUN1UCxZQUFZLElBQUk1UCxVQUFVSyxnQkFBZ0IsQ0FBQ2dLLElBQUk7Ozs7OztzRkFFN0UsOERBQUNtRTs0RUFBSzVCLFdBQVU7c0ZBQWlCOzs7Ozs7Ozs7Ozs7OEVBRW5DLDhEQUFDSTtvRUFDQ0osV0FBVTtvRUFDVkssU0FBU3RHOzhFQUNWOzs7Ozs7OEVBR0QsOERBQUNxRztvRUFDQ0osV0FBVTtvRUFDVkssU0FBU3ZHOzhFQUNWOzs7Ozs7Ozs7Ozt3RUFLSCxlQUFlO3NFQUNmLDhEQUFDaUc7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFDQ0MsV0FBVTtvRUFDVkssU0FBUyxLQUF5Qjs7c0ZBRWxDLDhEQUFDTjs0RUFBSUMsV0FBVTtzRkFBYzs7Ozs7O3NGQUM3Qiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs4RkFBZTs7Ozs7OzhGQUM5Qiw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFHakMsOERBQUNEO29FQUNDQyxXQUFVO29FQUNWSyxTQUFTdEc7O3NGQUVULDhEQUFDZ0c7NEVBQUlDLFdBQVU7c0ZBQWM7Ozs7OztzRkFDN0IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQWU7Ozs7Ozs4RkFDOUIsOERBQUNEO29GQUFJQyxXQUFVOzhGQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBVTNDLFlBQVk7a0RBQ1osOERBQUNEO3dDQUFJQyxXQUFVOzswREFFYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDSTt3REFDQ0osV0FBVyxXQUFnRCxPQUFyQzVLLGNBQWMsU0FBUyxXQUFXO3dEQUN4RGlMLFNBQVMsSUFBTWhMLGFBQWE7a0VBQzdCOzs7Ozs7a0VBR0QsOERBQUMrSzt3REFDQ0osV0FBVyxXQUFxRCxPQUExQzVLLGNBQWMsY0FBYyxXQUFXO3dEQUM3RGlMLFNBQVMsSUFBTWhMLGFBQWE7a0VBQzdCOzs7Ozs7Ozs7Ozs7MERBS0gsOERBQUMwSztnREFBSUMsV0FBVTswREFDWjVLLGNBQWMsdUJBQ2IsOERBQUMySztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDYTtnRUFDQ0UsTUFBSztnRUFDTGYsV0FBVTtnRUFDVmdCLGFBQVk7Z0VBQ1puRyxPQUFPNUcsU0FBU0UsUUFBUTtnRUFDeEI4TSxVQUFVLENBQUNsSixJQUFNN0QsWUFBWWlCLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRWhCLFVBQVU0RCxFQUFFbEQsTUFBTSxDQUFDZ0csS0FBSzt3RUFBQzs7Ozs7Ozs7Ozs7c0VBSTlFLDhEQUFDa0Y7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNpRDtnRUFDQ2pELFdBQVU7Z0VBQ1ZnQixhQUFZO2dFQUNabkcsT0FBTzVHLFNBQVNHLGVBQWU7Z0VBQy9CNk0sVUFBVSxDQUFDbEosSUFBTTdELFlBQVlpQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUVmLGlCQUFpQjJELEVBQUVsRCxNQUFNLENBQUNnRyxLQUFLO3dFQUFDO2dFQUNqRnFJLE1BQU07Ozs7Ozs7Ozs7O3NFQUlWLDhEQUFDbkQ7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNEO2dFQUFJQyxXQUFVOzBFQUNaL0wsU0FBU0ksbUJBQW1CLENBQUNxRSxNQUFNLEtBQUssa0JBQ3ZDLDhEQUFDMEg7b0VBQ0NXLE1BQUs7b0VBQ0xmLFdBQVU7b0VBQ1ZLLFNBQVMsSUFBTW5NLFlBQVlpQixDQUFBQSxPQUFTO2dGQUFFLEdBQUdBLElBQUk7Z0ZBQUVkLHFCQUFxQjtvRkFBQztpRkFBRzs0RUFBQzs4RUFDMUU7Ozs7OzhGQUlEOztzRkFDRSw4REFBQzhFOzRFQUFNNkcsV0FBVTtzRkFBYTs7Ozs7O3dFQUM3Qi9MLFNBQVNJLG1CQUFtQixDQUFDdUUsR0FBRyxDQUFDLENBQUNvRixNQUFNbUYsc0JBQ3ZDLDhEQUFDcEQ7Z0ZBQWdCQyxXQUFVOztrR0FDekIsOERBQUNhO3dGQUNDRSxNQUFLO3dGQUNMZixXQUFVO3dGQUNWZ0IsYUFBYSxPQUFpQixPQUFWbUMsUUFBUTt3RkFDNUJ0SSxPQUFPbUQ7d0ZBQ1BpRCxVQUFVLENBQUNsSjs0RkFDVCxNQUFNcUwsV0FBVzttR0FBSW5QLFNBQVNJLG1CQUFtQjs2RkFBQzs0RkFDbEQrTyxRQUFRLENBQUNELE1BQU0sR0FBR3BMLEVBQUVsRCxNQUFNLENBQUNnRyxLQUFLOzRGQUNoQzNHLFlBQVlpQixDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVkLHFCQUFxQitPO2dHQUFTO3dGQUNoRTs7Ozs7O2tHQUVGLDhEQUFDaEQ7d0ZBQ0NXLE1BQUs7d0ZBQ0xmLFdBQVU7d0ZBQ1ZLLFNBQVM7NEZBQ1AsTUFBTStDLFdBQVduUCxTQUFTSSxtQkFBbUIsQ0FBQ2dILE1BQU0sQ0FBQyxDQUFDZ0ksR0FBR0MsSUFBTUEsTUFBTUg7NEZBQ3JFalAsWUFBWWlCLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRWQscUJBQXFCK087Z0dBQVM7d0ZBQ2hFO2tHQUNEOzs7Ozs7OytFQW5CT0Q7Ozs7O3NGQXdCWiw4REFBQy9DOzRFQUNDVyxNQUFLOzRFQUNMZixXQUFVOzRFQUNWSyxTQUFTLElBQU1uTSxZQUFZaUIsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFZCxxQkFBcUI7K0ZBQUljLEtBQUtkLG1CQUFtQjs0RkFBRTt5RkFBRztvRkFBQzs7OEZBRXRHLDhEQUFDdU47b0ZBQUsxSyxPQUFPO3dGQUFFdUssVUFBVTtvRkFBTzs4RkFBRzs7Ozs7O2dGQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQVNyRCw4REFBQzFCOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM3Rzt3RUFBTTZHLFdBQVU7a0ZBQWE7Ozs7OztrRkFDOUIsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0k7Z0ZBQ0NKLFdBQVcsbUJBQStELE9BQTVDL0wsU0FBU0ssUUFBUSxLQUFLLFFBQVEsV0FBVztnRkFDdkUrTCxTQUFTLElBQU1uTCxxQkFBcUI7MEZBQ3JDOzs7Ozs7MEZBR0QsOERBQUNrTDtnRkFDQ0osV0FBVyxtQkFBK0QsT0FBNUMvTCxTQUFTSyxRQUFRLEtBQUssUUFBUSxXQUFXO2dGQUN2RStMLFNBQVMsSUFBTW5MLHFCQUFxQjswRkFDckM7Ozs7OzswRkFHRCw4REFBQ2tMO2dGQUNDSixXQUFXLG1CQUE4RCxPQUEzQy9MLFNBQVNLLFFBQVEsS0FBSyxPQUFPLFdBQVc7Z0ZBQ3RFK0wsU0FBUyxJQUFNbkwscUJBQXFCOzBGQUNyQzs7Ozs7OzBGQUdELDhEQUFDa0w7Z0ZBQ0NKLFdBQVcsbUJBQThELE9BQTNDL0wsU0FBU0ssUUFBUSxLQUFLLE9BQU8sV0FBVztnRkFDdEUrTCxTQUFTLElBQU1uTCxxQkFBcUI7MEZBQ3JDOzs7Ozs7MEZBR0QsOERBQUNrTDtnRkFDQ0osV0FBVyxtQkFBK0QsT0FBNUMvTCxTQUFTSyxRQUFRLEtBQUssUUFBUSxXQUFXO2dGQUN2RStMLFNBQVMsSUFBTW5MLHFCQUFxQjswRkFDckM7Ozs7Ozs7Ozs7OztrRkFJSCw4REFBQ3FPO3dFQUFFdkQsV0FBVTtrRkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBS25DLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3dEOzhFQUFHOzs7Ozs7OEVBQ0osOERBQUNEO29FQUFFdkQsV0FBVTs4RUFBWTs7Ozs7OzhFQUN6Qiw4REFBQ0Q7b0VBQ0NDLFdBQVU7b0VBQ1ZDLFNBQVM5TyxzREFBaUJBO29FQUMxQnNTLGFBQWEzTDtvRUFDYjRMLGFBQWF6TDtvRUFDYjBMLFdBQVd4TDtvRUFDWHdJLGNBQWN2STtvRUFDZGxCLE9BQU87d0VBQ0wwTSxXQUFXO3dFQUNYaEIsUUFBUTt3RUFDUmlCLFlBQVk7b0VBQ2Q7OEVBRUNqTyw2QkFDQyw4REFBQ21LO3dFQUNDQyxXQUFVO3dFQUNWOUksT0FBTzs0RUFBRTBNLFdBQVc7d0VBQVE7OzBGQUU1Qiw4REFBQzdEO2dGQUFJQyxXQUFVOzs7Ozs7MEZBQ2YsOERBQUM0QjswRkFBSzs7Ozs7Ozs7Ozs7b0ZBRU50TSxNQUFNb0QsTUFBTSxHQUFHLGtCQUNqQiw4REFBQ3FIO3dFQUFJQyxXQUFVOzs0RUFFWjFLLE1BQ0V3TyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7Z0ZBQ1IsTUFBTUMsWUFBWXpPLGdCQUFnQjBPLFFBQVEsQ0FBQ0gsRUFBRXZMLEVBQUU7Z0ZBQy9DLE1BQU0yTCxZQUFZM08sZ0JBQWdCME8sUUFBUSxDQUFDRixFQUFFeEwsRUFBRTtnRkFDL0MsV0FBVztnRkFDWCxJQUFJeUwsYUFBYSxDQUFDRSxXQUFXLE9BQU8sQ0FBQztnRkFDckMsSUFBSSxDQUFDRixhQUFhRSxXQUFXLE9BQU87Z0ZBQ3BDLE9BQU87NEVBQ1QsR0FDQ3ZMLEdBQUcsQ0FBQyxDQUFDd0wscUJBQ04sOERBQUNyRTtvRkFFQ0MsV0FBVyxhQUFpRSxPQUFwRHhLLGdCQUFnQjBPLFFBQVEsQ0FBQ0UsS0FBSzVMLEVBQUUsSUFBSSxhQUFhO29GQUN6RTZILFNBQVMsSUFBTXpJLHFCQUFxQndNLEtBQUs1TCxFQUFFOztzR0FHM0MsOERBQUN1SDs0RkFBSUMsV0FBVTs7Z0dBQ1pvRSxLQUFLQyxVQUFVLElBQUlELEtBQUtFLGVBQWUsaUJBQ3RDLDhEQUFDQztvR0FDQ0MsS0FBS3hULG9EQUFXQSxDQUFDb1QsS0FBS0MsVUFBVSxJQUFJRCxLQUFLRSxlQUFlO29HQUN4REcsS0FBS0wsS0FBS00sS0FBSztvR0FDZjFFLFdBQVU7b0dBQ1YyRSxTQUFTLENBQUM1TTt3R0FDUkEsRUFBRWdMLGFBQWEsQ0FBQzdMLEtBQUssQ0FBQ29MLE9BQU8sR0FBRzt3R0FDaEMsTUFBTXNDLGNBQWM3TSxFQUFFZ0wsYUFBYSxDQUFDOEIsa0JBQWtCO3dHQUN0RCxJQUFJRCxhQUFhOzRHQUNmQSxZQUFZMU4sS0FBSyxDQUFDb0wsT0FBTyxHQUFHO3dHQUM5QjtvR0FDRjs7Ozs7Z0hBRUE7OEdBQ0osOERBQUN2QztvR0FBSUMsV0FBVTtvR0FBbUI5SSxPQUFPO3dHQUFFb0wsU0FBUzhCLEtBQUtDLFVBQVUsSUFBSUQsS0FBS0UsZUFBZSxHQUFHLFNBQVM7b0dBQU87OEdBQzVHLDRFQUFDUTt3R0FBSUMsT0FBTTt3R0FBS0MsUUFBTzt3R0FBS0MsU0FBUTt3R0FBWUMsTUFBSzt3R0FBT0MsT0FBTTs7MEhBQ2hFLDhEQUFDQztnSEFBS0MsR0FBRTtnSEFBMEhDLFFBQU87Z0hBQWVDLGFBQVk7Ozs7OzswSEFDcEssOERBQUNIO2dIQUFLQyxHQUFFO2dIQUEyR0gsTUFBSzs7Ozs7OzBIQUN4SCw4REFBQ0U7Z0hBQUtDLEdBQUU7Z0hBQXlCQyxRQUFPO2dIQUFlQyxhQUFZO2dIQUFJQyxnQkFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEdBSTFGLDhEQUFDekY7b0dBQUlDLFdBQVcsdUJBQTJFLE9BQXBEeEssZ0JBQWdCME8sUUFBUSxDQUFDRSxLQUFLNUwsRUFBRSxJQUFJLGFBQWE7OEdBQ3RGLDRFQUFDc007d0dBQUlDLE9BQU07d0dBQUtDLFFBQU87d0dBQUtDLFNBQVE7d0dBQVlDLE1BQUs7d0dBQU9DLE9BQU07a0hBQ2hFLDRFQUFDQzs0R0FBS0MsR0FBRTs0R0FBb0JDLFFBQU87NEdBQWVDLGFBQVk7NEdBQUlFLGVBQWM7NEdBQVFELGdCQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NHQUs3Ryw4REFBQ3pGOzRGQUFJQyxXQUFVO3NHQUNiLDRFQUFDRDtnR0FBSUMsV0FBVTswR0FBY29FLEtBQUtNLEtBQUssSUFBSU4sS0FBSzNHLElBQUksSUFBSTJHLEtBQUtzQixRQUFRLElBQUk7Ozs7Ozs7Ozs7OzttRkFwQ3RFdEIsS0FBSzVMLEVBQUU7Ozs7OzRFQXlDZnhDLHlCQUNDLDhEQUFDK0o7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUNJO29GQUNDSixXQUFVO29GQUNWSyxTQUFTM0k7b0ZBQ1RpTyxVQUFVelA7OEZBRVRBLDRCQUNDOzswR0FDRSw4REFBQzZKO2dHQUFJQyxXQUFVOzs7Ozs7MEdBQ2YsOERBQUM0QjswR0FBSzs7Ozs7OztxSEFHUjs7MEdBQ0UsOERBQUNBOzBHQUFLOzs7Ozs7MEdBQ04sOERBQUNrRDtnR0FBSUMsT0FBTTtnR0FBS0MsUUFBTztnR0FBS0MsU0FBUTtnR0FBWUMsTUFBSztnR0FBT0MsT0FBTTswR0FDaEUsNEVBQUNDO29HQUFLQyxHQUFFO29HQUE0QkMsUUFBTztvR0FBZUMsYUFBWTtvR0FBSUUsZUFBYztvR0FBUUQsZ0JBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBUzdILDhEQUFDekY7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNEOzRFQUFJQyxXQUFVO3NGQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQU1wQyw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDd0Q7OEVBQUc7Ozs7Ozs4RUFDSiw4REFBQ3pEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ2E7NEVBQ0NFLE1BQUs7NEVBQ0w2RSxRQUFROzRFQUNSQyxRQUFPOzRFQUNQNUUsVUFBVSxDQUFDbEo7Z0ZBQ1QsSUFBSUEsRUFBRWxELE1BQU0sQ0FBQ2lSLEtBQUssRUFBRTtvRkFDbEIsTUFBTUEsUUFBUUMsTUFBTUMsSUFBSSxDQUFDak8sRUFBRWxELE1BQU0sQ0FBQ2lSLEtBQUs7b0ZBQ3ZDLE1BQU1HLGFBQXFCLEVBQUU7b0ZBQzdCLE1BQU1DLGVBQXlCLEVBQUU7b0ZBRWpDLFVBQVU7b0ZBQ1YsTUFBTUMsZUFBZTt3RkFDbkI7d0ZBQWM7d0ZBQWE7d0ZBQWE7d0ZBQ3hDO3dGQUNBO3dGQUFzQjt3RkFDdEI7d0ZBQTRCO3dGQUM1Qjt3RkFBaUM7d0ZBQ2pDO3FGQUNEO29GQUVELGtCQUFrQjtvRkFDbEIsTUFBTUMsb0JBQW9CO3dGQUFDO3dGQUFRO3dGQUFTO3dGQUFRO3dGQUFRO3dGQUFRO3dGQUFRO3dGQUFTO3dGQUFRO3dGQUFTO3dGQUFRO3dGQUFTO3FGQUFPO29GQUU5SE4sTUFBTXJILE9BQU8sQ0FBQ2pCLENBQUFBOzRGQVFnQkE7d0ZBUDVCLHdDQUF3Qzt3RkFDeEMsSUFBSUEsS0FBSzhDLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTs0RkFDaEM0RixhQUFhaEksSUFBSSxDQUFDLEdBQWEsT0FBVlYsS0FBS0MsSUFBSSxFQUFDOzRGQUMvQjt3RkFDRjt3RkFFQSxTQUFTO3dGQUNULE1BQU00SSxnQkFBZ0IsUUFBTTdJLHVCQUFBQSxLQUFLQyxJQUFJLENBQUM2SSxLQUFLLENBQUMsS0FBS0MsR0FBRyxnQkFBeEIvSSwyQ0FBQUEscUJBQTRCZ0osV0FBVzt3RkFDbkUsTUFBTUMsY0FBY04sYUFBYWpDLFFBQVEsQ0FBQzFHLEtBQUt1RCxJQUFJLEtBQUtxRixrQkFBa0JsQyxRQUFRLENBQUNtQzt3RkFFbkYsSUFBSSxDQUFDSSxhQUFhOzRGQUNoQlAsYUFBYWhJLElBQUksQ0FBQyxHQUFhLE9BQVZWLEtBQUtDLElBQUksRUFBQzs0RkFDL0I7d0ZBQ0Y7d0ZBRUF3SSxXQUFXL0gsSUFBSSxDQUFDVjtvRkFDbEI7b0ZBRUEsU0FBUztvRkFDVCxJQUFJeUksV0FBV3ZOLE1BQU0sR0FBRyxHQUFHO3dGQUN6Qi9DLGVBQWVSLENBQUFBLE9BQVE7bUdBQUlBO21HQUFTOFE7NkZBQVc7b0ZBQ2pEO29GQUVBLFNBQVM7b0ZBQ1QsSUFBSUMsYUFBYXhOLE1BQU0sR0FBRyxHQUFHO3dGQUMzQmdPLE1BQU0sY0FBc0MsT0FBeEJSLGFBQWF0SyxJQUFJLENBQUM7b0ZBQ3hDO29GQUVBLDZCQUE2QjtvRkFDN0I3RCxFQUFFbEQsTUFBTSxDQUFDZ0csS0FBSyxHQUFHO2dGQUNuQjs0RUFDRjs0RUFDQTNELE9BQU87Z0ZBQUVvTCxTQUFTOzRFQUFPOzRFQUN6QjlKLElBQUc7Ozs7OztzRkFFTCw4REFBQ1c7NEVBQU13TixTQUFROzRFQUFjM0csV0FBVTtzRkFBYTs7Ozs7O3NGQUdwRCw4REFBQzRCOzRFQUFLNUIsV0FBVTtzRkFBbUI7Ozs7Ozs7Ozs7OztnRUFJcEN0SyxZQUFZZ0QsTUFBTSxHQUFHLG1CQUNwQiw4REFBQ3FIO29FQUFJQyxXQUFVOzhFQUNadEssWUFBWWtELEdBQUcsQ0FBQyxDQUFDNEUsTUFBTTJGLHNCQUN0Qiw4REFBQ3BEOzRFQUFnQkMsV0FBVTs7OEZBQ3pCLDhEQUFDNEI7b0ZBQUs1QixXQUFVOzhGQUFheEMsS0FBS0MsSUFBSTs7Ozs7OzhGQUN0Qyw4REFBQzJDO29GQUNDQyxTQUFTLElBQU0xSyxlQUFlUixDQUFBQSxPQUFRQSxLQUFLa0csTUFBTSxDQUFDLENBQUNnSSxHQUFHQyxJQUFNQSxNQUFNSDtvRkFDbEVuRCxXQUFVOzhGQUNYOzs7Ozs7OzJFQUxPbUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFvQjVCLDhEQUFDcEQ7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDSTtnREFBT0osV0FBVTtnREFBV0ssU0FBU3BFOzBEQUFnQjs7Ozs7OzBEQUd0RCw4REFBQ21FO2dEQUNDSixXQUFXLFlBVVA1SixPQVRGLGdCQUFpQixjQUNma0QsQ0FBQUEsbUJBQ0lsRyxVQUFVRSxvQkFBb0IsR0FDOUIyRixtQkFDRTdGLFVBQVVLLGdCQUFnQixHQUN6QkwsVUFBVUssZ0JBQWdCLElBQUlMLFVBQVVFLG9CQUFvQixLQUVwRVMsZ0JBQWdCLGFBQWFFLFNBQVNFLFFBQVEsQ0FBQ2dJLElBQUksTUFBTSxDQUFDL0YsZUFDekQsWUFBWSxZQUNmLEtBQW9DLE9BQWpDQSxlQUFlLGVBQWU7Z0RBQ2xDaUssU0FBU3RNLGdCQUFnQixhQUFhK0csYUFBYW9CO2dEQUNuRHlKLFVBQ0U1UixnQkFBZ0IsYUFDWHVGLG1CQUNHLENBQUNsRyxVQUFVRSxvQkFBb0IsR0FDL0IyRixtQkFDRSxDQUFDN0YsVUFBVUssZ0JBQWdCLEdBQzFCLENBQUNMLFVBQVVLLGdCQUFnQixJQUFJLENBQUNMLFVBQVVFLG9CQUFvQixHQUNyRSxDQUFDVyxTQUFTRSxRQUFRLENBQUNnSSxJQUFJLE1BQU0vRjswREFHbENyQyxnQkFBZ0IsYUFDZixlQUFnQixVQUFVakMsZUFBZSxTQUFVLFFBQVEsT0FFM0RzRSw2QkFDRSw4REFBQ3dMO29EQUFLNUIsV0FBVTs7c0VBQ2QsOERBQUM0Qjs0REFBSzVCLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7OztnRUFHakM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTZCw4REFBQ3pQLDREQUFtQkE7Z0JBQ2xCbUIsUUFBUWU7Z0JBQ1JkLFNBQVNxSTtnQkFDVDRNLGtCQUFrQmhOOzs7Ozs7MEJBSXBCLDhEQUFDcEosNkVBQXVCQTtnQkFDdEJxVyxNQUFNbFU7Z0JBQ05tVSxvQkFBb0IxVCxVQUFVTSxnQkFBZ0I7Z0JBQzlDYixVQUFVQTtnQkFDVmxCLFNBQVN1STtnQkFDVDZNLFdBQVc1TTs7Ozs7Ozs7Ozs7O0FBSW5CO0dBcjlDTTFJO0tBQUFBO0FBdTlDTiwrREFBZUEsc0JBQXNCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9UZW1wbGF0ZVNlbGVjdGlvbk1vZGFsLnRzeD9hMzU3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IFgsIEFycm93TGVmdCwgVXNlcnMsIENoZWNrQ2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IHsgR2V0Tm90aWZpY2F0aW9uIH0gZnJvbSAnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nO1xyXG5pbXBvcnQgVGVtcGxhdGVQaWNrZXJNb2RhbCBmcm9tICcuL1RlbXBsYXRlUGlja2VyTW9kYWwnO1xyXG5pbXBvcnQgeyBCYXRjaFVzZUtleVBhY2thZ2VNb2RhbCB9IGZyb20gJy4vQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWwnO1xyXG5cclxuaW1wb3J0ICcuL1RlbXBsYXRlUGlja2VyTW9kYWwuY3NzJztcclxuaW1wb3J0ICcuL05ld1B1Ymxpc2hUYXNrTW9kYWwuY3NzJztcclxuaW1wb3J0ICcuL1RlbXBsYXRlU2VsZWN0aW9uTW9kYWwuY3NzJztcclxuXHJcbi8vIOWvvOWFpUFQSVxyXG5pbXBvcnQgeyBwb2ludHNBcGkgfSBmcm9tICdAL2xpYi9hcGkvcG9pbnRzJztcclxuaW1wb3J0IHRhc2tBcGksIHsgVGFza1R5cGUsIFByaW9yaXR5IH0gZnJvbSAnQC9saWIvYXBpL3Rhc2snO1xyXG5pbXBvcnQgeyBiYXRjaEFkZFVzZXJKb2luUm9sZSB9IGZyb20gJ0AvbGliL2FwaS9yb2xlJztcclxuXHJcbi8vIOWvvOWFpeW3peWFt+WHveaVsFxyXG5pbXBvcnQge1xyXG4gIFN0dWRlbnQsXHJcbiAgZmV0Y2hDbGFzc1N0dWRlbnRzV2l0aE5vdGlmaWNhdGlvbixcclxuICBmZXRjaFN0dWRlbnRQb2ludHMsXHJcbiAgV29ya3NTdGF0ZSxcclxuICBEcmFnU3RhdGUsXHJcbiAgZml4SW1hZ2VVcmwsXHJcbiAgZmV0Y2hXb3JrcyxcclxuICBoYW5kbGVTZWxlY3RXb3JrLFxyXG4gIGhhbmRsZVdoZWVsU2Nyb2xsLFxyXG4gIGhhbmRsZU1vdXNlRG93bixcclxuICBoYW5kbGVNb3VzZU1vdmUsXHJcbiAgaGFuZGxlTW91c2VVcCxcclxuICBoYW5kbGVNb3VzZUxlYXZlLFxyXG4gIGdldFRhc2tUaW1lc1xyXG59IGZyb20gJy4uL3V0aWxzJztcclxuXHJcbmludGVyZmFjZSBUZW1wbGF0ZVNlbGVjdGlvbk1vZGFsUHJvcHMge1xyXG4gIGlzT3BlbjogYm9vbGVhbjtcclxuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xyXG4gIG9uQmFjazogKCkgPT4gdm9pZDtcclxuICBvbkNvbmZpcm0/OiAodGFza0RhdGE6IGFueSkgPT4gdm9pZDtcclxuICBhY3Rpb25UeXBlOiBzdHJpbmc7XHJcbiAgc2VsZWN0ZWRTY2hvb2w6IGFueTtcclxuICBzZWxlY3RlZENsYXNzOiBhbnk7XHJcbn1cclxuXHJcblxyXG5cclxuY29uc3QgVGVtcGxhdGVTZWxlY3Rpb25Nb2RhbDogUmVhY3QuRkM8VGVtcGxhdGVTZWxlY3Rpb25Nb2RhbFByb3BzPiA9ICh7XHJcbiAgaXNPcGVuLFxyXG4gIG9uQ2xvc2UsXHJcbiAgb25CYWNrLFxyXG4gIG9uQ29uZmlybSxcclxuICBhY3Rpb25UeXBlLFxyXG4gIHNlbGVjdGVkU2Nob29sLFxyXG4gIHNlbGVjdGVkQ2xhc3NcclxufSkgPT4ge1xyXG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaG92ZXJlZE9wdGlvbiwgc2V0SG92ZXJlZE9wdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcclxuICBjb25zdCBbZm9jdXNlZElucHV0LCBzZXRGb2N1c2VkSW5wdXRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7IC8vIOi3n+i4quWTquS4qui+k+WFpeahhuacieeEpueCuVxyXG5cclxuICAvLyDovpPlhaXmoYblvJXnlKhcclxuICBjb25zdCBhc3NpZ25JbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBkaXN0cmlidXRlSW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XHJcbiAgLy8g56e76Zmk5pWZ5biI6IO96YeP55u45YWz54q25oCB77yM5Zug5Li65LiN6ZyA6KaB5qOA5p+l5pWZ5biI6IO96YeP5rGgXHJcbiAgY29uc3QgW2lzVGVtcGxhdGVQaWNrZXJPcGVuLCBzZXRJc1RlbXBsYXRlUGlja2VyT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IFtpc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsT3Blbiwgc2V0SXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyDlrabnlJ/nm7jlhbPnirbmgIFcclxuICBjb25zdCBbc3R1ZGVudHMsIHNldFN0dWRlbnRzXSA9IHVzZVN0YXRlPFN0dWRlbnRbXT4oW10pO1xyXG4gIGNvbnN0IFtzdHVkZW50UG9pbnRzTWFwLCBzZXRTdHVkZW50UG9pbnRzTWFwXSA9IHVzZVN0YXRlPE1hcDxudW1iZXIsIG51bWJlcj4+KG5ldyBNYXAoKSk7XHJcbiAgY29uc3QgW2xvYWRpbmdTdHVkZW50UG9pbnRzLCBzZXRMb2FkaW5nU3R1ZGVudFBvaW50c10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIC8vIOWtmOWCqOaooeaAgeahhuaVsOaNrueahOeKtuaAgVxyXG4gIGNvbnN0IFttb2RhbERhdGEsIHNldE1vZGFsRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBzZWxlY3RlZERpc3RyaWJ1dGlvbjogJ25vbmUnLCAvLyDpu5jorqTpgInmi6lcIuS4jeWIhumFjVwiXHJcbiAgICBhc3NpZ25FbmVyZ3lBbW91bnQ6ICcnLCAvLyDliIbphY3mjInpkq7nmoTog73ph4/mlbDlgLxcclxuICAgIGRpc3RyaWJ1dGVFbmVyZ3lBbW91bnQ6ICcnLCAvLyDliIbphY3oh7PmjInpkq7nmoTog73ph4/mlbDlgLxcclxuICAgIHNlbGVjdGVkVGVtcGxhdGU6IG51bGwgYXMgYW55LFxyXG4gICAgc2VsZWN0ZWRTdHVkZW50czogW10gYXMgbnVtYmVyW10sIC8vIOm7mOiupOS4uuaJgOacieWtpueUn1xyXG4gIH0pO1xyXG5cclxuICAvLyDovpPlhaXpqozor4HplJnor6/nirbmgIFcclxuICBjb25zdCBbaW5wdXRFcnJvcnMsIHNldElucHV0RXJyb3JzXSA9IHVzZVN0YXRlKHtcclxuICAgIGFzc2lnbkVuZXJneUVycm9yOiAnJyxcclxuICAgIGRpc3RyaWJ1dGVFbmVyZ3lFcnJvcjogJydcclxuICB9KTtcclxuXHJcbiAgLy8g5b2T5YmN5q2l6aqk54q25oCBXHJcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZTwndGVtcGxhdGUnIHwgJ3B1Ymxpc2gnPigndGVtcGxhdGUnKTtcclxuXHJcbiAgLy8g5Y+R5biD5Lu75Yqh55u45YWz54q25oCBXHJcbiAgY29uc3QgW3Rhc2tEYXRhLCBzZXRUYXNrRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICB0YXNrTmFtZTogJycsXHJcbiAgICB0YXNrRGVzY3JpcHRpb246ICcnLFxyXG4gICAgc2VsZkFzc2Vzc21lbnRJdGVtczogW10gYXMgc3RyaW5nW10sXHJcbiAgICBkdXJhdGlvbjogJzHlsI/ml7YnLCAvLyDpu5jorqTmjIHnu63ml7bpl7RcclxuICAgIHN0YXJ0VGltZTogJycsXHJcbiAgICBlbmRUaW1lOiAnJ1xyXG4gIH0pO1xyXG5cclxuICAvLyDmjIHnu63ml7bpl7TpgInmi6nlmajnirbmgIFcclxuICBjb25zdCBbc2hvd0R1cmF0aW9uU2VsZWN0b3IsIHNldFNob3dEdXJhdGlvblNlbGVjdG9yXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8g54K55Ye75aSW6YOo5YWz6Zet5oyB57ut5pe26Ze06YCJ5oup5ZmoXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZUNsaWNrT3V0c2lkZSA9IChldmVudDogTW91c2VFdmVudCkgPT4ge1xyXG4gICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQgYXMgRWxlbWVudDtcclxuICAgICAgaWYgKHNob3dEdXJhdGlvblNlbGVjdG9yICYmICF0YXJnZXQuY2xvc2VzdCgnLmR1cmF0aW9uLWZpZWxkLWNvbnRhaW5lcicpKSB7XHJcbiAgICAgICAgc2V0U2hvd0R1cmF0aW9uU2VsZWN0b3IoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGlmIChzaG93RHVyYXRpb25TZWxlY3Rvcikge1xyXG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xyXG4gICAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcclxuICAgIH1cclxuICB9LCBbc2hvd0R1cmF0aW9uU2VsZWN0b3JdKTtcclxuXHJcblxyXG5cclxuICAvLyDmjIHnu63ml7bpl7TpgInmi6nlh73mlbBcclxuICBjb25zdCBoYW5kbGVEdXJhdGlvblNlbGVjdCA9IChkdXJhdGlvbjogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRUYXNrRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGR1cmF0aW9uIH0pKTtcclxuICB9O1xyXG5cclxuXHJcblxyXG5cclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8J3Rhc2snIHwgJ3Jlc291cmNlcyc+KCd0YXNrJyk7XHJcbiAgY29uc3QgW3dvcmtzLCBzZXRXb3Jrc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFdvcmtJZHMsIHNldFNlbGVjdGVkV29ya0lkc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xyXG4gIGNvbnN0IFthdHRhY2htZW50cywgc2V0QXR0YWNobWVudHNdID0gdXNlU3RhdGU8RmlsZVtdPihbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmdXb3Jrcywgc2V0TG9hZGluZ1dvcmtzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbaGFzTW9yZSwgc2V0SGFzTW9yZV0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbbG9hZGluZ01vcmUsIHNldExvYWRpbmdNb3JlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8g5Y+R5biD5Lu75Yqh5Yqg6L2954q25oCBXHJcbiAgY29uc3QgW2lzUHVibGlzaGluZywgc2V0SXNQdWJsaXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8g6byg5qCH5ouW5ou95rua5Yqo54q25oCBXHJcbiAgY29uc3QgW2lzRHJhZ2dpbmcsIHNldElzRHJhZ2dpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtkcmFnU3RhcnQsIHNldERyYWdTdGFydF0gPSB1c2VTdGF0ZSh7IHg6IDAsIHNjcm9sbExlZnQ6IDAgfSk7XHJcbiAgY29uc3QgcGFnZVNpemUgPSAxMDtcclxuXHJcbiAgLy8g6Zi75q2i6IOM5pmv6aG16Z2i5rua5YqoXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc09wZW4pIHtcclxuICAgICAgLy8g5L+d5a2Y5Y6f5aeL55qEIG92ZXJmbG93IOagt+W8j1xyXG4gICAgICBjb25zdCBvcmlnaW5hbFN0eWxlID0gd2luZG93LmdldENvbXB1dGVkU3R5bGUoZG9jdW1lbnQuYm9keSkub3ZlcmZsb3c7XHJcbiAgICAgIC8vIOmYu+atouiDjOaZr+a7muWKqFxyXG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ2hpZGRlbic7XHJcblxyXG4gICAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAgIC8vIOaBouWkjeWOn+Wni+agt+W8j1xyXG4gICAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSBvcmlnaW5hbFN0eWxlO1xyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sIFtpc09wZW5dKTtcclxuXHJcblxyXG5cclxuICAvLyDojrflj5bkvZzlk4HliJfooajvvIjliIbpobXmh5LliqDovb3vvIlcclxuICBjb25zdCBmZXRjaFdvcmtzRGF0YSA9IGFzeW5jIChwYWdlTnVtOiBudW1iZXIgPSAxLCBpc0xvYWRNb3JlOiBib29sZWFuID0gZmFsc2UpID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRTdGF0ZTogV29ya3NTdGF0ZSA9IHtcclxuICAgICAgd29ya3MsXHJcbiAgICAgIHNlbGVjdGVkV29ya0lkcyxcclxuICAgICAgcGFnZSxcclxuICAgICAgaGFzTW9yZSxcclxuICAgICAgbG9hZGluZ1dvcmtzLFxyXG4gICAgICBsb2FkaW5nTW9yZVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBzZXRTdGF0ZSA9IChuZXdTdGF0ZTogUGFydGlhbDxXb3Jrc1N0YXRlPikgPT4ge1xyXG4gICAgICBpZiAobmV3U3RhdGUud29ya3MgIT09IHVuZGVmaW5lZCkgc2V0V29ya3MobmV3U3RhdGUud29ya3MpO1xyXG4gICAgICBpZiAobmV3U3RhdGUuc2VsZWN0ZWRXb3JrSWRzICE9PSB1bmRlZmluZWQpIHNldFNlbGVjdGVkV29ya0lkcyhuZXdTdGF0ZS5zZWxlY3RlZFdvcmtJZHMpO1xyXG4gICAgICBpZiAobmV3U3RhdGUucGFnZSAhPT0gdW5kZWZpbmVkKSBzZXRQYWdlKG5ld1N0YXRlLnBhZ2UpO1xyXG4gICAgICBpZiAobmV3U3RhdGUuaGFzTW9yZSAhPT0gdW5kZWZpbmVkKSBzZXRIYXNNb3JlKG5ld1N0YXRlLmhhc01vcmUpO1xyXG4gICAgICBpZiAobmV3U3RhdGUubG9hZGluZ1dvcmtzICE9PSB1bmRlZmluZWQpIHNldExvYWRpbmdXb3JrcyhuZXdTdGF0ZS5sb2FkaW5nV29ya3MpO1xyXG4gICAgICBpZiAobmV3U3RhdGUubG9hZGluZ01vcmUgIT09IHVuZGVmaW5lZCkgc2V0TG9hZGluZ01vcmUobmV3U3RhdGUubG9hZGluZ01vcmUpO1xyXG4gICAgfTtcclxuXHJcbiAgICBhd2FpdCBmZXRjaFdvcmtzKHBhZ2VOdW0sIHBhZ2VTaXplLCBpc0xvYWRNb3JlLCBjdXJyZW50U3RhdGUsIHNldFN0YXRlKTtcclxuICB9O1xyXG5cclxuICAvLyDliqDovb3mm7TlpJrkvZzlk4FcclxuICBjb25zdCBsb2FkTW9yZVdvcmtzRGF0YSA9ICgpID0+IHtcclxuICAgIGlmICghbG9hZGluZ01vcmUgJiYgaGFzTW9yZSkge1xyXG4gICAgICBjb25zdCBuZXh0UGFnZSA9IHBhZ2UgKyAxO1xyXG4gICAgICBmZXRjaFdvcmtzRGF0YShuZXh0UGFnZSwgdHJ1ZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g6YCJ5oup5L2c5ZOB77yI5pSv5oyB5aSa6YCJ77yJXHJcbiAgY29uc3QgaGFuZGxlU2VsZWN0V29ya0RhdGEgPSAod29ya0lkOiBudW1iZXIpID0+IHtcclxuICAgIGNvbnN0IHNldFN0YXRlID0gKG5ld1N0YXRlOiBQYXJ0aWFsPFdvcmtzU3RhdGU+KSA9PiB7XHJcbiAgICAgIGlmIChuZXdTdGF0ZS5zZWxlY3RlZFdvcmtJZHMgIT09IHVuZGVmaW5lZCkgc2V0U2VsZWN0ZWRXb3JrSWRzKG5ld1N0YXRlLnNlbGVjdGVkV29ya0lkcyk7XHJcbiAgICB9O1xyXG5cclxuICAgIGhhbmRsZVNlbGVjdFdvcmsod29ya0lkLCBzZWxlY3RlZFdvcmtJZHMsIHNldFN0YXRlKTtcclxuICB9O1xyXG5cclxuXHJcblxyXG4gIC8vIOWIm+W7uum8oOagh+WkhOeQhuWHveaVsOeahOWMheijheWZqFxyXG4gIGNvbnN0IGhhbmRsZU1vdXNlRG93bldyYXBwZXIgPSAoZTogUmVhY3QuTW91c2VFdmVudDxIVE1MRGl2RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHNldERyYWdTdGF0ZSA9IChuZXdTdGF0ZTogUGFydGlhbDxEcmFnU3RhdGU+KSA9PiB7XHJcbiAgICAgIGlmIChuZXdTdGF0ZS5pc0RyYWdnaW5nICE9PSB1bmRlZmluZWQpIHNldElzRHJhZ2dpbmcobmV3U3RhdGUuaXNEcmFnZ2luZyk7XHJcbiAgICAgIGlmIChuZXdTdGF0ZS5kcmFnU3RhcnQgIT09IHVuZGVmaW5lZCkgc2V0RHJhZ1N0YXJ0KG5ld1N0YXRlLmRyYWdTdGFydCk7XHJcbiAgICB9O1xyXG4gICAgaGFuZGxlTW91c2VEb3duKGUsIHNldERyYWdTdGF0ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTW91c2VNb3ZlV3JhcHBlciA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxEaXZFbGVtZW50PikgPT4ge1xyXG4gICAgY29uc3QgZHJhZ1N0YXRlOiBEcmFnU3RhdGUgPSB7IGlzRHJhZ2dpbmcsIGRyYWdTdGFydCB9O1xyXG4gICAgaGFuZGxlTW91c2VNb3ZlKGUsIGRyYWdTdGF0ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTW91c2VVcFdyYXBwZXIgPSAoZTogUmVhY3QuTW91c2VFdmVudDxIVE1MRGl2RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHNldERyYWdTdGF0ZSA9IChuZXdTdGF0ZTogUGFydGlhbDxEcmFnU3RhdGU+KSA9PiB7XHJcbiAgICAgIGlmIChuZXdTdGF0ZS5pc0RyYWdnaW5nICE9PSB1bmRlZmluZWQpIHNldElzRHJhZ2dpbmcobmV3U3RhdGUuaXNEcmFnZ2luZyk7XHJcbiAgICAgIGlmIChuZXdTdGF0ZS5kcmFnU3RhcnQgIT09IHVuZGVmaW5lZCkgc2V0RHJhZ1N0YXJ0KG5ld1N0YXRlLmRyYWdTdGFydCk7XHJcbiAgICB9O1xyXG4gICAgaGFuZGxlTW91c2VVcChlLCBzZXREcmFnU3RhdGUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZU1vdXNlTGVhdmVXcmFwcGVyID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTERpdkVsZW1lbnQ+KSA9PiB7XHJcbiAgICBjb25zdCBzZXREcmFnU3RhdGUgPSAobmV3U3RhdGU6IFBhcnRpYWw8RHJhZ1N0YXRlPikgPT4ge1xyXG4gICAgICBpZiAobmV3U3RhdGUuaXNEcmFnZ2luZyAhPT0gdW5kZWZpbmVkKSBzZXRJc0RyYWdnaW5nKG5ld1N0YXRlLmlzRHJhZ2dpbmcpO1xyXG4gICAgICBpZiAobmV3U3RhdGUuZHJhZ1N0YXJ0ICE9PSB1bmRlZmluZWQpIHNldERyYWdTdGFydChuZXdTdGF0ZS5kcmFnU3RhcnQpO1xyXG4gICAgfTtcclxuICAgIGhhbmRsZU1vdXNlTGVhdmUoZSwgc2V0RHJhZ1N0YXRlKTtcclxuICB9O1xyXG5cclxuICAvLyDlvZPliIfmjaLliLDlj5HluIPku7vliqHmraXpqqTkuJTpgInmi6notYTmupDmoIfnrb7pobXml7bojrflj5bkvZzlk4HliJfooahcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwID09PSAncHVibGlzaCcgJiYgYWN0aXZlVGFiID09PSAncmVzb3VyY2VzJyAmJiBtb3VudGVkKSB7XHJcbiAgICAgIC8vIOmHjee9ruS9nOWTgeeKtuaAgVxyXG4gICAgICBzZXRXb3JrcyhbXSk7XHJcbiAgICAgIHNldFBhZ2UoMSk7XHJcbiAgICAgIHNldEhhc01vcmUodHJ1ZSk7XHJcbiAgICAgIHNldFNlbGVjdGVkV29ya0lkcyhbXSk7XHJcbiAgICAgIHNldExvYWRpbmdNb3JlKGZhbHNlKTtcclxuXHJcbiAgICAgIGZldGNoV29ya3NEYXRhKDEsIGZhbHNlKTtcclxuICAgIH1cclxuICB9LCBbY3VycmVudFN0ZXAsIGFjdGl2ZVRhYiwgbW91bnRlZF0pO1xyXG5cclxuICAvLyDmuIXnqbrmiYDmnInmlbDmja7nmoTlh73mlbBcclxuICBjb25zdCBjbGVhckFsbERhdGEgPSAoKSA9PiB7XHJcbiAgICAvLyDph43nva7mraXpqqTnirbmgIFcclxuICAgIHNldEN1cnJlbnRTdGVwKCd0ZW1wbGF0ZScpO1xyXG5cclxuICAgIC8vIOmHjee9ruaooeaAgeahhuaVsOaNrlxyXG4gICAgc2V0TW9kYWxEYXRhKHtcclxuICAgICAgc2VsZWN0ZWREaXN0cmlidXRpb246ICcnLFxyXG4gICAgICBhc3NpZ25FbmVyZ3lBbW91bnQ6ICcnLFxyXG4gICAgICBkaXN0cmlidXRlRW5lcmd5QW1vdW50OiAnJyxcclxuICAgICAgc2VsZWN0ZWRUZW1wbGF0ZTogbnVsbCxcclxuICAgICAgc2VsZWN0ZWRTdHVkZW50czogW11cclxuICAgIH0pO1xyXG5cclxuICAgIC8vIOmHjee9rumUmeivr+eKtuaAgVxyXG4gICAgc2V0SW5wdXRFcnJvcnMoe1xyXG4gICAgICBhc3NpZ25FbmVyZ3lFcnJvcjogJycsXHJcbiAgICAgIGRpc3RyaWJ1dGVFbmVyZ3lFcnJvcjogJydcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIOmHjee9ruS7u+WKoeaVsOaNrlxyXG4gICAgc2V0VGFza0RhdGEoe1xyXG4gICAgICB0YXNrTmFtZTogJycsXHJcbiAgICAgIHRhc2tEZXNjcmlwdGlvbjogJycsXHJcbiAgICAgIHNlbGZBc3Nlc3NtZW50SXRlbXM6IFtdLFxyXG4gICAgICBkdXJhdGlvbjogJzHlsI/ml7YnLFxyXG4gICAgICBzdGFydFRpbWU6ICcnLFxyXG4gICAgICBlbmRUaW1lOiAnJ1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8g6YeN572u5YW25LuW54q25oCBXHJcbiAgICBzZXRBY3RpdmVUYWIoJ3Rhc2snKTtcclxuICAgIHNldFdvcmtzKFtdKTtcclxuICAgIHNldFNlbGVjdGVkV29ya0lkcyhbXSk7XHJcbiAgICBzZXRBdHRhY2htZW50cyhbXSk7XHJcbiAgICBzZXRIb3ZlcmVkT3B0aW9uKCcnKTtcclxuICAgIHNldElzVGVtcGxhdGVQaWNrZXJPcGVuKGZhbHNlKTtcclxuICAgIHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxPcGVuKGZhbHNlKTtcclxuICAgIHNldFBhZ2UoMSk7XHJcbiAgICBzZXRIYXNNb3JlKHRydWUpO1xyXG4gICAgc2V0TG9hZGluZ01vcmUoZmFsc2UpO1xyXG5cclxuICAgIC8vIOmHjee9ruWtpueUn+ebuOWFs+aVsOaNrlxyXG4gICAgc2V0U3R1ZGVudHMoW10pO1xyXG4gICAgc2V0U3R1ZGVudFBvaW50c01hcChuZXcgTWFwKCkpO1xyXG4gIH07XHJcblxyXG4gIC8vIOebkeWQrOaooeaAgeahhuWFs+mXre+8jOa4heepuuaVsOaNrlxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWlzT3Blbikge1xyXG4gICAgICBjbGVhckFsbERhdGEoKTtcclxuICAgIH1cclxuICB9LCBbaXNPcGVuXSk7XHJcblxyXG4gIC8vIOe7hOS7tuWNuOi9veaXtua4heepuuaVsOaNrlxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBjbGVhckFsbERhdGEoKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuXHJcblxyXG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xyXG5cclxuICAvLyDpmLLmraLmsLTlkIjplJnor69cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0TW91bnRlZCh0cnVlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIOenu+mZpOiOt+WPluaVmeW4iOWPr+WIhumFjeiDvemHj+eahOWHveaVsO+8jOWboOS4uuS4jemcgOimgeajgOafpeaVmeW4iOiDvemHj+axoFxyXG5cclxuICAvLyDojrflj5bnj63nuqflrabnlJ/liJfooajlkozog73ph4/kv6Hmga/vvIjlubbooYzlpITnkIbvvIlcclxuICBjb25zdCBmZXRjaENsYXNzU3R1ZGVudHNEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFzZWxlY3RlZENsYXNzPy5pZCB8fCAhbW91bnRlZCkgcmV0dXJuO1xyXG5cclxuICAgIHNldExvYWRpbmdTdHVkZW50UG9pbnRzKHRydWUpOyAvLyDmj5DliY3orr7nva7og73ph4/liqDovb3nirbmgIFcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBzdHVkZW50c0RhdGEgPSBhd2FpdCBmZXRjaENsYXNzU3R1ZGVudHNXaXRoTm90aWZpY2F0aW9uKHNlbGVjdGVkQ2xhc3MuaWQsIG5vdGlmaWNhdGlvbik7XHJcbiAgICAgIHNldFN0dWRlbnRzKHN0dWRlbnRzRGF0YSk7XHJcblxyXG4gICAgICAvLyDnq4vljbPlubbooYzojrflj5blrabnlJ/og73ph4/kv6Hmga/vvIzkuI3nrYnlvoXlrabnlJ/liJfooajlrozlhajlpITnkIblroxcclxuICAgICAgaWYgKHN0dWRlbnRzRGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgLy8g5LiN562J5b6F77yM56uL5Y2z5byA5aeL6I635Y+W6IO96YeP5L+h5oGvXHJcbiAgICAgICAgY29uc3QgcG9pbnRzTWFwID0gYXdhaXQgZmV0Y2hTdHVkZW50UG9pbnRzKHN0dWRlbnRzRGF0YS5tYXAoKHM6IFN0dWRlbnQpID0+IHMudXNlcklkKSwgbm90aWZpY2F0aW9uKTtcclxuICAgICAgICBzZXRTdHVkZW50UG9pbnRzTWFwKHBvaW50c01hcCk7XHJcbiAgICAgIH1cclxuICAgICAgc2V0TG9hZGluZ1N0dWRlbnRQb2ludHMoZmFsc2UpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a2m55Sf5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgc2V0U3R1ZGVudHMoW10pO1xyXG4gICAgICBzZXRMb2FkaW5nU3R1ZGVudFBvaW50cyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcblxyXG5cclxuICAvLyDnp7vpmaTojrflj5bmlZnluIjog73ph4/nmoQgdXNlRWZmZWN0XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaXNPcGVuICYmIHNlbGVjdGVkQ2xhc3MpIHtcclxuICAgICAgZmV0Y2hDbGFzc1N0dWRlbnRzRGF0YSgpO1xyXG4gICAgICAvLyDph43nva7mqKHmgIHmoYbmlbDmja5cclxuICAgICAgc2V0TW9kYWxEYXRhKHtcclxuICAgICAgICBzZWxlY3RlZERpc3RyaWJ1dGlvbjogaXNCbG9ja3NPbmx5TW9kZSA/ICdub25lJyA6ICdub25lJywgLy8g57qv56ev5pyo5YiG6YWN5qih5byP5pe26buY6K6k5LiN5YiG6YWN6IO96YePXHJcbiAgICAgICAgYXNzaWduRW5lcmd5QW1vdW50OiAnJyxcclxuICAgICAgICBkaXN0cmlidXRlRW5lcmd5QW1vdW50OiAnJyxcclxuICAgICAgICBzZWxlY3RlZFRlbXBsYXRlOiBudWxsLFxyXG4gICAgICAgIHNlbGVjdGVkU3R1ZGVudHM6IFtdLCAvLyDlsIblnKjojrflj5blrabnlJ/liJfooajlkI7orr7nva7kuLrmiYDmnInlrabnlJ9cclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyDph43nva7plJnor6/nirbmgIFcclxuICAgICAgc2V0SW5wdXRFcnJvcnMoe1xyXG4gICAgICAgIGFzc2lnbkVuZXJneUVycm9yOiAnJyxcclxuICAgICAgICBkaXN0cmlidXRlRW5lcmd5RXJyb3I6ICcnXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8g56aB55SoYm9keea7muWKqFxyXG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ2hpZGRlbic7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyDmgaLlpI1ib2R55rua5YqoXHJcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnJztcclxuICAgIH1cclxuXHJcbiAgICAvLyDmuIXnkIblh73mlbDvvJrnu4Tku7bljbjovb3ml7bmgaLlpI3mu5rliqhcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnJztcclxuICAgIH07XHJcbiAgfSwgW2lzT3Blbiwgc2VsZWN0ZWRDbGFzcywgbW91bnRlZF0pO1xyXG5cclxuICAvLyDlvZPlrabnlJ/liJfooajliqDovb3lrozmiJDlkI7vvIzoh6rliqjpgInmi6nmiYDmnInlrabnlJ9cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgc2V0TW9kYWxEYXRhKHByZXYgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIHNlbGVjdGVkU3R1ZGVudHM6IHN0dWRlbnRzLm1hcChzID0+IHMudXNlcklkKVxyXG4gICAgICB9KSk7XHJcbiAgICB9XHJcbiAgfSwgW3N0dWRlbnRzXSk7XHJcblxyXG4gIGNvbnN0IGRpc3RyaWJ1dGlvbk9wdGlvbnMgPSBbXHJcbiAgICB7IGlkOiAnbm9uZScsIGxhYmVsOiAn5LiN5YiG6YWNJywgZGVzY3JpcHRpb246ICfkv53mjIHljp/mnInorr7nva4nLCBoYXNJbnB1dDogZmFsc2UgfSxcclxuICAgIHsgaWQ6ICdhc3NpZ24nLCBsYWJlbDogJ+WIhumFjScsIGRlc2NyaXB0aW9uOiAn5YiG6YWN57uZ5a2m55SfJywgaGFzSW5wdXQ6IHRydWUgfSxcclxuICAgIHsgaWQ6ICdkaXN0cmlidXRlJywgbGFiZWw6ICfliIbphY3oh7MnLCBkZXNjcmlwdGlvbjogJ+WIhumFjeWIsOaMh+WumuS9jee9ricsIGhhc0lucHV0OiB0cnVlIH1cclxuICBdO1xyXG5cclxuICAvLyDliKTmlq3mmK/lkKbkuLrnuq/np6/mnKjliIbphY3mqKHlvI/vvIjkuI3mtonlj4rog73ph4/vvIlcclxuICBjb25zdCBpc0Jsb2Nrc09ubHlNb2RlID0gYWN0aW9uVHlwZSA9PT0gJ+WIhumFjeenr+acqCc7XHJcblxyXG4gIC8vIOWIpOaWreaYr+WQpuS4uue6r+iDvemHj+WIhumFjeaooeW8j++8iOS4jea2ieWPiuenr+acqO+8iVxyXG4gIGNvbnN0IGlzRW5lcmd5T25seU1vZGUgPSBhY3Rpb25UeXBlID09PSAn5YiG6YWN6IO96YePJztcclxuXHJcbiAgY29uc3QgaGFuZGxlRGlzdHJpYnV0aW9uU2VsZWN0ID0gKG9wdGlvbklkOiBzdHJpbmcpID0+IHtcclxuICAgIHNldE1vZGFsRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIHNlbGVjdGVkRGlzdHJpYnV0aW9uOiBvcHRpb25JZFxyXG4gICAgfSkpO1xyXG5cclxuICAgIC8vIOiHquWKqOiBmueEpuWIsOWvueW6lOeahOi+k+WFpeahhlxyXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGlmIChvcHRpb25JZCA9PT0gJ2Fzc2lnbicgJiYgYXNzaWduSW5wdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGFzc2lnbklucHV0UmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgfSBlbHNlIGlmIChvcHRpb25JZCA9PT0gJ2Rpc3RyaWJ1dGUnICYmIGRpc3RyaWJ1dGVJbnB1dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgZGlzdHJpYnV0ZUlucHV0UmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgfVxyXG4gICAgfSwgMTAwKTsgLy8g5bu26L+f5LiA54K556Gu5L+d6L6T5YWl5qGG5bey57uP5riy5p+TXHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVGVtcGxhdGVTZWxlY3QgPSAodGVtcGxhdGU6IGFueSkgPT4ge1xyXG4gICAgc2V0TW9kYWxEYXRhKHByZXYgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgc2VsZWN0ZWRUZW1wbGF0ZTogdGVtcGxhdGVcclxuICAgIH0pKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDYW5jZWxUZW1wbGF0ZSA9ICgpID0+IHtcclxuICAgIHNldE1vZGFsRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIHNlbGVjdGVkVGVtcGxhdGU6IG51bGxcclxuICAgICAgLy8g5LiN6YeN572uc2VsZWN0ZWREaXN0cmlidXRpb27vvIzkv53mjIHnlKjmiLfnmoTog73ph4/liIbphY3pgInmi6lcclxuICAgIH0pKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVUZW1wbGF0ZVBpY2tlck9wZW4gPSAoKSA9PiB7XHJcbiAgICBzZXRJc1RlbXBsYXRlUGlja2VyT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVUZW1wbGF0ZVBpY2tlckNsb3NlID0gKCkgPT4ge1xyXG4gICAgc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4oZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIC8vIOWkhOeQhuaJuemHj+WFkeaNouWvhumSpeaooeaAgeahhlxyXG4gIGNvbnN0IGhhbmRsZUJhdGNoVXNlS2V5UGFja2FnZU1vZGFsT3BlbiA9ICgpID0+IHtcclxuICAgIHNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxPcGVuKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJhdGNoVXNlS2V5UGFja2FnZU1vZGFsQ2xvc2UgPSAoKSA9PiB7XHJcbiAgICBzZXRJc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsT3BlbihmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlQmF0Y2hVc2VLZXlQYWNrYWdlU3VjY2VzcyA9IGFzeW5jICgpID0+IHtcclxuICAgIC8vIOWFkeaNouaIkOWKn+WQjumHjeaWsOiOt+WPluWtpueUn+iDvemHj+S/oeaBr1xyXG4gICAgaWYgKHN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc3QgcG9pbnRzTWFwID0gYXdhaXQgZmV0Y2hTdHVkZW50UG9pbnRzKHN0dWRlbnRzLm1hcChzID0+IHMudXNlcklkKSwgbm90aWZpY2F0aW9uKTtcclxuICAgICAgc2V0U3R1ZGVudFBvaW50c01hcChwb2ludHNNYXApO1xyXG4gICAgfVxyXG4gICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WvhumSpeWFkeaNouaIkOWKn++8gScpO1xyXG4gIH07XHJcblxyXG4gIC8vIOiOt+WPluW9k+WJjemAieS4reWIhumFjeaWueW8j+WvueW6lOeahOiDvemHj+aVsOWAvFxyXG4gIGNvbnN0IGdldEN1cnJlbnRFbmVyZ3lBbW91bnQgPSAoKSA9PiB7XHJcbiAgICBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnYXNzaWduJykge1xyXG4gICAgICByZXR1cm4gbW9kYWxEYXRhLmFzc2lnbkVuZXJneUFtb3VudDtcclxuICAgIH0gZWxzZSBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcclxuICAgICAgcmV0dXJuIG1vZGFsRGF0YS5kaXN0cmlidXRlRW5lcmd5QW1vdW50O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuICcnO1xyXG4gIH07XHJcblxyXG4gIC8vIOiuoeeul+aJgOacieWtpueUn+eahOacgOS9juWPr+WIhumFjeiDvemHj1xyXG4gIGNvbnN0IGdldE1pbkF2YWlsYWJsZVBvaW50cyA9ICgpID0+IHtcclxuICAgIGlmIChtb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50cy5sZW5ndGggPT09IDApIHJldHVybiAwO1xyXG5cclxuICAgIGNvbnN0IHNlbGVjdGVkU3R1ZGVudFBvaW50cyA9IG1vZGFsRGF0YS5zZWxlY3RlZFN0dWRlbnRzLm1hcChzdHVkZW50SWQgPT5cclxuICAgICAgc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKSB8fCAwXHJcbiAgICApO1xyXG5cclxuICAgIHJldHVybiBNYXRoLm1pbiguLi5zZWxlY3RlZFN0dWRlbnRQb2ludHMpO1xyXG4gIH07XHJcblxyXG4gIC8vIOiOt+WPluW9k+WJjeWIhumFjeaWueW8j+eahOaPkOekuuS/oeaBr1xyXG4gIGNvbnN0IGdldEVuZXJneURpc3BsYXlJbmZvID0gKCkgPT4ge1xyXG4gICAgaWYgKG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gJ2Fzc2lnbicgfHwgbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBsYWJlbDogJ+acgOS9juWPr+WIhumFjeiDvemHjycsXHJcbiAgICAgICAgdmFsdWU6IGdldE1pbkF2YWlsYWJsZVBvaW50cygpXHJcbiAgICAgIH07XHJcbiAgICB9XHJcbiAgICByZXR1cm4geyBsYWJlbDogJycsIHZhbHVlOiAwIH07XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTmV4dCA9ICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCfpgInmi6nnmoTliIbphY3mlrnlvI86JywgbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uKTtcclxuICAgIGNvbnNvbGUubG9nKCfpgInmi6nnmoTmqKHmnb86JywgbW9kYWxEYXRhLnNlbGVjdGVkVGVtcGxhdGUpO1xyXG5cclxuICAgIC8vIOWcqOe6r+enr+acqOWIhumFjeaooeW8j+aXtu+8jOi3s+i/h+iDvemHj+mqjOivgVxyXG4gICAgaWYgKCFpc0Jsb2Nrc09ubHlNb2RlICYmIChtb2RhbERhdGEuc2VsZWN0ZWREaXN0cmlidXRpb24gPT09ICdhc3NpZ24nIHx8IG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gJ2Rpc3RyaWJ1dGUnKSkge1xyXG4gICAgICBjb25zdCBjdXJyZW50RW5lcmd5QW1vdW50ID0gZ2V0Q3VycmVudEVuZXJneUFtb3VudCgpO1xyXG4gICAgICBjb25zb2xlLmxvZygn5YiG6YWN6IO96YeP5pWw6YePOicsIGN1cnJlbnRFbmVyZ3lBbW91bnQpO1xyXG5cclxuICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6L6T5YWl6ZSZ6K+vXHJcbiAgICAgIGNvbnN0IGVycm9yS2V5ID0gbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnYXNzaWduJyA/ICdhc3NpZ25FbmVyZ3lFcnJvcicgOiAnZGlzdHJpYnV0ZUVuZXJneUVycm9yJztcclxuICAgICAgaWYgKGlucHV0RXJyb3JzW2Vycm9yS2V5IGFzIGtleW9mIHR5cGVvZiBpbnB1dEVycm9yc10pIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+S/ruato+i+k+WFpemUmeivr+WQjuWGjee7p+e7rScpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8g5qOA5p+l6IO96YeP5pWw6YeP5piv5ZCm5pyJ5pWIXHJcbiAgICAgIGNvbnN0IGVuZXJneUFtb3VudE51bSA9IE51bWJlcihjdXJyZW50RW5lcmd5QW1vdW50KTtcclxuICAgICAgaWYgKCFjdXJyZW50RW5lcmd5QW1vdW50IHx8IGVuZXJneUFtb3VudE51bSA8PSAwKSB7XHJcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fovpPlhaXmnInmlYjnmoTliIbphY3og73ph4/mlbDph48nKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChtb2RhbERhdGEuc2VsZWN0ZWREaXN0cmlidXRpb24gPT09ICdhc3NpZ24nKSB7XHJcbiAgICAgICAgLy8gXCLliIbphY1cIuaMiemSru+8muajgOafpemAieS4reWtpueUn+eahOWPr+WIhumFjeiDvemHj+aYr+WQpui2s+Wkn1xyXG4gICAgICAgIGNvbnN0IGluc3VmZmljaWVudFN0dWRlbnRzID0gbW9kYWxEYXRhLnNlbGVjdGVkU3R1ZGVudHMuZmlsdGVyKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICBjb25zdCBzdHVkZW50QXZhaWxhYmxlUG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKTtcclxuICAgICAgICAgIHJldHVybiBzdHVkZW50QXZhaWxhYmxlUG9pbnRzICE9PSB1bmRlZmluZWQgJiYgc3R1ZGVudEF2YWlsYWJsZVBvaW50cyA8IGVuZXJneUFtb3VudE51bTtcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgaWYgKGluc3VmZmljaWVudFN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIGNvbnN0IGluc3VmZmljaWVudE5hbWVzID0gaW5zdWZmaWNpZW50U3R1ZGVudHMubWFwKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHN0dWRlbnQgPSBzdHVkZW50cy5maW5kKHMgPT4gcy51c2VySWQgPT09IHN0dWRlbnRJZCk7XHJcbiAgICAgICAgICAgIGNvbnN0IGF2YWlsYWJsZVBvaW50cyA9IHN0dWRlbnRQb2ludHNNYXAuZ2V0KHN0dWRlbnRJZCkgfHwgMDtcclxuICAgICAgICAgICAgcmV0dXJuIGAke3N0dWRlbnQ/Lm5pY2tOYW1lIHx8IGDlrabnlJ8ke3N0dWRlbnRJZH1gfSjlj6/liIbphY06ICR7YXZhaWxhYmxlUG9pbnRzfSlgO1xyXG4gICAgICAgICAgfSkuam9pbign44CBJyk7XHJcblxyXG4gICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKGDnp6/liIbkuI3otrPvvJrku6XkuIvlrabnlJ/nmoTlj6/liIbphY3og73ph4/kuI3otrMgJHtlbmVyZ3lBbW91bnROdW1977yaJHtpbnN1ZmZpY2llbnROYW1lc31gKTtcclxuICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcclxuICAgICAgICAvLyBcIuWIhumFjeiHs1wi5oyJ6ZKu77ya5qOA5p+l6ZyA6KaB6KGl5YWF6IO96YeP55qE5a2m55SfXHJcbiAgICAgICAgY29uc3Qgc3R1ZGVudHNOZWVkaW5nRW5lcmd5ID0gbW9kYWxEYXRhLnNlbGVjdGVkU3R1ZGVudHMuZmlsdGVyKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICBjb25zdCBjdXJyZW50UG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKSB8fCAwO1xyXG4gICAgICAgICAgcmV0dXJuIGN1cnJlbnRQb2ludHMgPCBlbmVyZ3lBbW91bnROdW07XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGlmIChzdHVkZW50c05lZWRpbmdFbmVyZ3kubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgLy8g5qOA5p+l6L+Z5Lqb5a2m55Sf5piv5ZCm5pyJ6Laz5aSf55qE5Y+v5YiG6YWN6IO96YeP5p2l6L6+5Yiw55uu5qCH5YC8XHJcbiAgICAgICAgICBjb25zdCBpbnN1ZmZpY2llbnRTdHVkZW50cyA9IHN0dWRlbnRzTmVlZGluZ0VuZXJneS5maWx0ZXIoc3R1ZGVudElkID0+IHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFBvaW50cyA9IHN0dWRlbnRQb2ludHNNYXAuZ2V0KHN0dWRlbnRJZCkgfHwgMDtcclxuICAgICAgICAgICAgY29uc3QgbmVlZGVkUG9pbnRzID0gZW5lcmd5QW1vdW50TnVtIC0gY3VycmVudFBvaW50cztcclxuICAgICAgICAgICAgY29uc3Qgc3R1ZGVudEF2YWlsYWJsZVBvaW50cyA9IHN0dWRlbnRQb2ludHNNYXAuZ2V0KHN0dWRlbnRJZCk7XHJcbiAgICAgICAgICAgIHJldHVybiBzdHVkZW50QXZhaWxhYmxlUG9pbnRzICE9PSB1bmRlZmluZWQgJiYgc3R1ZGVudEF2YWlsYWJsZVBvaW50cyA8IG5lZWRlZFBvaW50cztcclxuICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgIGlmIChpbnN1ZmZpY2llbnRTdHVkZW50cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGluc3VmZmljaWVudE5hbWVzID0gaW5zdWZmaWNpZW50U3R1ZGVudHMubWFwKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3Qgc3R1ZGVudCA9IHN0dWRlbnRzLmZpbmQocyA9PiBzLnVzZXJJZCA9PT0gc3R1ZGVudElkKTtcclxuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50UG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKSB8fCAwO1xyXG4gICAgICAgICAgICAgIGNvbnN0IG5lZWRlZFBvaW50cyA9IGVuZXJneUFtb3VudE51bSAtIGN1cnJlbnRQb2ludHM7XHJcbiAgICAgICAgICAgICAgY29uc3QgYXZhaWxhYmxlUG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKSB8fCAwO1xyXG4gICAgICAgICAgICAgIHJldHVybiBgJHtzdHVkZW50Py5uaWNrTmFtZSB8fCBg5a2m55SfJHtzdHVkZW50SWR9YH0o6ZyA6KaBOiAke25lZWRlZFBvaW50c30sIOWPr+WIhumFjTogJHthdmFpbGFibGVQb2ludHN9KWA7XHJcbiAgICAgICAgICAgIH0pLmpvaW4oJ+OAgScpO1xyXG5cclxuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKGDnp6/liIbkuI3otrPvvJrku6XkuIvlrabnlJ/ml6Dms5Xovr7liLDnm67moIfog73ph4/lgLwgJHtlbmVyZ3lBbW91bnROdW1977yaJHtpbnN1ZmZpY2llbnROYW1lc31gKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOagueaNruaTjeS9nOexu+Wei+WGs+WumuS4i+S4gOatpVxyXG4gICAgaWYgKGFjdGlvblR5cGUgPT09ICflj5HluIPku7vliqEnIHx8IGFjdGlvblR5cGUgPT09ICflv6vpgJ/kuIror74nKSB7XHJcbiAgICAgIC8vIOWPkeW4g+S7u+WKoeaIluW/q+mAn+S4iuivvu+8muWIh+aNouWIsOWPkeW4g+S7u+WKoeatpemqpFxyXG4gICAgICBzZXRUYXNrRGF0YShwcmV2ID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBkdXJhdGlvbjogJzHlsI/ml7YnIC8vIOmHjee9ruS4uum7mOiupOaMgee7reaXtumXtFxyXG4gICAgICB9KSk7XHJcbiAgICAgIHNldEN1cnJlbnRTdGVwKCdwdWJsaXNoJyk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyDliIbphY3np6/mnKjmiJbliIbphY3og73ph4/vvJrnm7TmjqXmiafooYzliIbphY3mk43kvZzlubblhbPpl63lvLnnqpdcclxuICAgICAgY29uc29sZS5sb2coYCR7YWN0aW9uVHlwZX3mk43kvZzmiafooYxgLCB7XHJcbiAgICAgICAgc2VsZWN0ZWREaXN0cmlidXRpb246IG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbixcclxuICAgICAgICBzZWxlY3RlZFRlbXBsYXRlOiBtb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZSxcclxuICAgICAgICBzZWxlY3RlZFN0dWRlbnRzOiBtb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50cyxcclxuICAgICAgICBlbmVyZ3lBbW91bnQ6IGdldEN1cnJlbnRFbmVyZ3lBbW91bnQoKVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIOi/memHjOWPr+S7peiwg+eUqOebuOW6lOeahEFQSeadpeaJp+ihjOWIhumFjeaTjeS9nFxyXG4gICAgICAvLyBUT0RPOiDlrp7njrDliIbphY3np6/mnKjlkozliIbphY3og73ph4/nmoRBUEnosIPnlKhcclxuXHJcbiAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKGAke2FjdGlvblR5cGV95oiQ5Yqf77yBYCk7XHJcbiAgICAgIGNsZWFyQWxsRGF0YSgpO1xyXG4gICAgICBvbkNsb3NlKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlUHJldmlvdXMgPSAoKSA9PiB7XHJcbiAgICBpZiAoY3VycmVudFN0ZXAgPT09ICdwdWJsaXNoJykge1xyXG4gICAgICAvLyDku47lj5HluIPku7vliqHmraXpqqTov5Tlm57ml7bph43nva7mjIHnu63ml7bpl7RcclxuICAgICAgc2V0VGFza0RhdGEocHJldiA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgZHVyYXRpb246ICcx5bCP5pe2J1xyXG4gICAgICB9KSk7XHJcbiAgICAgIHNldEN1cnJlbnRTdGVwKCd0ZW1wbGF0ZScpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgb25CYWNrKCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcblxyXG5cclxuICAvLyDnoa7orqTlj5HluIPvvIjkuI5OZXdQdWJsaXNoVGFza01vZGFs5L+d5oyB5LiA6Ie077yJXHJcbiAgY29uc3QgaGFuZGxlQ29uZmlybSA9ICgpID0+IHtcclxuICAgIGlmICghdGFza0RhdGEudGFza05hbWUudHJpbSgpKSB7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+36L6T5YWl5Lu75Yqh5ZCN56ewJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB0aW1lcyA9IGdldFRhc2tUaW1lcyh0YXNrRGF0YS5kdXJhdGlvbik7XHJcbiAgICBjb25zdCBmaW5hbFRhc2tEYXRhID0ge1xyXG4gICAgICAuLi50YXNrRGF0YSxcclxuICAgICAgc3RhcnRUaW1lOiB0aW1lcy5zdGFydFRpbWUsXHJcbiAgICAgIGVuZFRpbWU6IHRpbWVzLmVuZFRpbWUsXHJcbiAgICAgIHNlbGVjdGVkV29ya0lkcyxcclxuICAgICAgYXR0YWNobWVudHMsXHJcbiAgICAgIG1vZGFsRGF0YVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyDlpoLmnpzmnIlvbkNvbmZpcm3lm57osIPvvIzosIPnlKjlroPvvJvlkKbliJnmiafooYzljp/mnInnmoTlj5HluIPpgLvovpFcclxuICAgIGlmIChvbkNvbmZpcm0pIHtcclxuICAgICAgb25Db25maXJtKGZpbmFsVGFza0RhdGEpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8g5L+d55WZ5Y6f5pyJ55qE5Y+R5biD6YC76L6R5L2c5Li65ZCO5aSHXHJcbiAgICAgIGhhbmRsZVB1Ymxpc2hUYXNrT2soKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVQdWJsaXNoVGFza09rID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgLy8g6Ziy5q2i6YeN5aSN54K55Ye7XHJcbiAgICBpZiAoaXNQdWJsaXNoaW5nKSB7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc1B1Ymxpc2hpbmcodHJ1ZSk7XHJcblxyXG4gICAgICAvLyDpqozor4Hlv4XloavlrZfmrrVcclxuICAgICAgaWYgKCF0YXNrRGF0YS50YXNrTmFtZS50cmltKCkpIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+i+k+WFpeS7u+WKoeWQjeensCcpO1xyXG4gICAgICAgIHNldElzUHVibGlzaGluZyhmYWxzZSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygn5Y+R5biD5Lu75YqhOicsIHRhc2tEYXRhKTtcclxuICAgICAgY29uc29sZS5sb2coJ+mAieS4reeahOS9nOWTgUlEOicsIHNlbGVjdGVkV29ya0lkcyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfmqKHmgIHmoYbmlbDmja46JywgbW9kYWxEYXRhKTtcclxuXHJcbiAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt+S/oeaBr1xyXG4gICAgICBjb25zdCB1c2VyRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XHJcbiAgICAgIGNvbnN0IHVzZXIgPSB1c2VyRGF0YSA/IEpTT04ucGFyc2UodXNlckRhdGEpIDogbnVsbDtcclxuICAgICAgY29uc3QgdGVhY2hlcklkID0gdXNlcj8udXNlcklkO1xyXG5cclxuICAgICAgaWYgKCF0ZWFjaGVySWQpIHtcclxuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+acquaJvuWIsOeUqOaIt+S/oeaBrycpO1xyXG4gICAgICAgIHNldElzUHVibGlzaGluZyhmYWxzZSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDlpITnkIbml7bpl7RcclxuICAgICAgY29uc3Qgc3RhcnREYXRlID0gdGFza0RhdGEuc3RhcnRUaW1lID8gbmV3IERhdGUodGFza0RhdGEuc3RhcnRUaW1lKSA6IG5ldyBEYXRlKCk7XHJcbiAgICAgIGNvbnN0IGVuZERhdGUgPSB0YXNrRGF0YS5lbmRUaW1lID8gbmV3IERhdGUodGFza0RhdGEuZW5kVGltZSkgOiBuZXcgRGF0ZShEYXRlLm5vdygpICsgNyAqIDI0ICogNjAgKiA2MCAqIDEwMDApO1xyXG5cclxuICAgICAgLy8g5p6E5bu65Lu75Yqh5Y+R5biD5Y+C5pWwXHJcbiAgICAgIGNvbnN0IHRhc2tQYXJhbXMgPSB7XHJcbiAgICAgICAgdGFza05hbWU6IHRhc2tEYXRhLnRhc2tOYW1lLFxyXG4gICAgICAgIHRhc2tEZXNjcmlwdGlvbjogdGFza0RhdGEudGFza0Rlc2NyaXB0aW9uIHx8ICcnLFxyXG4gICAgICAgIHRhc2tUeXBlOiBUYXNrVHlwZS5HUkFQSElDLCAvLyDpu5jorqTkuLrlm77lvaLljJbku7vliqFcclxuICAgICAgICBwcmlvcml0eTogUHJpb3JpdHkuTk9STUFMLCAvLyDpu5jorqTkuLrmma7pgJrkvJjlhYjnuqdcclxuICAgICAgICBzdGFydERhdGU6IHN0YXJ0RGF0ZSxcclxuICAgICAgICBlbmREYXRlOiBlbmREYXRlLFxyXG4gICAgICAgIHRhc2tDb250ZW50OiB0YXNrRGF0YS50YXNrRGVzY3JpcHRpb24gfHwgJycsIC8vIOS9v+eUqOS7u+WKoeaPj+i/sOS9nOS4uuWGheWuuVxyXG4gICAgICAgIGF0dGFjaG1lbnRzOiBhdHRhY2htZW50cy5tYXAoZmlsZSA9PiBmaWxlLm5hbWUpIHx8IFtdLCAvLyDovazmjaLkuLrmlofku7blkI3mlbDnu4RcclxuICAgICAgICBpc1B1YmxpYzogMSwgLy8g5YWs5byA5Lu75YqhXHJcbiAgICAgICAgYWxsb3dMYXRlU3VibWlzc2lvbjogZmFsc2UsXHJcbiAgICAgICAgc3R1ZGVudElkczogbW9kYWxEYXRhLnNlbGVjdGVkU3R1ZGVudHMsXHJcbiAgICAgICAgY2xhc3NJZDogc2VsZWN0ZWRDbGFzcz8uaWQsXHJcbiAgICAgICAgdGVtcGxhdGVJZDogbW9kYWxEYXRhLnNlbGVjdGVkVGVtcGxhdGU/LmlkLFxyXG4gICAgICAgIHdvcmtJZHM6IHNlbGVjdGVkV29ya0lkcy5sZW5ndGggPiAwID8gc2VsZWN0ZWRXb3JrSWRzIDogdW5kZWZpbmVkLCAvLyDkvKDpgJLkvZzlk4FJROaVsOe7hFxyXG4gICAgICAgIHNlbGZBc3Nlc3NtZW50SXRlbXM6IHRhc2tEYXRhLnNlbGZBc3Nlc3NtZW50SXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS50cmltKCkgIT09ICcnKSB8fCBbXSAvLyDov4fmu6TnqbrnmoToh6ror4TpoblcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCfku7vliqHlj5HluIPlj4LmlbA6JywgdGFza1BhcmFtcyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfkvZzlk4FJROaVsOe7hDonLCB0YXNrUGFyYW1zLndvcmtJZHMpO1xyXG5cclxuICAgICAgLy8g5YeG5aSH5bm26KGM6K+35rGC5pWw57uEXHJcbiAgICAgIGNvbnN0IHJlcXVlc3RzOiBQcm9taXNlPGFueT5bXSA9IFtdO1xyXG5cclxuICAgICAgLy8gMS4g5Lu75Yqh5Y+R5biD6K+35rGC77yI5b+F6aG75omn6KGM77yJXHJcbiAgICAgIHJlcXVlc3RzLnB1c2godGFza0FwaS5wdWJsaXNoVGFzayh0YXNrUGFyYW1zKSk7XHJcblxyXG4gICAgICAvLyAyLiDog73ph4/liIbphY3or7fmsYLvvIjlpoLmnpzpnIDopoHvvIlcclxuICAgICAgbGV0IGVuZXJneVJlcXVlc3Q6IFByb21pc2U8YW55PiB8IG51bGwgPSBudWxsO1xyXG4gICAgICBjb25zdCBjdXJyZW50RW5lcmd5QW1vdW50ID0gZ2V0Q3VycmVudEVuZXJneUFtb3VudCgpO1xyXG4gICAgICBpZiAoKG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gJ2Fzc2lnbicgfHwgbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpICYmIGN1cnJlbnRFbmVyZ3lBbW91bnQpIHtcclxuICAgICAgICBjb25zdCB0YXJnZXRBbW91bnQgPSBOdW1iZXIoY3VycmVudEVuZXJneUFtb3VudCk7XHJcbiAgICAgICAgY29uc3QgZGVmYXVsdEV4cGlyZVRpbWUgPSBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpO1xyXG5cclxuICAgICAgICBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnYXNzaWduJykge1xyXG4gICAgICAgICAgLy8gXCLliIbphY1cIuaMiemSru+8mue7meavj+S4quWtpueUn+WIhumFjeWbuuWumuaVsOmHj+eahOiDvemHj1xyXG4gICAgICAgICAgY29uc3Qgc3R1ZGVudEV4cGlyaWVzOiB7IFtzdHVkZW50SWQ6IG51bWJlcl06IHN0cmluZyB9ID0ge307XHJcbiAgICAgICAgICBtb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50cy5mb3JFYWNoKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICAgIHN0dWRlbnRFeHBpcmllc1tzdHVkZW50SWRdID0gZGVmYXVsdEV4cGlyZVRpbWU7XHJcbiAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICBlbmVyZ3lSZXF1ZXN0ID0gcG9pbnRzQXBpLmJhdGNoQXNzaWduUGVybWlzc2lvbih7XHJcbiAgICAgICAgICAgIGF2YWlsYWJsZVBvaW50czogdGFyZ2V0QW1vdW50LFxyXG4gICAgICAgICAgICBzdHVkZW50RXhwaXJpZXMsXHJcbiAgICAgICAgICAgIHJlbWFyazogYOS7u+WKoeWPkeW4gyAtICR7dGFza0RhdGEudGFza05hbWV9YFxyXG4gICAgICAgICAgfSk7XHJcbiAgICAgICAgICByZXF1ZXN0cy5wdXNoKGVuZXJneVJlcXVlc3QpO1xyXG4gICAgICAgIH0gZWxzZSBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcclxuICAgICAgICAgIC8vIFwi5YiG6YWN6IezXCLmjInpkq7vvJrlsIblrabnlJ/og73ph4/ooaXlhYXliLDnm67moIflgLxcclxuICAgICAgICAgIGNvbnN0IGVuZXJneVJlcXVlc3RzOiBQcm9taXNlPGFueT5bXSA9IFtdO1xyXG5cclxuICAgICAgICAgIG1vZGFsRGF0YS5zZWxlY3RlZFN0dWRlbnRzLmZvckVhY2goc3R1ZGVudElkID0+IHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudFBvaW50cyA9IHN0dWRlbnRQb2ludHNNYXAuZ2V0KHN0dWRlbnRJZCkgfHwgMDtcclxuICAgICAgICAgICAgY29uc3QgbmVlZGVkUG9pbnRzID0gdGFyZ2V0QW1vdW50IC0gY3VycmVudFBvaW50cztcclxuXHJcbiAgICAgICAgICAgIC8vIOWPquacieW9k+WtpueUn+W9k+WJjeiDvemHj+Wwj+S6juebruagh+WAvOaXtuaJjeWIhumFjVxyXG4gICAgICAgICAgICBpZiAobmVlZGVkUG9pbnRzID4gMCkge1xyXG4gICAgICAgICAgICAgIGNvbnN0IHN0dWRlbnRFeHBpcmllczogeyBbc3R1ZGVudElkOiBudW1iZXJdOiBzdHJpbmcgfSA9IHt9O1xyXG4gICAgICAgICAgICAgIHN0dWRlbnRFeHBpcmllc1tzdHVkZW50SWRdID0gZGVmYXVsdEV4cGlyZVRpbWU7XHJcblxyXG4gICAgICAgICAgICAgIGNvbnN0IHJlcXVlc3QgPSBwb2ludHNBcGkuYmF0Y2hBc3NpZ25QZXJtaXNzaW9uKHtcclxuICAgICAgICAgICAgICAgIGF2YWlsYWJsZVBvaW50czogbmVlZGVkUG9pbnRzLFxyXG4gICAgICAgICAgICAgICAgc3R1ZGVudEV4cGlyaWVzLFxyXG4gICAgICAgICAgICAgICAgcmVtYXJrOiBg5Lu75Yqh5Y+R5biDIC0gJHt0YXNrRGF0YS50YXNrTmFtZX0gKOihpeWFheiHsyR7dGFyZ2V0QW1vdW50fSlgXHJcbiAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgZW5lcmd5UmVxdWVzdHMucHVzaChyZXF1ZXN0KTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgLy8g5bCG5omA5pyJ6IO96YeP5YiG6YWN6K+35rGC5re75Yqg5Yiw5Li76K+35rGC5YiX6KGoXHJcbiAgICAgICAgICByZXF1ZXN0cy5wdXNoKC4uLmVuZXJneVJlcXVlc3RzKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIDMuIOaooeadv+WIhumFjeivt+axgu+8iOWmguaenOmcgOimge+8iVxyXG4gICAgICBsZXQgdGVtcGxhdGVSZXF1ZXN0OiBQcm9taXNlPGFueT4gfCBudWxsID0gbnVsbDtcclxuICAgICAgaWYgKG1vZGFsRGF0YS5zZWxlY3RlZFRlbXBsYXRlKSB7XHJcbiAgICAgICAgY29uc3QgdXNlcnMgPSBtb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50cy5tYXAoc3R1ZGVudElkID0+ICh7XHJcbiAgICAgICAgICB1c2VySWQ6IHN0dWRlbnRJZCxcclxuICAgICAgICAgIHJvbGVJZDogMSxcclxuICAgICAgICAgIHRlbXBsYXRlSWQ6IG1vZGFsRGF0YS5zZWxlY3RlZFRlbXBsYXRlLmlkLFxyXG4gICAgICAgICAgb3JpZ2luYWxUZW1wbGF0ZUlkOiBtb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZS5vcmlnaW5hbFRlbXBsYXRlSWQgfHwgbW9kYWxEYXRhLnNlbGVjdGVkVGVtcGxhdGUuaWRcclxuICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgIHRlbXBsYXRlUmVxdWVzdCA9IGJhdGNoQWRkVXNlckpvaW5Sb2xlKHsgdXNlcnMgfSk7XHJcbiAgICAgICAgcmVxdWVzdHMucHVzaCh0ZW1wbGF0ZVJlcXVlc3QpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDlubbooYzmiafooYzmiYDmnInor7fmsYJcclxuICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChyZXF1ZXN0cyk7XHJcblxyXG4gICAgICAvLyDlpITnkIbku7vliqHlj5HluIPnu5PmnpxcclxuICAgICAgY29uc3QgdGFza1Jlc3VsdCA9IHJlc3VsdHNbMF07XHJcbiAgICAgIGlmICh0YXNrUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgJiYgdGFza1Jlc3VsdC52YWx1ZS5kYXRhLmNvZGUgPT09IDIwMCkge1xyXG4gICAgICAgIC8vIOaYvuekuuaIkOWKn+WPkeW4g+S7u+WKoeeahOaPkOekulxyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfwn46JIOS7u+WKoeWPkeW4g+aIkOWKn++8geWtpueUn+WPr+S7peW8gOWni+WtpuS5oOS6hicpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIGNvbnN0IGVycm9yTXNnID0gdGFza1Jlc3VsdC5zdGF0dXMgPT09ICdmdWxmaWxsZWQnXHJcbiAgICAgICAgICA/IHRhc2tSZXN1bHQudmFsdWUuZGF0YS5tZXNzYWdlIHx8ICfku7vliqHlj5HluIPlpLHotKUnXHJcbiAgICAgICAgICA6ICfku7vliqHlj5HluIPlpLHotKUnO1xyXG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihlcnJvck1zZyk7XHJcbiAgICAgICAgc2V0SXNQdWJsaXNoaW5nKGZhbHNlKTtcclxuICAgICAgICByZXR1cm47IC8vIOS7u+WKoeWPkeW4g+Wksei0peWImeebtOaOpei/lOWbnlxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDlpITnkIbog73ph4/liIbphY3nu5PmnpxcclxuICAgICAgbGV0IHJlc3VsdEluZGV4ID0gMTtcclxuICAgICAgaWYgKGVuZXJneVJlcXVlc3QpIHtcclxuICAgICAgICBjb25zdCBlbmVyZ3lSZXN1bHQgPSByZXN1bHRzW3Jlc3VsdEluZGV4XTtcclxuICAgICAgICBpZiAoZW5lcmd5UmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgJiYgZW5lcmd5UmVzdWx0LnZhbHVlLmRhdGEuY29kZSA9PT0gMjAwKSB7XHJcbiAgICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn6IO96YeP5YiG6YWN5a6M5oiQ77yBJyk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iDvemHj+WIhumFjeWksei0pTonLCBlbmVyZ3lSZXN1bHQpO1xyXG4gICAgICAgICAgbm90aWZpY2F0aW9uLndhcm5pbmcoJ+iDvemHj+WIhumFjeWksei0pScpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXN1bHRJbmRleCsrO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDlpITnkIbmqKHmnb/liIbphY3nu5PmnpxcclxuICAgICAgaWYgKHRlbXBsYXRlUmVxdWVzdCkge1xyXG4gICAgICAgIGNvbnN0IHRlbXBsYXRlUmVzdWx0ID0gcmVzdWx0c1tyZXN1bHRJbmRleF07XHJcbiAgICAgICAgaWYgKHRlbXBsYXRlUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgJiYgdGVtcGxhdGVSZXN1bHQudmFsdWUuZGF0YS5jb2RlID09PSAyMDApIHtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfmqKHmnb/liIbphY3lrozmiJDvvIEnKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcign5qih5p2/5YiG6YWN5aSx6LSlOicsIHRlbXBsYXRlUmVzdWx0KTtcclxuICAgICAgICAgIG5vdGlmaWNhdGlvbi53YXJuaW5nKCfmqKHmnb/liIbphY3lpLHotKUnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIOW7tui/n+WFs+mXreW8ueeql++8jOiuqeeUqOaIt+iDveeci+WIsOaIkOWKn+aPkOekulxyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzZXRJc1B1Ymxpc2hpbmcoZmFsc2UpO1xyXG4gICAgICAgIGNsZWFyQWxsRGF0YSgpO1xyXG4gICAgICAgIG9uQ2xvc2UoKTtcclxuICAgICAgfSwgODAwKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WPkeW4g+S7u+WKoeWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5Lu75Yqh5Y+R5biD5aSx6LSl77yM6K+36YeN6K+VJyk7XHJcbiAgICAgIHNldElzUHVibGlzaGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8g6Ziy5q2i5rC05ZCI6ZSZ6K+v77yM5Zyo5a6i5oi356uv5oyC6L295YmN5LiN5riy5p+TXHJcbiAgaWYgKCFtb3VudGVkIHx8ICFpc09wZW4pIHJldHVybiBudWxsO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9XCJtb2RhbC1vdmVybGF5XCJcclxuICAgICAgb25XaGVlbD17KGUpID0+IHtcclxuICAgICAgICAvLyDlj6rpmLvmraLkuovku7bkvKDmkq3vvIzkvp3otZZDU1PmjqfliLbmu5rliqjooYzkuLpcclxuICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgICB9fVxyXG4gICAgICBvblRvdWNoTW92ZT17KGUpID0+IHtcclxuICAgICAgICAvLyDlj6rpmLvmraLkuovku7bkvKDmkq3vvIzkuI3osIPnlKhwcmV2ZW50RGVmYXVsdOmBv+WFjeiiq+WKqOebkeWQrOWZqOitpuWRilxyXG4gICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtd3JhcHBlclwiPlxyXG4gICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwibW9kYWwtY2xvc2UtYnRuLW91dHNpZGVcIiBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICBjbGVhckFsbERhdGEoKTtcclxuICAgICAgICAgIG9uQ2xvc2UoKTtcclxuICAgICAgICB9fT5cclxuICAgICAgICAgIDxYIHNpemU9ezIwfSAvPlxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtY29udGVudCB0ZW1wbGF0ZS1zZWxlY3Rpb24tbW9kYWxcIiBkYXRhLXN0ZXA9e2N1cnJlbnRTdGVwfT5cclxuXHJcbiAgICAgICAgey8qIOatpemqpOaMh+ekuuWZqCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0ZXAtaW5kaWNhdG9yXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0ZXAgY29tcGxldGVkXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RlcC1udW1iZXJcIj4xPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RlcC1sYWJlbFwiPumAieaLqeePree6pzwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHN0ZXAgJHtjdXJyZW50U3RlcCA9PT0gJ3RlbXBsYXRlJyA/ICdhY3RpdmUnIDogJ2NvbXBsZXRlZCd9YH0+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RlcC1udW1iZXJcIj4yPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RlcC1sYWJlbFwiPlxyXG4gICAgICAgICAgICAgIHthY3Rpb25UeXBlID09PSAn5YiG6YWN56ev5pyoJyA/ICfliIbphY3np6/mnKgnIDpcclxuICAgICAgICAgICAgICAgYWN0aW9uVHlwZSA9PT0gJ+WIhumFjeiDvemHjycgPyAn5YiG6YWN6IO96YePJyA6XHJcbiAgICAgICAgICAgICAgICfog73ph4/lkoznp6/mnKgnfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAge2FjdGlvblR5cGUgPT09ICflv6vpgJ/kuIror74nICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BzdGVwICR7Y3VycmVudFN0ZXAgPT09ICdwdWJsaXNoJyA/ICdhY3RpdmUnIDogJyd9YH0+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGVwLW51bWJlclwiPjM8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0ZXAtbGFiZWxcIj7lj5HluIPku7vliqE8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAge2FjdGlvblR5cGUgPT09ICflj5HluIPku7vliqEnICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BzdGVwICR7Y3VycmVudFN0ZXAgPT09ICdwdWJsaXNoJyA/ICdhY3RpdmUnIDogJyd9YH0+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGVwLW51bWJlclwiPjI8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0ZXAtbGFiZWxcIj7lj5HluIPku7vliqE8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7Lyog5YaF5a655Yy65Z+fICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtY29udGVudC1ib2R5XCI+XHJcbiAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09ICd0ZW1wbGF0ZScgPyAoXHJcbiAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgey8qIOiDvemHj+WIhumFjemDqOWIhiAtIOWPquWcqOmdnue6r+enr+acqOWIhumFjeaooeW8j+aXtuaYvuekuiAqL31cclxuICAgICAgICAgICAgICB7IWlzQmxvY2tzT25seU1vZGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtb2RhbC1jb250ZW50LWhlYWRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwic2VjdGlvbi10aXRsZVwiPuS4uuWtpueUn+WIhumFjeiDvemHjzwvaDM+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1vZGFsLWNvbnRlbnQtc2Nyb2xsYWJsZVwiPlxyXG4gICAgICAgICAgICAgICAgey8qIOWIhumFjemAiemhuSAtIOWPquWcqOmdnue6r+enr+acqOWIhumFjeaooeW8j+aXtuaYvuekuiAqL31cclxuICAgICAgICAgICAgICAgIHshaXNCbG9ja3NPbmx5TW9kZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGlzdHJpYnV0aW9uLW9wdGlvbnNcIj5cclxuICAgICAgICAgICAgICAgIHtkaXN0cmlidXRpb25PcHRpb25zLm1hcCgob3B0aW9uKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICBrZXk9e29wdGlvbi5pZH1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BkaXN0cmlidXRpb24tY2FyZCAke21vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gb3B0aW9uLmlkID8gJ3NlbGVjdGVkJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGlzdHJpYnV0aW9uU2VsZWN0KG9wdGlvbi5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBzZXRIb3ZlcmVkT3B0aW9uKG9wdGlvbi5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRIb3ZlcmVkT3B0aW9uKCcnKX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGlzdHJpYnV0aW9uLWxhYmVsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEFtb3VudCA9IG9wdGlvbi5pZCA9PT0gJ2Fzc2lnbicgPyBtb2RhbERhdGEuYXNzaWduRW5lcmd5QW1vdW50IDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb24uaWQgPT09ICdkaXN0cmlidXRlJyA/IG1vZGFsRGF0YS5kaXN0cmlidXRlRW5lcmd5QW1vdW50IDogJyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBvcHRpb24uaGFzSW5wdXQgJiYgY3VycmVudEFtb3VudCAmJiBOdW1iZXIoY3VycmVudEFtb3VudCkgPiAwICYmIG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gb3B0aW9uLmlkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtvcHRpb24ubGFiZWx9ICR7Y3VycmVudEFtb3VudH3og73ph49gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBvcHRpb24ubGFiZWw7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHtvcHRpb24uaGFzSW5wdXQgJiYgKG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gb3B0aW9uLmlkIHx8IGZvY3VzZWRJbnB1dCA9PT0gb3B0aW9uLmlkKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVuZXJneS1pbnB1dC1jb250YWluZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXtvcHRpb24uaWQgPT09ICdhc3NpZ24nID8gYXNzaWduSW5wdXRSZWYgOiBkaXN0cmlidXRlSW5wdXRSZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZW5lcmd5LWlucHV0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17b3B0aW9uLmlkID09PSAnYXNzaWduJyA/ICfovpPlhaXog73ph48nIDogJ+i+k+WFpeebruagh+WAvCd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29wdGlvbi5pZCA9PT0gJ2Fzc2lnbicgPyBtb2RhbERhdGEuYXNzaWduRW5lcmd5QW1vdW50IDogbW9kYWxEYXRhLmRpc3RyaWJ1dGVFbmVyZ3lBbW91bnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXBkYXRlS2V5ID0gb3B0aW9uLmlkID09PSAnYXNzaWduJyA/ICdhc3NpZ25FbmVyZ3lBbW91bnQnIDogJ2Rpc3RyaWJ1dGVFbmVyZ3lBbW91bnQnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3JLZXkgPSBvcHRpb24uaWQgPT09ICdhc3NpZ24nID8gJ2Fzc2lnbkVuZXJneUVycm9yJyA6ICdkaXN0cmlidXRlRW5lcmd5RXJyb3InO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOa4hemZpOS5i+WJjeeahOmUmeivr1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SW5wdXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZXJyb3JLZXldOiAnJyB9KSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5YWB6K6456m65YC85oiW5q2j5pW05pWwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsdWUgPT09ICcnKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldE1vZGFsRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFt1cGRhdGVLZXldOiB2YWx1ZSB9KSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBudW1WYWx1ZSA9IE51bWJlcih2YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChOdW1iZXIuaXNJbnRlZ2VyKG51bVZhbHVlKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChudW1WYWx1ZSA8IDEpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOiuvue9rumUmeivr+aPkOekulxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0SW5wdXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZXJyb3JLZXldOiAn6L6T5YWl6IO96YeP5LiN6IO95L2O5LqOMScgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TW9kYWxEYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgW3VwZGF0ZUtleV06IHZhbHVlIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5pyJ5pWI6L6T5YWlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRNb2RhbERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbdXBkYXRlS2V5XTogdmFsdWUgfSkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6L6T5YWl5pWw5a2X5pe26Ieq5Yqo6YCJ5Lit5b2T5YmN5oKs5YGc55qE5YiG6YWN5oyJ6ZKuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRNb2RhbERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzZWxlY3RlZERpc3RyaWJ1dGlvbjogb3B0aW9uLmlkIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g54K55Ye76L6T5YWl5qGG5pe26Ieq5Yqo6YCJ5Lit5b2T5YmN5oKs5YGc55qE5YiG6YWN5oyJ6ZKuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRNb2RhbERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzZWxlY3RlZERpc3RyaWJ1dGlvbjogb3B0aW9uLmlkIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uRm9jdXM9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvY3VzZWRJbnB1dChvcHRpb24uaWQpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6IGa54Sm5pe25Lmf6Ieq5Yqo6YCJ5Lit5YiG6YWN6YCJ6aG5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRNb2RhbERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzZWxlY3RlZERpc3RyaWJ1dGlvbjogb3B0aW9uLmlkIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cj17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Rm9jdXNlZElucHV0KCcnKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Lyog6ZSZ6K+v5o+Q56S6ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7KCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBlcnJvcktleSA9IG9wdGlvbi5pZCA9PT0gJ2Fzc2lnbicgPyAnYXNzaWduRW5lcmd5RXJyb3InIDogJ2Rpc3RyaWJ1dGVFbmVyZ3lFcnJvcic7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gaW5wdXRFcnJvcnNbZXJyb3JLZXkgYXMga2V5b2YgdHlwZW9mIGlucHV0RXJyb3JzXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3JNZXNzYWdlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjZWY0NDQ0JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxMnB4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luVG9wOiAnNHB4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlcnJvck1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIHsvKiDmmL7npLrog73ph4/liIbphY3kv6Hmga8gLSDlj6rlnKjpnZ7nuq/np6/mnKjliIbphY3mqKHlvI/ml7bmmL7npLogKi99XHJcbiAgICAgICAgICAgICAgeyFpc0Jsb2Nrc09ubHlNb2RlICYmIChtb2RhbERhdGEuc2VsZWN0ZWREaXN0cmlidXRpb24gPT09ICdhc3NpZ24nIHx8IG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gJ2Rpc3RyaWJ1dGUnKSAmJiBtb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50cy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWF2YWlsYWJsZS1lbmVyZ3lcIj5cclxuICAgICAgICAgICAgICAgICAge2xvYWRpbmdTdHVkZW50UG9pbnRzID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAnIzZiNzI4MCcsIGZvbnRTdHlsZTogJ2l0YWxpYycgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICDij7Mg5q2j5Zyo6I635Y+W6IO96YeP5L+h5oGvLi4uXHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBkaXNwbGF5SW5mbyA9IGdldEVuZXJneURpc3BsYXlJbmZvKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiAnIzY0NzQ4YicsIGZvbnRTaXplOiAnMTJweCcgfX0+8J+SoTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57ZGlzcGxheUluZm8ubGFiZWx9OiB7ZGlzcGxheUluZm8udmFsdWV9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgfSkoKVxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgey8qIOmUmeivr+aPkOekuiAtIOWPquWcqOmdnue6r+enr+acqOWIhumFjeaooeW8j+aXtuaYvuekuiAqL31cclxuICAgICAgICAgICAgICB7IWlzQmxvY2tzT25seU1vZGUgJiYgKG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbiA9PT0gJ2Fzc2lnbicgfHwgbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpICYmIGdldEN1cnJlbnRFbmVyZ3lBbW91bnQoKSAmJiAhbG9hZGluZ1N0dWRlbnRQb2ludHMgJiYgKFxyXG4gICAgICAgICAgICAgICAgKCgpID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgZW5lcmd5QW1vdW50TnVtID0gTnVtYmVyKGdldEN1cnJlbnRFbmVyZ3lBbW91bnQoKSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAvLyDmoLnmja7liIbphY3mlrnlvI/ov5vooYzkuI3lkIznmoTpqozor4FcclxuICAgICAgICAgICAgICAgICAgbGV0IHNob3VsZFNob3dFcnJvciA9IGZhbHNlO1xyXG4gICAgICAgICAgICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJyc7XHJcblxyXG4gICAgICAgICAgICAgICAgICBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnYXNzaWduJykge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1pbkF2YWlsYWJsZSA9IGdldE1pbkF2YWlsYWJsZVBvaW50cygpO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChlbmVyZ3lBbW91bnROdW0gPiBtaW5BdmFpbGFibGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNob3VsZFNob3dFcnJvciA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAn5Y+v5YiG6YWN56ev5YiG5LiN6LazJztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uID09PSAnZGlzdHJpYnV0ZScpIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyDlr7nkuo5cIuWIhumFjeiHs1wi77yM5qOA5p+l5piv5ZCm5pyJ5a2m55Sf5peg5rOV6L6+5Yiw55uu5qCH5YC8XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3R1ZGVudHNOZWVkaW5nRW5lcmd5ID0gbW9kYWxEYXRhLnNlbGVjdGVkU3R1ZGVudHMuZmlsdGVyKHN0dWRlbnRJZCA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50UG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKSB8fCAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRQb2ludHMgPCBlbmVyZ3lBbW91bnROdW07XHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGluc3VmZmljaWVudFN0dWRlbnRzID0gc3R1ZGVudHNOZWVkaW5nRW5lcmd5LmZpbHRlcihzdHVkZW50SWQgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFBvaW50cyA9IHN0dWRlbnRQb2ludHNNYXAuZ2V0KHN0dWRlbnRJZCkgfHwgMDtcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5lZWRlZFBvaW50cyA9IGVuZXJneUFtb3VudE51bSAtIGN1cnJlbnRQb2ludHM7XHJcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzdHVkZW50QXZhaWxhYmxlUG9pbnRzID0gc3R1ZGVudFBvaW50c01hcC5nZXQoc3R1ZGVudElkKTtcclxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzdHVkZW50QXZhaWxhYmxlUG9pbnRzICE9PSB1bmRlZmluZWQgJiYgc3R1ZGVudEF2YWlsYWJsZVBvaW50cyA8IG5lZWRlZFBvaW50cztcclxuICAgICAgICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGluc3VmZmljaWVudFN0dWRlbnRzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNob3VsZFNob3dFcnJvciA9IHRydWU7XHJcbiAgICAgICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAn6YOo5YiG5a2m55Sf56ev5YiG5LiN6Laz5Lul6L6+5Yiw55uu5qCH5YC8JztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgIGlmIChzaG91bGRTaG93RXJyb3IpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZlZjJmMicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZmVjYWNhJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMTZweCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpblRvcDogJzhweCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzEycHgnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBnYXA6ICcxMnB4J1xyXG4gICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAnOHB4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBmbGV4OiAxXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyNlZjQ0NDQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxM3B4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDimqDvuI8ge2Vycm9yTWVzc2FnZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZjk3MzE2JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzZweCAxMnB4JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTJweCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmxleFNocmluazogMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnI2VhNTgwYyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICcjZjk3MzE2JztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUJhdGNoVXNlS2V5UGFja2FnZU1vZGFsT3BlbigpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICDlhZHmjaLlr4bpkqVcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICAgICAgICAgICAgfSkoKVxyXG4gICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgIHsvKiDkuLrlrabnlJ/liIbphY3np6/mnKjmoIfpopggLSDlj6rlnKjpnZ7nuq/og73ph4/liIbphY3mqKHlvI/ml7bmmL7npLogKi99XHJcbiAgICAgICAgICAgICAgeyFpc0VuZXJneU9ubHlNb2RlICYmIChcclxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJzZWN0aW9uLXRpdGxlXCI+5Li65a2m55Sf5YiG6YWN56ev5pyoPC9oMz5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7Lyog5qih5p2/6YCJ5oup5Yy65Z+fIC0g5Y+q5Zyo6Z2e57qv6IO96YeP5YiG6YWN5qih5byP5pe25pi+56S6ICovfVxyXG4gICAgICAgICAgICAgIHshaXNFbmVyZ3lPbmx5TW9kZSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLXNlbGVjdGlvbi1hcmVhXCI+XHJcbiAgICAgICAgICAgICAgICAgIHttb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZSA/IChcclxuICAgICAgICAgICAgICAgICAgICAvLyDlt7LpgInmi6nmqKHmnb/ml7bmmL7npLrmqKHmnb/kv6Hmga9cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLXNlbGVjdGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlbXBsYXRlLWljb25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAg4q2QXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVtcGxhdGUtaW5mb1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZW1wbGF0ZS1uYW1lXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge21vZGFsRGF0YS5zZWxlY3RlZFRlbXBsYXRlLnRlbXBsYXRlTmFtZSB8fCBtb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRlbXBsYXRlLWxhYmVsXCI+5bey6YCJ5oup5qih5p2/PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNoYW5nZS10ZW1wbGF0ZS1idG5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVUZW1wbGF0ZVBpY2tlck9wZW59XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIOabtOaNolxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNhbmNlbC10ZW1wbGF0ZS1idG5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDYW5jZWxUZW1wbGF0ZX1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgw5dcclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgIC8vIOacqumAieaLqeaooeadv+aXtuaYvuekuumAieaLqemAiemhuVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVtcGxhdGUtb3B0aW9uc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZW1wbGF0ZS1vcHRpb24gdGVtcGxhdGUtbm9uZSBzZWxlY3RlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHsvKiDkuI3liIbphY3mqKHmnb/vvIzkv53mjIHlvZPliY3nirbmgIEgKi99fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9wdGlvbi1pY29uXCI+4pyP77iPPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3B0aW9uLWNvbnRlbnRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9wdGlvbi10aXRsZVwiPuS4jeWIhumFjeaooeadvzwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3B0aW9uLWRlc2NcIj7kv53mjIHljp/mnInorr7nva48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGVtcGxhdGUtb3B0aW9uIHRlbXBsYXRlLXNlbGVjdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVRlbXBsYXRlUGlja2VyT3Blbn1cclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvcHRpb24taWNvblwiPvCfp6k8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvcHRpb24tY29udGVudFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3B0aW9uLXRpdGxlXCI+6YCJ5oup5qih5p2/PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvcHRpb24tZGVzY1wiPuS4uueUqOaIt+aPkOS+m+enr+acqOaooeadvzwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAvLyDlj5HluIPku7vliqHmraXpqqTnmoTlhoXlrrlcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdWJsaXNoLXRhc2stY29udGVudFwiPlxyXG4gICAgICAgICAgICAgIHsvKiDmoIfnrb7pobXliIfmjaIgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YWItc3dpdGNoZXJcIj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGFiLWJ0biAke2FjdGl2ZVRhYiA9PT0gJ3Rhc2snID8gJ2FjdGl2ZScgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ3Rhc2snKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAg5Lu75Yqh5L+h5oGvXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGFiLWJ0biAke2FjdGl2ZVRhYiA9PT0gJ3Jlc291cmNlcycgPyAnYWN0aXZlJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYigncmVzb3VyY2VzJyl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIOi1hOa6kOS4jumZhOS7tlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtY29udGVudC1zY3JvbGxhYmxlXCI+XHJcbiAgICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSAndGFzaycgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1pbmZvLXRhYlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1ncm91cFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5Lu75Yqh5ZCN56ewXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Rhc2tEYXRhLnRhc2tOYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRhc2tEYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgdGFza05hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1ncm91cFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0tdGV4dGFyZWFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuS7u+WKoeaPj+i/sFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0YXNrRGF0YS50YXNrRGVzY3JpcHRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VGFza0RhdGEocHJldiA9PiAoeyAuLi5wcmV2LCB0YXNrRGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1ncm91cFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWxmLWFzc2Vzc21lbnQtc2VjdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dGFza0RhdGEuc2VsZkFzc2Vzc21lbnRJdGVtcy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhZGQtc2VsZi1hc3Nlc3NtZW50LWJ0blwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUYXNrRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNlbGZBc3Nlc3NtZW50SXRlbXM6IFsnJ10gfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIOa3u+WKoOiHquivhOmhuVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPuiHquivhOmhuTwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFza0RhdGEuc2VsZkFzc2Vzc21lbnRJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwic2VsZi1hc3Nlc3NtZW50LWl0ZW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2Doh6ror4TpobkgJHtpbmRleCArIDF9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW1zID0gWy4uLnRhc2tEYXRhLnNlbGZBc3Nlc3NtZW50SXRlbXNdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdJdGVtc1tpbmRleF0gPSBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGFza0RhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzZWxmQXNzZXNzbWVudEl0ZW1zOiBuZXdJdGVtcyB9KSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZW1vdmUtYnRuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3SXRlbXMgPSB0YXNrRGF0YS5zZWxmQXNzZXNzbWVudEl0ZW1zLmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUYXNrRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNlbGZBc3Nlc3NtZW50SXRlbXM6IG5ld0l0ZW1zIH0pKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgw5dcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFkZC1idG5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUYXNrRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNlbGZBc3Nlc3NtZW50SXRlbXM6IFsuLi5wcmV2LnNlbGZBc3Nlc3NtZW50SXRlbXMsICcnXSB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcgfX0+4p6VPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDmt7vliqDoh6ror4TpoblcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHmjIHnu63ml7bpl7Torr7nva4gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLWdyb3VwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRpbWUtc2V0dGluZ3NcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7ku7vliqHmjIHnu63ml7bpl7Q8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImR1cmF0aW9uLW9wdGlvbnNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BkdXJhdGlvbi1vcHRpb24gJHt0YXNrRGF0YS5kdXJhdGlvbiA9PT0gJzHlsI/ml7YnID8gJ2FjdGl2ZScgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRHVyYXRpb25TZWxlY3QoJzHlsI/ml7YnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAx5bCP5pe2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZHVyYXRpb24tb3B0aW9uICR7dGFza0RhdGEuZHVyYXRpb24gPT09ICcz5bCP5pe2JyA/ICdhY3RpdmUnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUR1cmF0aW9uU2VsZWN0KCcz5bCP5pe2Jyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgM+Wwj+aXtlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGR1cmF0aW9uLW9wdGlvbiAke3Rhc2tEYXRhLmR1cmF0aW9uID09PSAnMeWkqScgPyAnYWN0aXZlJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEdXJhdGlvblNlbGVjdCgnMeWkqScpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDHlpKlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BkdXJhdGlvbi1vcHRpb24gJHt0YXNrRGF0YS5kdXJhdGlvbiA9PT0gJzflpKknID8gJ2FjdGl2ZScgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRHVyYXRpb25TZWxlY3QoJzflpKknKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA35aSpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZHVyYXRpb24tb3B0aW9uICR7dGFza0RhdGEuZHVyYXRpb24gPT09ICcx5Liq5pyIJyA/ICdhY3RpdmUnIDogJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUR1cmF0aW9uU2VsZWN0KCcx5Liq5pyIJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgMeS4quaciFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZHVyYXRpb24taGludFwiPuS7u+WKoeWwhuS7juWIm+W7uuaXtuW8gOWni++8jOaMgee7reaJgOmAieaXtumXtDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVzb3VyY2VzLXRhYlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29ya3Mtc2VjdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGg0PumAieaLqeS9nOWTgTwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJoZWxwLXRleHRcIj7pgInmi6nkvZzlk4HkvZzkuLrku7vliqHlj4LogIPotYTmlpnvvIjlj6/lpJrpgInvvIk8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHdvcmtzLXNjcm9sbC13cmFwcGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25XaGVlbD17aGFuZGxlV2hlZWxTY3JvbGx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VEb3duPXtoYW5kbGVNb3VzZURvd25XcmFwcGVyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTW92ZT17aGFuZGxlTW91c2VNb3ZlV3JhcHBlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZVVwPXtoYW5kbGVNb3VzZVVwV3JhcHBlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXtoYW5kbGVNb3VzZUxlYXZlV3JhcHBlcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ6ICcyMDBweCcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAnZ3JhYicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlclNlbGVjdDogJ25vbmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nV29ya3MgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibG9hZGluZy1jb250YWluZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgbWluSGVpZ2h0OiAnMjAwcHgnIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXJcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWKoOi9veS4rS4uLjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IHdvcmtzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3Jrcy1ob3Jpem9udGFsLXNjcm9sbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOWvueS9nOWTgei/m+ihjOaOkuW6j++8muW3sumAieS4reeahOS9nOWTgeaYvuekuuWcqOWJjemdoiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3b3Jrc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuc29ydCgoYSwgYikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGFTZWxlY3RlZCA9IHNlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyhhLmlkKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBiU2VsZWN0ZWQgPSBzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMoYi5pZCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5bey6YCJ5Lit55qE5o6S5Zyo5YmN6Z2iXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFTZWxlY3RlZCAmJiAhYlNlbGVjdGVkKSByZXR1cm4gLTE7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFhU2VsZWN0ZWQgJiYgYlNlbGVjdGVkKSByZXR1cm4gMTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLm1hcCgod29yaykgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt3b3JrLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHdvcmstY2FyZCAke3NlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyh3b3JrLmlkKSA/ICdzZWxlY3RlZCcgOiAnJ31gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNlbGVjdFdvcmtEYXRhKHdvcmsuaWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS9nOWTgemihOiniOWMuuWfnyAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndvcmstcHJldmlld1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3dvcmsuY292ZXJJbWFnZSB8fCB3b3JrLnNjcmVlblNob3RJbWFnZSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17Zml4SW1hZ2VVcmwod29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3dvcmsudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwid29yay1pbWFnZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dEVsZW1lbnQgPSBlLmN1cnJlbnRUYXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nIGFzIEhUTUxFbGVtZW50O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRFbGVtZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFbGVtZW50LnN0eWxlLmRpc3BsYXkgPSAnZmxleCc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLXBsYWNlaG9sZGVyXCIgc3R5bGU9e3sgZGlzcGxheTogd29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gJ25vbmUnIDogJ2ZsZXgnIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMjRcIiBoZWlnaHQ9XCIyNFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTQgNEgyMEMyMC41NTIzIDQgMjEgNC40NDc3MiAyMSA1VjE5QzIxIDE5LjU1MjMgMjAuNTUyMyAyMCAyMCAyMEg0QzMuNDQ3NzIgMjAgMyAxOS41NTIzIDMgMTlWNUMzIDQuNDQ3NzIgMy40NDc3MiA0IDQgNFpcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIi8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk05IDlDOS41NTIyOCA5IDEwIDguNTUyMjggMTAgOEMxMCA3LjQ0NzcyIDkuNTUyMjggNyA5IDdDOC40NDc3MiA3IDggNy40NDc3MiA4IDhDOCA4LjU1MjI4IDguNDQ3NzIgOSA5IDlaXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIxIDE1TDE2IDEwTDExIDE1SDIxWlwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIi8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog6YCJ5oup5oyH56S65ZmoICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BzZWxlY3Rpb24taW5kaWNhdG9yICR7c2VsZWN0ZWRXb3JrSWRzLmluY2x1ZGVzKHdvcmsuaWQpID8gJ3NlbGVjdGVkJyA6ICcnfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMTJcIiBoZWlnaHQ9XCIxMlwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTkgMTJMMTEgMTRMMTUgMTBcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIiBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDkvZzlk4Hkv6Hmga/ljLrln58gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLWluZm9cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay10aXRsZVwiPnt3b3JrLnRpdGxlIHx8IHdvcmsubmFtZSB8fCB3b3JrLndvcmtOYW1lIHx8ICfmnKrlkb3lkI3kvZzlk4EnfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOWKoOi9veabtOWkmuaMiemSriAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoYXNNb3JlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkLW1vcmUtY29udGFpbmVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibG9hZC1tb3JlLWJ0blwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2FkTW9yZVdvcmtzRGF0YX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nTW9yZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZ01vcmUgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXItc21hbGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liqDovb3kuK0uLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liqDovb3mm7TlpJo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEyIDVWMTlNNSAxMkwxMiAxOUwxOSAxMlwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIi8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktcGxhY2Vob2xkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktdGV4dFwiPuS9nOWTgeWIl+ihqDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudHMtc2VjdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGg0PumZhOS7tuS4iuS8oDwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInVwbG9hZC1hcmVhXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBtdWx0aXBsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cIi5qcGcsLmpwZWcsLnBuZywuZ2lmLC5wZGYsLmRvYywuZG9jeCwueGxzLC54bHN4LC5wcHQsLnBwdHgsLnR4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQuZmlsZXMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZXMgPSBBcnJheS5mcm9tKGUudGFyZ2V0LmZpbGVzKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsaWRGaWxlczogRmlsZVtdID0gW107XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGludmFsaWRGaWxlczogc3RyaW5nW10gPSBbXTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaUr+aMgeeahOaWh+S7tuagvOW8j1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvanBnJywgJ2ltYWdlL3BuZycsICdpbWFnZS9naWYnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdhcHBsaWNhdGlvbi9wZGYnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdhcHBsaWNhdGlvbi9tc3dvcmQnLCAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnLCAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtcG93ZXJwb2ludCcsICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQucHJlc2VudGF0aW9ubWwucHJlc2VudGF0aW9uJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndGV4dC9wbGFpbidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaWh+S7tuaJqeWxleWQjeajgOafpe+8iOS9nOS4uuWkh+eUqOmqjOivge+8iVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBhbGxvd2VkRXh0ZW5zaW9ucyA9IFsnLmpwZycsICcuanBlZycsICcucG5nJywgJy5naWYnLCAnLnBkZicsICcuZG9jJywgJy5kb2N4JywgJy54bHMnLCAnLnhsc3gnLCAnLnBwdCcsICcucHB0eCcsICcudHh0J107XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlcy5mb3JFYWNoKGZpbGUgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOajgOafpeaWh+S7tuWkp+Wwj++8iDEwTUIgPSAxMCAqIDEwMjQgKiAxMDI0IGJ5dGVz77yJXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGludmFsaWRGaWxlcy5wdXNoKGAke2ZpbGUubmFtZX3vvJrmlofku7blpKflsI/otoXov4cxME1CYCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlRXh0ZW5zaW9uID0gJy4nICsgZmlsZS5uYW1lLnNwbGl0KCcuJykucG9wKCk/LnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNWYWxpZFR5cGUgPSBhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSB8fCBhbGxvd2VkRXh0ZW5zaW9ucy5pbmNsdWRlcyhmaWxlRXh0ZW5zaW9uKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1ZhbGlkVHlwZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW52YWxpZEZpbGVzLnB1c2goYCR7ZmlsZS5uYW1lfe+8muS4jeaUr+aMgeeahOaWh+S7tuagvOW8j2ApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsaWRGaWxlcy5wdXNoKGZpbGUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOa3u+WKoOacieaViOaWh+S7tlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsaWRGaWxlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QXR0YWNobWVudHMocHJldiA9PiBbLi4ucHJldiwgLi4udmFsaWRGaWxlc10pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmmL7npLrplJnor6/kv6Hmga9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGludmFsaWRGaWxlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxlcnQoYOS7peS4i+aWh+S7tuaXoOazleS4iuS8oO+8mlxcbiR7aW52YWxpZEZpbGVzLmpvaW4oJ1xcbicpfWApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDph43nva5pbnB1dOeahHZhbHVl77yM56Gu5L+d5Y+v5Lul6YeN5aSN6YCJ5oup55u45ZCM5paH5Lu2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnZhbHVlID0gJyc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiAnbm9uZScgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImZpbGUtdXBsb2FkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJmaWxlLXVwbG9hZFwiIGNsYXNzTmFtZT1cInVwbG9hZC1idG5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICArXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZpbGUtZm9ybWF0LWluZm9cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICDmlK/mjIEganBn44CBcG5n44CBZ2lm44CBcGRm44CBZG9j44CBZG9jeOOAgXhsc+OAgXhsc3jjgIFwcHTjgIFwcHR444CBdHh0IOagvOW8j++8jOWNleS4quaWh+S7tuWkp+Wwj+S4jei2hei/hzEwTUJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7YXR0YWNobWVudHMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudHMtbGlzdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHthdHRhY2htZW50cy5tYXAoKGZpbGUsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtaXRlbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmaWxlLW5hbWVcIj57ZmlsZS5uYW1lfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEF0dGFjaG1lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLWF0dGFjaG1lbnQtYnRuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIOW6lemDqOaMiemSriAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtZm9vdGVyXCI+XHJcbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicHJldi1idG5cIiBvbkNsaWNrPXtoYW5kbGVQcmV2aW91c30+XHJcbiAgICAgICAgICAgICAg5LiK5LiA5q2lXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbmV4dC1idG4gJHtcclxuICAgICAgICAgICAgICAgIChjdXJyZW50U3RlcCA9PT0gJ3RlbXBsYXRlJyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIGlzRW5lcmd5T25seU1vZGVcclxuICAgICAgICAgICAgICAgICAgICA/IG1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvblxyXG4gICAgICAgICAgICAgICAgICAgIDogaXNCbG9ja3NPbmx5TW9kZVxyXG4gICAgICAgICAgICAgICAgICAgICAgPyBtb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgOiAobW9kYWxEYXRhLnNlbGVjdGVkVGVtcGxhdGUgfHwgbW9kYWxEYXRhLnNlbGVjdGVkRGlzdHJpYnV0aW9uKVxyXG4gICAgICAgICAgICAgICAgKSkgfHxcclxuICAgICAgICAgICAgICAgIChjdXJyZW50U3RlcCA9PT0gJ3B1Ymxpc2gnICYmIHRhc2tEYXRhLnRhc2tOYW1lLnRyaW0oKSAmJiAhaXNQdWJsaXNoaW5nKVxyXG4gICAgICAgICAgICAgICAgPyAnZW5hYmxlZCcgOiAnZGlzYWJsZWQnXHJcbiAgICAgICAgICAgICAgfSAke2lzUHVibGlzaGluZyA/ICdwdWJsaXNoaW5nJyA6ICcnfWB9XHJcbiAgICAgICAgICAgICAgb25DbGljaz17Y3VycmVudFN0ZXAgPT09ICd0ZW1wbGF0ZScgPyBoYW5kbGVOZXh0IDogaGFuZGxlQ29uZmlybX1cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICAgICBjdXJyZW50U3RlcCA9PT0gJ3RlbXBsYXRlJ1xyXG4gICAgICAgICAgICAgICAgICA/IChpc0VuZXJneU9ubHlNb2RlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/ICFtb2RhbERhdGEuc2VsZWN0ZWREaXN0cmlidXRpb25cclxuICAgICAgICAgICAgICAgICAgICAgIDogaXNCbG9ja3NPbmx5TW9kZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICFtb2RhbERhdGEuc2VsZWN0ZWRUZW1wbGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICghbW9kYWxEYXRhLnNlbGVjdGVkVGVtcGxhdGUgJiYgIW1vZGFsRGF0YS5zZWxlY3RlZERpc3RyaWJ1dGlvbikpXHJcbiAgICAgICAgICAgICAgICAgIDogIXRhc2tEYXRhLnRhc2tOYW1lLnRyaW0oKSB8fCBpc1B1Ymxpc2hpbmdcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09ICd0ZW1wbGF0ZScgPyAoXHJcbiAgICAgICAgICAgICAgICAoYWN0aW9uVHlwZSA9PT0gJ+WPkeW4g+S7u+WKoScgfHwgYWN0aW9uVHlwZSA9PT0gJ+W/q+mAn+S4iuivvicpID8gJ+S4i+S4gOatpScgOiAn5YiG6YWNJ1xyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICBpc1B1Ymxpc2hpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB1Ymxpc2hpbmctY29udGVudFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNwaW5uZXJcIj48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAg5Y+R5biD5LitLi4uXHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICkgOiAn5byA5aeL5LiK6K++J1xyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiDmqKHmnb/pgInmi6nlvLnnqpcgKi99XHJcbiAgICAgIDxUZW1wbGF0ZVBpY2tlck1vZGFsXHJcbiAgICAgICAgaXNPcGVuPXtpc1RlbXBsYXRlUGlja2VyT3Blbn1cclxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVUZW1wbGF0ZVBpY2tlckNsb3NlfVxyXG4gICAgICAgIG9uVGVtcGxhdGVTZWxlY3Q9e2hhbmRsZVRlbXBsYXRlU2VsZWN0fVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIOaJuemHj+WFkeaNouWvhumSpeaooeaAgeahhiAqL31cclxuICAgICAgPEJhdGNoVXNlS2V5UGFja2FnZU1vZGFsXHJcbiAgICAgICAgb3Blbj17aXNCYXRjaFVzZUtleVBhY2thZ2VNb2RhbE9wZW59XHJcbiAgICAgICAgc2VsZWN0ZWRTdHVkZW50SWRzPXttb2RhbERhdGEuc2VsZWN0ZWRTdHVkZW50c31cclxuICAgICAgICBzdHVkZW50cz17c3R1ZGVudHN9XHJcbiAgICAgICAgb25DbG9zZT17aGFuZGxlQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxDbG9zZX1cclxuICAgICAgICBvblN1Y2Nlc3M9e2hhbmRsZUJhdGNoVXNlS2V5UGFja2FnZVN1Y2Nlc3N9XHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgVGVtcGxhdGVTZWxlY3Rpb25Nb2RhbDtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJYIiwiR2V0Tm90aWZpY2F0aW9uIiwiVGVtcGxhdGVQaWNrZXJNb2RhbCIsIkJhdGNoVXNlS2V5UGFja2FnZU1vZGFsIiwicG9pbnRzQXBpIiwidGFza0FwaSIsIlRhc2tUeXBlIiwiUHJpb3JpdHkiLCJiYXRjaEFkZFVzZXJKb2luUm9sZSIsImZldGNoQ2xhc3NTdHVkZW50c1dpdGhOb3RpZmljYXRpb24iLCJmZXRjaFN0dWRlbnRQb2ludHMiLCJmaXhJbWFnZVVybCIsImZldGNoV29ya3MiLCJoYW5kbGVTZWxlY3RXb3JrIiwiaGFuZGxlV2hlZWxTY3JvbGwiLCJoYW5kbGVNb3VzZURvd24iLCJoYW5kbGVNb3VzZU1vdmUiLCJoYW5kbGVNb3VzZVVwIiwiaGFuZGxlTW91c2VMZWF2ZSIsImdldFRhc2tUaW1lcyIsIlRlbXBsYXRlU2VsZWN0aW9uTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwib25CYWNrIiwib25Db25maXJtIiwiYWN0aW9uVHlwZSIsInNlbGVjdGVkU2Nob29sIiwic2VsZWN0ZWRDbGFzcyIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiaG92ZXJlZE9wdGlvbiIsInNldEhvdmVyZWRPcHRpb24iLCJmb2N1c2VkSW5wdXQiLCJzZXRGb2N1c2VkSW5wdXQiLCJhc3NpZ25JbnB1dFJlZiIsImRpc3RyaWJ1dGVJbnB1dFJlZiIsImlzVGVtcGxhdGVQaWNrZXJPcGVuIiwic2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4iLCJpc0JhdGNoVXNlS2V5UGFja2FnZU1vZGFsT3BlbiIsInNldElzQmF0Y2hVc2VLZXlQYWNrYWdlTW9kYWxPcGVuIiwic3R1ZGVudHMiLCJzZXRTdHVkZW50cyIsInN0dWRlbnRQb2ludHNNYXAiLCJzZXRTdHVkZW50UG9pbnRzTWFwIiwiTWFwIiwibG9hZGluZ1N0dWRlbnRQb2ludHMiLCJzZXRMb2FkaW5nU3R1ZGVudFBvaW50cyIsIm1vZGFsRGF0YSIsInNldE1vZGFsRGF0YSIsInNlbGVjdGVkRGlzdHJpYnV0aW9uIiwiYXNzaWduRW5lcmd5QW1vdW50IiwiZGlzdHJpYnV0ZUVuZXJneUFtb3VudCIsInNlbGVjdGVkVGVtcGxhdGUiLCJzZWxlY3RlZFN0dWRlbnRzIiwiaW5wdXRFcnJvcnMiLCJzZXRJbnB1dEVycm9ycyIsImFzc2lnbkVuZXJneUVycm9yIiwiZGlzdHJpYnV0ZUVuZXJneUVycm9yIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsInRhc2tEYXRhIiwic2V0VGFza0RhdGEiLCJ0YXNrTmFtZSIsInRhc2tEZXNjcmlwdGlvbiIsInNlbGZBc3Nlc3NtZW50SXRlbXMiLCJkdXJhdGlvbiIsInN0YXJ0VGltZSIsImVuZFRpbWUiLCJzaG93RHVyYXRpb25TZWxlY3RvciIsInNldFNob3dEdXJhdGlvblNlbGVjdG9yIiwiaGFuZGxlQ2xpY2tPdXRzaWRlIiwiZXZlbnQiLCJ0YXJnZXQiLCJjbG9zZXN0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZUR1cmF0aW9uU2VsZWN0IiwicHJldiIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsIndvcmtzIiwic2V0V29ya3MiLCJzZWxlY3RlZFdvcmtJZHMiLCJzZXRTZWxlY3RlZFdvcmtJZHMiLCJhdHRhY2htZW50cyIsInNldEF0dGFjaG1lbnRzIiwibG9hZGluZ1dvcmtzIiwic2V0TG9hZGluZ1dvcmtzIiwicGFnZSIsInNldFBhZ2UiLCJoYXNNb3JlIiwic2V0SGFzTW9yZSIsImxvYWRpbmdNb3JlIiwic2V0TG9hZGluZ01vcmUiLCJpc1B1Ymxpc2hpbmciLCJzZXRJc1B1Ymxpc2hpbmciLCJpc0RyYWdnaW5nIiwic2V0SXNEcmFnZ2luZyIsImRyYWdTdGFydCIsInNldERyYWdTdGFydCIsIngiLCJzY3JvbGxMZWZ0IiwicGFnZVNpemUiLCJvcmlnaW5hbFN0eWxlIiwid2luZG93IiwiZ2V0Q29tcHV0ZWRTdHlsZSIsImJvZHkiLCJvdmVyZmxvdyIsInN0eWxlIiwiZmV0Y2hXb3Jrc0RhdGEiLCJwYWdlTnVtIiwiaXNMb2FkTW9yZSIsImN1cnJlbnRTdGF0ZSIsInNldFN0YXRlIiwibmV3U3RhdGUiLCJ1bmRlZmluZWQiLCJsb2FkTW9yZVdvcmtzRGF0YSIsIm5leHRQYWdlIiwiaGFuZGxlU2VsZWN0V29ya0RhdGEiLCJ3b3JrSWQiLCJoYW5kbGVNb3VzZURvd25XcmFwcGVyIiwiZSIsInNldERyYWdTdGF0ZSIsImhhbmRsZU1vdXNlTW92ZVdyYXBwZXIiLCJkcmFnU3RhdGUiLCJoYW5kbGVNb3VzZVVwV3JhcHBlciIsImhhbmRsZU1vdXNlTGVhdmVXcmFwcGVyIiwiY2xlYXJBbGxEYXRhIiwibm90aWZpY2F0aW9uIiwiZmV0Y2hDbGFzc1N0dWRlbnRzRGF0YSIsImlkIiwic3R1ZGVudHNEYXRhIiwibGVuZ3RoIiwicG9pbnRzTWFwIiwibWFwIiwicyIsInVzZXJJZCIsImVycm9yIiwiY29uc29sZSIsImlzQmxvY2tzT25seU1vZGUiLCJkaXN0cmlidXRpb25PcHRpb25zIiwibGFiZWwiLCJkZXNjcmlwdGlvbiIsImhhc0lucHV0IiwiaXNFbmVyZ3lPbmx5TW9kZSIsImhhbmRsZURpc3RyaWJ1dGlvblNlbGVjdCIsIm9wdGlvbklkIiwic2V0VGltZW91dCIsImN1cnJlbnQiLCJmb2N1cyIsImhhbmRsZVRlbXBsYXRlU2VsZWN0IiwidGVtcGxhdGUiLCJoYW5kbGVDYW5jZWxUZW1wbGF0ZSIsImhhbmRsZVRlbXBsYXRlUGlja2VyT3BlbiIsImhhbmRsZVRlbXBsYXRlUGlja2VyQ2xvc2UiLCJoYW5kbGVCYXRjaFVzZUtleVBhY2thZ2VNb2RhbE9wZW4iLCJoYW5kbGVCYXRjaFVzZUtleVBhY2thZ2VNb2RhbENsb3NlIiwiaGFuZGxlQmF0Y2hVc2VLZXlQYWNrYWdlU3VjY2VzcyIsInN1Y2Nlc3MiLCJnZXRDdXJyZW50RW5lcmd5QW1vdW50IiwiZ2V0TWluQXZhaWxhYmxlUG9pbnRzIiwic2VsZWN0ZWRTdHVkZW50UG9pbnRzIiwic3R1ZGVudElkIiwiZ2V0IiwiTWF0aCIsIm1pbiIsImdldEVuZXJneURpc3BsYXlJbmZvIiwidmFsdWUiLCJoYW5kbGVOZXh0IiwibG9nIiwiY3VycmVudEVuZXJneUFtb3VudCIsImVycm9yS2V5IiwiZW5lcmd5QW1vdW50TnVtIiwiTnVtYmVyIiwiaW5zdWZmaWNpZW50U3R1ZGVudHMiLCJmaWx0ZXIiLCJzdHVkZW50QXZhaWxhYmxlUG9pbnRzIiwiaW5zdWZmaWNpZW50TmFtZXMiLCJzdHVkZW50IiwiZmluZCIsImF2YWlsYWJsZVBvaW50cyIsIm5pY2tOYW1lIiwiam9pbiIsInN0dWRlbnRzTmVlZGluZ0VuZXJneSIsImN1cnJlbnRQb2ludHMiLCJuZWVkZWRQb2ludHMiLCJlbmVyZ3lBbW91bnQiLCJoYW5kbGVQcmV2aW91cyIsImhhbmRsZUNvbmZpcm0iLCJ0cmltIiwidGltZXMiLCJmaW5hbFRhc2tEYXRhIiwiaGFuZGxlUHVibGlzaFRhc2tPayIsInVzZXJEYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXIiLCJKU09OIiwicGFyc2UiLCJ0ZWFjaGVySWQiLCJzdGFydERhdGUiLCJEYXRlIiwiZW5kRGF0ZSIsIm5vdyIsInRhc2tQYXJhbXMiLCJ0YXNrVHlwZSIsIkdSQVBISUMiLCJwcmlvcml0eSIsIk5PUk1BTCIsInRhc2tDb250ZW50IiwiZmlsZSIsIm5hbWUiLCJpc1B1YmxpYyIsImFsbG93TGF0ZVN1Ym1pc3Npb24iLCJzdHVkZW50SWRzIiwiY2xhc3NJZCIsInRlbXBsYXRlSWQiLCJ3b3JrSWRzIiwiaXRlbSIsInJlcXVlc3RzIiwicHVzaCIsInB1Ymxpc2hUYXNrIiwiZW5lcmd5UmVxdWVzdCIsInRhcmdldEFtb3VudCIsImRlZmF1bHRFeHBpcmVUaW1lIiwidG9JU09TdHJpbmciLCJzdHVkZW50RXhwaXJpZXMiLCJmb3JFYWNoIiwiYmF0Y2hBc3NpZ25QZXJtaXNzaW9uIiwicmVtYXJrIiwiZW5lcmd5UmVxdWVzdHMiLCJyZXF1ZXN0IiwidGVtcGxhdGVSZXF1ZXN0IiwidXNlcnMiLCJyb2xlSWQiLCJvcmlnaW5hbFRlbXBsYXRlSWQiLCJyZXN1bHRzIiwiUHJvbWlzZSIsImFsbFNldHRsZWQiLCJ0YXNrUmVzdWx0Iiwic3RhdHVzIiwiZGF0YSIsImNvZGUiLCJlcnJvck1zZyIsIm1lc3NhZ2UiLCJyZXN1bHRJbmRleCIsImVuZXJneVJlc3VsdCIsIndhcm5pbmciLCJ0ZW1wbGF0ZVJlc3VsdCIsImRpdiIsImNsYXNzTmFtZSIsIm9uV2hlZWwiLCJzdG9wUHJvcGFnYXRpb24iLCJvblRvdWNoTW92ZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzaXplIiwiZGF0YS1zdGVwIiwiaDMiLCJvcHRpb24iLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJjdXJyZW50QW1vdW50IiwiaW5wdXQiLCJyZWYiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsInVwZGF0ZUtleSIsIm51bVZhbHVlIiwiaXNJbnRlZ2VyIiwib25Gb2N1cyIsIm9uQmx1ciIsImVycm9yTWVzc2FnZSIsImNvbG9yIiwiZm9udFNpemUiLCJtYXJnaW5Ub3AiLCJ0ZXh0QWxpZ24iLCJzcGFuIiwiZm9udFN0eWxlIiwiZGlzcGxheUluZm8iLCJzaG91bGRTaG93RXJyb3IiLCJtaW5BdmFpbGFibGUiLCJiYWNrZ3JvdW5kIiwiYm9yZGVyIiwiYm9yZGVyUmFkaXVzIiwicGFkZGluZyIsIm1hcmdpbkJvdHRvbSIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJnYXAiLCJmbGV4IiwiZm9udFdlaWdodCIsImN1cnNvciIsInRyYW5zaXRpb24iLCJmbGV4U2hyaW5rIiwiY3VycmVudFRhcmdldCIsInRlbXBsYXRlTmFtZSIsInRleHRhcmVhIiwicm93cyIsImluZGV4IiwibmV3SXRlbXMiLCJfIiwiaSIsInAiLCJoNCIsIm9uTW91c2VEb3duIiwib25Nb3VzZU1vdmUiLCJvbk1vdXNlVXAiLCJtaW5IZWlnaHQiLCJ1c2VyU2VsZWN0Iiwic29ydCIsImEiLCJiIiwiYVNlbGVjdGVkIiwiaW5jbHVkZXMiLCJiU2VsZWN0ZWQiLCJ3b3JrIiwiY292ZXJJbWFnZSIsInNjcmVlblNob3RJbWFnZSIsImltZyIsInNyYyIsImFsdCIsInRpdGxlIiwib25FcnJvciIsIm5leHRFbGVtZW50IiwibmV4dEVsZW1lbnRTaWJsaW5nIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInhtbG5zIiwicGF0aCIsImQiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlTGluZWNhcCIsIndvcmtOYW1lIiwiZGlzYWJsZWQiLCJtdWx0aXBsZSIsImFjY2VwdCIsImZpbGVzIiwiQXJyYXkiLCJmcm9tIiwidmFsaWRGaWxlcyIsImludmFsaWRGaWxlcyIsImFsbG93ZWRUeXBlcyIsImFsbG93ZWRFeHRlbnNpb25zIiwiZmlsZUV4dGVuc2lvbiIsInNwbGl0IiwicG9wIiwidG9Mb3dlckNhc2UiLCJpc1ZhbGlkVHlwZSIsImFsZXJ0IiwiaHRtbEZvciIsIm9uVGVtcGxhdGVTZWxlY3QiLCJvcGVuIiwic2VsZWN0ZWRTdHVkZW50SWRzIiwib25TdWNjZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\n"));

/***/ })

});