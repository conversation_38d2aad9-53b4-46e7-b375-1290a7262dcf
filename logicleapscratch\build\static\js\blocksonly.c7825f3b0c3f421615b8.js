window.addEventListener("error",(function(e){try{if(e.message.includes("ResizeObserver"))return e.stopImmediatePropagation(),!1}catch(e){console.log(e)}}),!0),function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.GUI=r():e.GUI=r()}(self,(()=>(()=>{var e,r,n,t,o,i={93558:(e,r,n)=>{"use strict";n.r(r);var t=n(96540),o=n(40961),i=n(61934),c=n(24935),a=n(69596),d=n(73125),s=n(41580),l=n(11640),u=n(3555);const f=e=>({vm:e.scratchGui.vm}),p=(0,i.connect)(f)(a.default),h=(0,i.connect)(f)(c.default),v=(0,l.default)((0,s.default)((e=>t.createElement(d.default,e,t.createElement(p,{grow:1,options:{media:"static/blocks-media/"}}),t.createElement(h,{className:u.default.controls}))))),m=document.createElement("div");document.body.appendChild(m),o.render(t.createElement(v,null),m)},21746:(e,r,n)=>{var t=n(54991),o=n(76314)(t);o.push([e.id,".blocks-only_controls_ICD60 {\n    position: absolute;\n    z-index: 2;\n    top: 10px;\n    right: 15px;\n}\n","",{version:3,sources:["webpack://./src/playground/blocks-only.css"],names:[],mappings:"AAAA;IACI,kBAAkB;IAClB,UAAU;IACV,SAAS;IACT,WAAW;AACf",sourcesContent:[".controls {\n    position: absolute;\n    z-index: 2;\n    top: 10px;\n    right: 15px;\n}\n"],sourceRoot:""}]),o.locals={controls:"blocks-only_controls_ICD60"},e.exports=o},3555:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>w});var t=n(85072),o=n.n(t),i=n(97825),c=n.n(i),a=n(77659),d=n.n(a),s=n(55056),l=n.n(s),u=n(10540),f=n.n(u),p=n(41113),h=n.n(p),v=n(21746),m=n.n(v),b={};for(const e in v)"default"!==e&&(b[e]=()=>v[e]);n.d(r,b);var y={};y.styleTagTransform=h(),y.setAttributes=l(),y.insert=d().bind(null,"head"),y.domAPI=c(),y.insertStyleElement=f();var g=o()(m(),y);if(!m().locals||e.hot.invalidate){var E=!m().locals,_=E?v:m().locals;e.hot.accept(21746,(r=>{v=n(21746),m=n.n(v),function(e,r,n){if(!e&&r||e&&!r)return!1;var t;for(t in e)if((!n||"default"!==t)&&e[t]!==r[t])return!1;for(t in r)if(!(n&&"default"===t||e[t]))return!1;return!0}(_,E?v:m().locals,E)?(_=E?v:m().locals,g(m())):e.hot.invalidate()}))}e.hot.dispose((function(){g()}));const w=m()&&m().locals?m().locals:void 0}},c={};function a(e){var r=c[e];if(void 0!==r)return r.exports;var n=c[e]={id:e,loaded:!1,exports:{}},t={id:e,module:n,factory:i[e],require:a};return a.i.forEach((function(e){e(t)})),n=t.module,t.factory.call(n.exports,n,n.exports,t.require),n.loaded=!0,n.exports}a.m=i,a.c=c,a.i=[],a.amdD=function(){throw new Error("define cannot be used indirect")},a.amdO={},e=[],a.O=(r,n,t,o)=>{if(!n){var i=1/0;for(l=0;l<e.length;l++){for(var[n,t,o]=e[l],c=!0,d=0;d<n.length;d++)(!1&o||i>=o)&&Object.keys(a.O).every((e=>a.O[e](n[d])))?n.splice(d--,1):(c=!1,o<i&&(i=o));if(c){e.splice(l--,1);var s=t();void 0!==s&&(r=s)}}return r}o=o||0;for(var l=e.length;l>0&&e[l-1][2]>o;l--)e[l]=e[l-1];e[l]=[n,t,o]},a.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return a.d(r,{a:r}),r},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var o=Object.create(null);a.r(o);var i={};r=r||[null,n({}),n([]),n(n)];for(var c=2&t&&e;"object"==typeof c&&!~r.indexOf(c);c=n(c))Object.getOwnPropertyNames(c).forEach((r=>i[r]=()=>e[r]));return i.default=()=>e,a.d(o,i),o},a.d=(e,r)=>{for(var n in r)a.o(r,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce(((r,n)=>(a.f[n](e,r),r)),[])),a.u=e=>"static/js/"+{29:"uk-steps",122:"ja-steps",149:"fr-steps",179:"tr-steps",365:"zh_TW-steps",367:"sw-steps",509:"es-steps",665:"zh_CN-steps",875:"am-steps",884:"ar-steps",904:"pt_BR-steps",950:"zu-steps"}[e]+"."+{29:"ad8cc0c1d099b36fb652",122:"c425b7c855b44887bd5a",149:"8dcb9cbc81509f82e9fb",179:"da27b180bbd46e0b39b0",365:"7a3b2aecb25816a500ef",367:"b52015c009cc4a3ad349",509:"e3a6b4f0b3a58302c68c",665:"2669dcf583e8f55a3632",875:"54abb2724ac1d567ad8a",884:"970da08a0bcd1f6a166f",904:"f7703429f8123efdb1b1",950:"38c123d1621f65d39f90"}[e]+".js",a.hu=e=>e+"."+a.h()+".hot-update.js",a.hmrF=()=>"blocksonly."+a.h()+".hot-update.json",a.h=()=>"619309e5f863873ae05b",a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),a.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t={},o="GUI:",a.l=(e,r,n,i)=>{if(t[e])t[e].push(r);else{var c,d;if(void 0!==n)for(var s=document.getElementsByTagName("script"),l=0;l<s.length;l++){var u=s[l];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==o+n){c=u;break}}c||(d=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,a.nc&&c.setAttribute("nonce",a.nc),c.setAttribute("data-webpack",o+n),c.src=e),t[e]=[r];var f=(r,n)=>{c.onerror=c.onload=null,clearTimeout(p);var o=t[e];if(delete t[e],c.parentNode&&c.parentNode.removeChild(c),o&&o.forEach((e=>e(n))),r)return r(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=f.bind(null,c.onerror),c.onload=f.bind(null,c.onload),d&&document.head.appendChild(c)}},a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e,r,n,t={},o=a.c,i=[],c=[],d="idle",s=0,l=[];function u(e){d=e;for(var r=[],n=0;n<c.length;n++)r[n]=c[n].call(null,e);return Promise.all(r).then((function(){}))}function f(){0==--s&&u("ready").then((function(){if(0===s){var e=l;l=[];for(var r=0;r<e.length;r++)e[r]()}}))}function p(e){if("idle"!==d)throw new Error("check() is only allowed in idle status");return u("check").then(a.hmrM).then((function(n){return n?u("prepare").then((function(){var t=[];return r=[],Promise.all(Object.keys(a.hmrC).reduce((function(e,o){return a.hmrC[o](n.c,n.r,n.m,e,r,t),e}),[])).then((function(){return r=function(){return e?v(e):u("ready").then((function(){return t}))},0===s?r():new Promise((function(e){l.push((function(){e(r())}))}));var r}))})):u(m()?"ready":"idle").then((function(){return null}))}))}function h(e){return"ready"!==d?Promise.resolve().then((function(){throw new Error("apply() is only allowed in ready status (state: "+d+")")})):v(e)}function v(e){e=e||{},m();var t=r.map((function(r){return r(e)}));r=void 0;var o=t.map((function(e){return e.error})).filter(Boolean);if(o.length>0)return u("abort").then((function(){throw o[0]}));var i=u("dispose");t.forEach((function(e){e.dispose&&e.dispose()}));var c,a=u("apply"),d=function(e){c||(c=e)},s=[];return t.forEach((function(e){if(e.apply){var r=e.apply(d);if(r)for(var n=0;n<r.length;n++)s.push(r[n])}})),Promise.all([i,a]).then((function(){return c?u("fail").then((function(){throw c})):n?v(e).then((function(e){return s.forEach((function(r){e.indexOf(r)<0&&e.push(r)})),e})):u("idle").then((function(){return s}))}))}function m(){if(n)return r||(r=[]),Object.keys(a.hmrI).forEach((function(e){n.forEach((function(n){a.hmrI[e](n,r)}))})),n=void 0,!0}a.hmrD=t,a.i.push((function(l){var v,m,b,y,g=l.module,E=function(r,n){var t=o[n];if(!t)return r;var c=function(c){if(t.hot.active){if(o[c]){var a=o[c].parents;-1===a.indexOf(n)&&a.push(n)}else i=[n],e=c;-1===t.children.indexOf(c)&&t.children.push(c)}else console.warn("[HMR] unexpected require("+c+") from disposed module "+n),i=[];return r(c)},a=function(e){return{configurable:!0,enumerable:!0,get:function(){return r[e]},set:function(n){r[e]=n}}};for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&"e"!==l&&Object.defineProperty(c,l,a(l));return c.e=function(e,n){return function(e){switch(d){case"ready":u("prepare");case"prepare":return s++,e.then(f,f),e;default:return e}}(r.e(e,n))},c}(l.require,l.id);g.hot=(v=l.id,m=g,y={_acceptedDependencies:{},_acceptedErrorHandlers:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_selfInvalidated:!1,_disposeHandlers:[],_main:b=e!==v,_requireSelf:function(){i=m.parents.slice(),e=b?void 0:v,a(v)},active:!0,accept:function(e,r,n){if(void 0===e)y._selfAccepted=!0;else if("function"==typeof e)y._selfAccepted=e;else if("object"==typeof e&&null!==e)for(var t=0;t<e.length;t++)y._acceptedDependencies[e[t]]=r||function(){},y._acceptedErrorHandlers[e[t]]=n;else y._acceptedDependencies[e]=r||function(){},y._acceptedErrorHandlers[e]=n},decline:function(e){if(void 0===e)y._selfDeclined=!0;else if("object"==typeof e&&null!==e)for(var r=0;r<e.length;r++)y._declinedDependencies[e[r]]=!0;else y._declinedDependencies[e]=!0},dispose:function(e){y._disposeHandlers.push(e)},addDisposeHandler:function(e){y._disposeHandlers.push(e)},removeDisposeHandler:function(e){var r=y._disposeHandlers.indexOf(e);r>=0&&y._disposeHandlers.splice(r,1)},invalidate:function(){switch(this._selfInvalidated=!0,d){case"idle":r=[],Object.keys(a.hmrI).forEach((function(e){a.hmrI[e](v,r)})),u("ready");break;case"ready":Object.keys(a.hmrI).forEach((function(e){a.hmrI[e](v,r)}));break;case"prepare":case"check":case"dispose":case"apply":(n=n||[]).push(v)}},check:p,apply:h,status:function(e){if(!e)return d;c.push(e)},addStatusHandler:function(e){c.push(e)},removeStatusHandler:function(e){var r=c.indexOf(e);r>=0&&c.splice(r,1)},data:t[v]},e=void 0,y),g.parents=i,g.children=[],i=[],l.require=E})),a.hmrC={},a.hmrI={}})(),a.p="/logicleap/",(()=>{var e,r=a.hmrS_jsonp=a.hmrS_jsonp||{775:0};a.f.j=(e,n)=>{var t=a.o(r,e)?r[e]:void 0;if(0!==t)if(t)n.push(t[2]);else{var o=new Promise(((n,o)=>t=r[e]=[n,o]));n.push(t[2]=o);var i=a.p+a.u(e),c=new Error;a.l(i,(n=>{if(a.o(r,e)&&(0!==(t=r[e])&&(r[e]=void 0),t)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;c.message="Loading chunk "+e+" failed.\n("+o+": "+i+")",c.name="ChunkLoadError",c.type=o,c.request=i,t[1](c)}}),"chunk-"+e,e)}};var n,t,o,i,c={};function d(r,n){return e=n,new Promise(((e,n)=>{c[r]=e;var t=a.p+a.hu(r),o=new Error;a.l(t,(e=>{if(c[r]){c[r]=void 0;var t=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;o.message="Loading hot update chunk "+r+" failed.\n("+t+": "+i+")",o.name="ChunkLoadError",o.type=t,o.request=i,n(o)}}))}))}function s(e){function c(e){for(var r=[e],n={},t=r.map((function(e){return{chain:[e],id:e}}));t.length>0;){var o=t.pop(),i=o.id,c=o.chain,s=a.c[i];if(s&&(!s.hot._selfAccepted||s.hot._selfInvalidated)){if(s.hot._selfDeclined)return{type:"self-declined",chain:c,moduleId:i};if(s.hot._main)return{type:"unaccepted",chain:c,moduleId:i};for(var l=0;l<s.parents.length;l++){var u=s.parents[l],f=a.c[u];if(f){if(f.hot._declinedDependencies[i])return{type:"declined",chain:c.concat([u]),moduleId:i,parentId:u};-1===r.indexOf(u)&&(f.hot._acceptedDependencies[i]?(n[u]||(n[u]=[]),d(n[u],[i])):(delete n[u],r.push(u),t.push({chain:c.concat([u]),id:u})))}}}}return{type:"accepted",moduleId:e,outdatedModules:r,outdatedDependencies:n}}function d(e,r){for(var n=0;n<r.length;n++){var t=r[n];-1===e.indexOf(t)&&e.push(t)}}a.f&&delete a.f.jsonpHmr,n=void 0;var s={},l=[],u={},f=function(e){console.warn("[HMR] unexpected require("+e.id+") to disposed module")};for(var p in t)if(a.o(t,p)){var h=t[p],v=h?c(p):{type:"disposed",moduleId:p},m=!1,b=!1,y=!1,g="";switch(v.chain&&(g="\nUpdate propagation: "+v.chain.join(" -> ")),v.type){case"self-declined":e.onDeclined&&e.onDeclined(v),e.ignoreDeclined||(m=new Error("Aborted because of self decline: "+v.moduleId+g));break;case"declined":e.onDeclined&&e.onDeclined(v),e.ignoreDeclined||(m=new Error("Aborted because of declined dependency: "+v.moduleId+" in "+v.parentId+g));break;case"unaccepted":e.onUnaccepted&&e.onUnaccepted(v),e.ignoreUnaccepted||(m=new Error("Aborted because "+p+" is not accepted"+g));break;case"accepted":e.onAccepted&&e.onAccepted(v),b=!0;break;case"disposed":e.onDisposed&&e.onDisposed(v),y=!0;break;default:throw new Error("Unexception type "+v.type)}if(m)return{error:m};if(b)for(p in u[p]=h,d(l,v.outdatedModules),v.outdatedDependencies)a.o(v.outdatedDependencies,p)&&(s[p]||(s[p]=[]),d(s[p],v.outdatedDependencies[p]));y&&(d(l,[v.moduleId]),u[p]=f)}t=void 0;for(var E,_=[],w=0;w<l.length;w++){var I=l[w],O=a.c[I];O&&(O.hot._selfAccepted||O.hot._main)&&u[I]!==f&&!O.hot._selfInvalidated&&_.push({module:I,require:O.hot._requireSelf,errorHandler:O.hot._selfAccepted})}return{dispose:function(){var e;o.forEach((function(e){delete r[e]})),o=void 0;for(var n,t=l.slice();t.length>0;){var i=t.pop(),c=a.c[i];if(c){var d={},u=c.hot._disposeHandlers;for(w=0;w<u.length;w++)u[w].call(null,d);for(a.hmrD[i]=d,c.hot.active=!1,delete a.c[i],delete s[i],w=0;w<c.children.length;w++){var f=a.c[c.children[w]];f&&((e=f.parents.indexOf(i))>=0&&f.parents.splice(e,1))}}}for(var p in s)if(a.o(s,p)&&(c=a.c[p]))for(E=s[p],w=0;w<E.length;w++)n=E[w],(e=c.children.indexOf(n))>=0&&c.children.splice(e,1)},apply:function(r){for(var n in u)a.o(u,n)&&(a.m[n]=u[n]);for(var t=0;t<i.length;t++)i[t](a);for(var o in s)if(a.o(s,o)){var c=a.c[o];if(c){E=s[o];for(var d=[],f=[],p=[],h=0;h<E.length;h++){var v=E[h],m=c.hot._acceptedDependencies[v],b=c.hot._acceptedErrorHandlers[v];if(m){if(-1!==d.indexOf(m))continue;d.push(m),f.push(b),p.push(v)}}for(var y=0;y<d.length;y++)try{d[y].call(null,E)}catch(n){if("function"==typeof f[y])try{f[y](n,{moduleId:o,dependencyId:p[y]})}catch(t){e.onErrored&&e.onErrored({type:"accept-error-handler-errored",moduleId:o,dependencyId:p[y],error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"accept-errored",moduleId:o,dependencyId:p[y],error:n}),e.ignoreErrored||r(n)}}}for(var g=0;g<_.length;g++){var w=_[g],I=w.module;try{w.require(I)}catch(n){if("function"==typeof w.errorHandler)try{w.errorHandler(n,{moduleId:I,module:a.c[I]})}catch(t){e.onErrored&&e.onErrored({type:"self-accept-error-handler-errored",moduleId:I,error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"self-accept-errored",moduleId:I,error:n}),e.ignoreErrored||r(n)}}return l}}}self.webpackHotUpdateGUI=(r,n,o)=>{for(var d in n)a.o(n,d)&&(t[d]=n[d],e&&e.push(d));o&&i.push(o),c[r]&&(c[r](),c[r]=void 0)},a.hmrI.jsonp=function(e,r){t||(t={},i=[],o=[],r.push(s)),a.o(t,e)||(t[e]=a.m[e])},a.hmrC.jsonp=function(e,c,l,u,f,p){f.push(s),n={},o=c,t=l.reduce((function(e,r){return e[r]=!1,e}),{}),i=[],e.forEach((function(e){a.o(r,e)&&void 0!==r[e]?(u.push(d(e,p)),n[e]=!0):n[e]=!1})),a.f&&(a.f.jsonpHmr=function(e,r){n&&a.o(n,e)&&!n[e]&&(r.push(d(e)),n[e]=!0)})},a.hmrM=()=>{if("undefined"==typeof fetch)throw new Error("No browser support: need fetch API");return fetch(a.p+a.hmrF()).then((e=>{if(404!==e.status){if(!e.ok)throw new Error("Failed to fetch update manifest "+e.statusText);return e.json()}}))},a.O.j=e=>0===r[e];var l=(e,n)=>{var t,o,[i,c,d]=n,s=0;if(i.some((e=>0!==r[e]))){for(t in c)a.o(c,t)&&(a.m[t]=c[t]);if(d)var l=d(a)}for(e&&e(n);s<i.length;s++)o=i[s],a.o(r,o)&&r[o]&&r[o][0](),r[o]=0;return a.O(l)},u=self.webpackChunkGUI=self.webpackChunkGUI||[];u.forEach(l.bind(null,0)),u.push=l.bind(null,u.push.bind(u))})(),a.nc=void 0,a.O(void 0,[340,644,96,223],(()=>a(42440))),a.O(void 0,[340,644,96,223],(()=>a(63943)));var d=a.O(void 0,[340,644,96,223],(()=>a(93558)));return d=a.O(d)})()));