"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseTeachingRecord = exports.TeachingStatusUtils = exports.TemplateAppliedStatus = exports.TeachingStatus = void 0;
const typeorm_1 = require("typeorm");
const course_entity_1 = require("./course.entity");
var TeachingStatus;
(function (TeachingStatus) {
    TeachingStatus[TeachingStatus["IN_PROGRESS"] = 0] = "IN_PROGRESS";
    TeachingStatus[TeachingStatus["SUCCESS"] = 1] = "SUCCESS";
    TeachingStatus[TeachingStatus["FAILED"] = 2] = "FAILED";
    TeachingStatus[TeachingStatus["PARTIAL_SUCCESS"] = 3] = "PARTIAL_SUCCESS";
})(TeachingStatus || (exports.TeachingStatus = TeachingStatus = {}));
var TemplateAppliedStatus;
(function (TemplateAppliedStatus) {
    TemplateAppliedStatus[TemplateAppliedStatus["NO"] = 0] = "NO";
    TemplateAppliedStatus[TemplateAppliedStatus["YES"] = 1] = "YES";
})(TemplateAppliedStatus || (exports.TemplateAppliedStatus = TemplateAppliedStatus = {}));
class TeachingStatusUtils {
    static getStatusLabel(status) {
        const statusMap = {
            [TeachingStatus.IN_PROGRESS]: '进行中',
            [TeachingStatus.SUCCESS]: '成功',
            [TeachingStatus.FAILED]: '失败'
        };
        return statusMap[status] || '未知';
    }
    static getTemplateAppliedLabel(applied) {
        const appliedMap = {
            [TemplateAppliedStatus.NO]: '否',
            [TemplateAppliedStatus.YES]: '是'
        };
        return appliedMap[applied] || '未知';
    }
    static isSuccess(status) {
        return status === TeachingStatus.SUCCESS;
    }
    static isFailed(status) {
        return status === TeachingStatus.FAILED;
    }
    static isInProgress(status) {
        return status === TeachingStatus.IN_PROGRESS;
    }
}
exports.TeachingStatusUtils = TeachingStatusUtils;
let CourseTeachingRecord = class CourseTeachingRecord {
    id;
    courseId;
    classId;
    teacherId;
    status;
    pointsAllocated;
    tasksCreated;
    templateApplied;
    errorMessage;
    executionDetails;
    lockAcquireTime;
    totalExecutionTime;
    createdDate;
    createdAt;
    updatedAt;
    course;
};
exports.CourseTeachingRecord = CourseTeachingRecord;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '记录ID' }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_course'),
    (0, typeorm_1.Index)('idx_course_class_teacher_date'),
    (0, typeorm_1.Column)({ type: 'int', name: 'course_id', comment: '课程ID' }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "courseId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_class'),
    (0, typeorm_1.Index)('idx_course_class_teacher_date'),
    (0, typeorm_1.Column)({ type: 'int', name: 'class_id', comment: '班级ID' }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "classId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_teacher'),
    (0, typeorm_1.Index)('idx_teacher_status_time'),
    (0, typeorm_1.Index)('idx_course_class_teacher_date'),
    (0, typeorm_1.Column)({ type: 'int', name: 'teacher_id', comment: '教师ID' }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "teacherId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Index)('idx_teacher_status_time'),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: TeachingStatus.IN_PROGRESS,
        comment: '状态：0=进行中，1=成功，2=失败'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'points_allocated',
        default: 0,
        comment: '分配的积分总数'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "pointsAllocated", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'tasks_created',
        default: 0,
        comment: '创建的任务数量'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "tasksCreated", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'tinyint',
        name: 'template_applied',
        default: TemplateAppliedStatus.NO,
        comment: '是否应用了模板：0=否，1=是'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "templateApplied", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        name: 'error_message',
        nullable: true,
        comment: '错误信息'
    }),
    __metadata("design:type", String)
], CourseTeachingRecord.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'json',
        name: 'execution_details',
        nullable: true,
        comment: '执行详情'
    }),
    __metadata("design:type", Object)
], CourseTeachingRecord.prototype, "executionDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'lock_acquire_time',
        default: 0,
        comment: '获取锁耗时（毫秒）'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "lockAcquireTime", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_execution_time'),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'total_execution_time',
        default: 0,
        comment: '总执行耗时（毫秒）'
    }),
    __metadata("design:type", Number)
], CourseTeachingRecord.prototype, "totalExecutionTime", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_course_class_teacher_date'),
    (0, typeorm_1.Column)({
        type: 'date',
        name: 'created_date',
        comment: '创建日期（用于分区）',
        generatedType: 'VIRTUAL',
        asExpression: 'DATE(`created_at`)',
        select: false
    }),
    __metadata("design:type", Date)
], CourseTeachingRecord.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_created_at'),
    (0, typeorm_1.Index)('idx_teacher_status_time'),
    (0, typeorm_1.CreateDateColumn)({
        name: 'created_at',
        comment: '创建时间'
    }),
    __metadata("design:type", Date)
], CourseTeachingRecord.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        name: 'updated_at',
        comment: '更新时间'
    }),
    __metadata("design:type", Date)
], CourseTeachingRecord.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_entity_1.Course, course => course.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'course_id' }),
    __metadata("design:type", course_entity_1.Course)
], CourseTeachingRecord.prototype, "course", void 0);
exports.CourseTeachingRecord = CourseTeachingRecord = __decorate([
    (0, typeorm_1.Entity)('course_teaching_records')
], CourseTeachingRecord);
//# sourceMappingURL=course-teaching-record.entity.js.map