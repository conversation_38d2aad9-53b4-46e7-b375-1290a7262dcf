"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassTasks.tsx":
/*!*************************************************!*\
  !*** ./app/workbench/components/ClassTasks.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/delete.js\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ClassTasks.module.css */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.module.css\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.tsx\");\n/* harmony import */ var _teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../teacher-space/components/task-detail-modal */ \"(app-pages-browser)/./app/teacher-space/components/task-detail-modal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../teacher-space/components/modals/edit-task-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/edit-task-modal.tsx\");\n/* harmony import */ var _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/utils/task-event-manager */ \"(app-pages-browser)/./app/utils/task-event-manager.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* harmony import */ var _lib_utils_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/user */ \"(app-pages-browser)/./lib/utils/user.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ClassTasks = ()=>{\n    _s();\n    // 移除对模板上下文的依赖，只使用全局事件\n    // const { notifyTaskPublished } = useTemplate();\n    // 获取状态对应的CSS类名\n    const getStatusClassName = (status)=>{\n        switch(status){\n            case \"已完成\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().completed);\n            case \"进行中\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().inProgress);\n            case \"已结束\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().ended);\n            case \"未开始\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().notStarted);\n            default:\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default()[\"default\"]);\n        }\n    };\n    // 数据状态\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 发布任务多步骤弹窗状态\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishTaskModalOpen, setIsPublishTaskModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishModalData, setPublishModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"\",\n        energyAmount: \"\",\n        selectedTemplate: {\n            id: 0\n        },\n        selectedStudents: []\n    });\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务详情弹窗状态\n    const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForDetail, setSelectedTaskForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务编辑弹窗状态\n    const [isEditTaskModalVisible, setIsEditTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForEdit, setSelectedTaskForEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 选择状态\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allSchools, setAllSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有学校数据\n    const [allClasses, setAllClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有班级数据\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"全部\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFilterExpanded, setIsFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSchoolFilterExpanded, setIsSchoolFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClassFilterExpanded, setIsClassFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isStatusFilterExpanded, setIsStatusFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSidebarCollapsed, setIsSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 监听屏幕尺寸变化，大屏幕时自动展开筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth >= 1024) {\n                setIsFilterExpanded(true);\n            }\n        };\n        handleResize(); // 初始化时检查\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    const statusOptions = [\n        \"全部\",\n        \"已完成\",\n        \"进行中\",\n        \"未开始\",\n        \"已结束\"\n    ];\n    // 获取用户关联的学校列表\n    const loadUserSchools = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n            setSchools(schoolsData);\n            setAllSchools(schoolsData); // 保存所有学校数据\n            // 默认不选择任何学校，显示\"全部\"状态\n            setSelectedSchool(null);\n            setSelectedClass(null);\n            // 加载所有学校的所有班级\n            await loadAllClasses(schoolsData);\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setError(error.message || \"网络连接失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载所有学校的所有班级\n    const loadAllClasses = async (schoolsData)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setAllClasses([]);\n                return;\n            }\n            let allClassesData = [];\n            // 遍历所有学校，获取每个学校的班级\n            for (const school of schoolsData){\n                try {\n                    const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(school.id, teacherId);\n                    allClassesData = [\n                        ...allClassesData,\n                        ...classesData\n                    ];\n                } catch (error) {\n                    console.error(\"获取学校 \".concat(school.schoolName, \" 的班级失败:\"), error);\n                }\n            }\n            setClasses(allClassesData);\n            setAllClasses(allClassesData);\n            console.log(\"加载所有班级完成，总数:\", allClassesData.length);\n        } catch (error) {\n            console.error(\"加载所有班级失败:\", error);\n            setClasses([]);\n            setAllClasses([]);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用教师班级API\n    const loadSchoolClasses = async (schoolId)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setSelectedClass(null);\n                return;\n            }\n            const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(schoolId, teacherId);\n            setClasses(classesData);\n            // 默认选择第一个班级\n            if (classesData.length > 0) {\n                setSelectedClass(classesData[0]);\n                console.log(\"默认选择班级:\", classesData[0].className, \"(ID:\", classesData[0].id, \")\");\n            } else {\n                setSelectedClass(null);\n                setStudents([]);\n                console.log(\"没有班级数据，清空选择\");\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            setClasses([]);\n            setSelectedClass(null);\n        }\n    };\n    // 处理班级选择变化\n    const handleClassChange = (classItem)=>{\n        console.log(\"=== 切换班级 ===\");\n        console.log(\"从班级:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className, \"(ID:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id, \")\");\n        console.log(\"切换到班级:\", (classItem === null || classItem === void 0 ? void 0 : classItem.className) || \"全部\", \"(ID:\", (classItem === null || classItem === void 0 ? void 0 : classItem.id) || \"all\", \")\");\n        // 如果点击的是当前已选中的班级，不需要切换\n        if ((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === (classItem === null || classItem === void 0 ? void 0 : classItem.id)) {\n            console.log(\"点击的是当前班级，无需切换\");\n            return;\n        }\n        // 更新选中的班级\n        setSelectedClass(classItem);\n        console.log(\"班级切换完成\");\n    };\n    // 处理学校选择变化\n    const handleSchoolChange = (school)=>{\n        console.log(\"=== 切换学校 ===\");\n        console.log(\"从学校:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName, \"(ID:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id, \")\");\n        console.log(\"切换到学校:\", (school === null || school === void 0 ? void 0 : school.schoolName) || \"全部\", \"(ID:\", (school === null || school === void 0 ? void 0 : school.id) || \"all\", \")\");\n        setSelectedClass(null);\n        setSelectedSchool(school);\n        if (school === null) {\n            // 选择\"全部学校\"，显示所有班级\n            setClasses(allClasses);\n            console.log(\"选择全部学校，显示所有班级:\", allClasses.length);\n        } else {\n            // 选择特定学校，只显示该学校的班级\n            console.log(\"选择特定学校，开始获取该学校的班级...\");\n            loadSchoolClasses(school.id);\n        }\n    };\n    // 获取班级的真实任务数据 - 使用工具函数\n    const loadClassTasks = async (classId)=>{\n        try {\n            return await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchClassTasks)(classId);\n        } catch (error) {\n            console.error(\"获取班级任务数据失败:\", error);\n            return [];\n        }\n    };\n    // 组件挂载时获取数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserSchools();\n    }, []);\n    // 监听selectedClass变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"selectedClass状态变化:\", selectedClass);\n    }, [\n        selectedClass\n    ]);\n    // 当选择的班级改变时，加载任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass) {\n            console.log(\"开始加载班级任务数据:\", selectedClass.className);\n            loadTasksData();\n        } else if (selectedClass === null && classes.length > 0) {\n            console.log(\"选择全部班级，加载所有班级任务数据\");\n            loadAllTasksData();\n        } else {\n            console.log(\"没有班级数据，清空任务数据\");\n            setTasks([]);\n        }\n    }, [\n        selectedClass\n    ]);\n    // 当班级列表变化且没有选中特定班级时，重新加载全部任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass === null && classes.length > 0) {\n            console.log(\"班级列表更新，重新加载全部班级任务数据\");\n            loadAllTasksData();\n        }\n    }, [\n        classes.length\n    ]); // 只监听班级数量变化，避免频繁重新渲染\n    // 获取任务数据（使用真实API）\n    const loadTasksData = async ()=>{\n        if (!selectedClass) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            const tasksData = await loadClassTasks(selectedClass.id);\n            setTasks(tasksData);\n        } catch (error) {\n            console.error(\"加载任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取所有班级的任务数据\n    const loadAllTasksData = async ()=>{\n        if (!classes || classes.length === 0) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            let allTasksData = [];\n            // 遍历所有班级，获取每个班级的任务\n            for (const classItem of classes){\n                try {\n                    const tasksData = await loadClassTasks(classItem.id);\n                    allTasksData = [\n                        ...allTasksData,\n                        ...tasksData\n                    ];\n                } catch (error) {\n                    console.error(\"获取班级 \".concat(classItem.className, \" 的任务失败:\"), error);\n                }\n            }\n            setTasks(allTasksData);\n            console.log(\"加载所有任务完成，总数:\", allTasksData.length);\n        } catch (error) {\n            console.error(\"加载所有任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 计算剩余时间\n    const calculateRemainingTime = (task)=>{\n        // 如果任务已完成或已结束，返回 \"-\"\n        if (task.status === \"已完成\" || task.status === \"已结束\") {\n            return \"-\";\n        }\n        // 解析截止时间\n        const deadline = new Date(task.deadline);\n        const now = new Date();\n        // 如果截止时间已过，返回 \"已超时\"\n        if (deadline < now) {\n            return \"已超时\";\n        }\n        // 计算剩余时间（毫秒）\n        const diffMs = deadline.getTime() - now.getTime();\n        // 计算剩余天数（不向上取整，精确计算）\n        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n        if (diffDays >= 1) {\n            // 剩余1天或以上，显示天数\n            return \"\".concat(diffDays, \"天\");\n        } else {\n            // 不足1天，显示小时数\n            const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));\n            if (diffHours <= 0) {\n                return \"已超时\";\n            }\n            return \"\".concat(diffHours, \"小时\");\n        }\n    };\n    // 获取剩余时间的样式类\n    const getRemainingTimeClass = (task)=>{\n        const remainingTime = calculateRemainingTime(task);\n        if (remainingTime === \"-\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished);\n        } else if (remainingTime === \"已超时\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue);\n        } else if (remainingTime.includes(\"小时\") || remainingTime === \"1天\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent);\n        } else {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal);\n        }\n    };\n    // 根据筛选条件过滤任务数据\n    const getFilteredTasks = ()=>{\n        const filters = {\n            statusFilter,\n            startDate,\n            endDate,\n            searchQuery\n        };\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_10__.filterTasks)(tasks, filters);\n    };\n    // 处理日期输入框点击事件\n    const handleDateInputClick = (event)=>{\n        var _input_showPicker;\n        const input = event.currentTarget;\n        (_input_showPicker = input.showPicker) === null || _input_showPicker === void 0 ? void 0 : _input_showPicker.call(input);\n    };\n    // 处理开始日期变化\n    const handleStartDateChange = (event)=>{\n        const newStartDate = event.target.value;\n        setStartDate(newStartDate);\n        // 如果开始日期晚于结束日期，清空结束日期\n        if (endDate && newStartDate > endDate) {\n            setEndDate(\"\");\n        }\n    };\n    // 处理结束日期变化\n    const handleEndDateChange = (event)=>{\n        const newEndDate = event.target.value;\n        // 如果结束日期早于开始日期，不允许设置\n        if (startDate && newEndDate < startDate) {\n            alert(\"结束日期不能早于开始日期\");\n            return;\n        }\n        setEndDate(newEndDate);\n    };\n    // 处理刷新列表\n    const handleRefreshList = async ()=>{\n        try {\n            setRefreshing(true);\n            // 如果没有选择学校，重新获取学校数据\n            if (!selectedSchool) {\n                console.log(\"没有选择学校，重新获取学校数据...\");\n                await loadUserSchools();\n                return;\n            }\n            // 如果没有选择班级，重新获取班级数据\n            if (!selectedClass) {\n                console.log(\"没有选择班级，重新获取班级数据...\");\n                await loadSchoolClasses(selectedSchool.id);\n                return;\n            }\n            // 重新获取当前班级的任务数据\n            console.log(\"重新获取班级任务数据...\");\n            await loadTasksData();\n            // 显示刷新成功的提示\n            setTimeout(()=>{\n                getFilteredTasks();\n            }, 100);\n        } catch (error) {\n            console.error(\"❌ 刷新任务列表失败:\", error);\n        // 这里可以添加错误提示给用户\n        } finally{\n            setRefreshing(false);\n        }\n    };\n    // 重置筛选条件\n    const handleResetFilters = ()=>{\n        setStartDate(\"\");\n        setEndDate(\"\");\n        setStatusFilter(\"全部\");\n        setSearchQuery(\"\");\n        console.log(\"已重置所有筛选条件\");\n    };\n    // 处理发布任务按钮点击 - 启动多步骤流程\n    const handlePublishTaskClick = async ()=>{\n        // 如果已经选择了学校和班级，直接跳到模板选择步骤\n        if (selectedSchool && selectedClass) {\n            setModalSelectedSchool(selectedSchool);\n            setIsTemplateModalOpen(true);\n        } else {\n            try {\n                // 获取用户的学校列表\n                const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            } catch (error) {\n                console.error(\"获取学校列表失败:\", error);\n                // 出错时仍然显示学校选择弹窗\n                setIsSchoolModalOpen(true);\n            }\n        }\n    };\n    // 处理学校选择完成\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    // 处理班级选择完成\n    const handleClassSelect = ()=>{\n        setIsClassModalOpen(false);\n        // 发布任务直接跳过模板选择，进入发布任务弹窗\n        // 设置默认的模态框数据\n        setPublishModalData({\n            selectedDistribution: \"\",\n            energyAmount: \"\",\n            selectedTemplate: {\n                id: 0\n            },\n            selectedStudents: []\n        });\n        setIsPublishTaskModalOpen(true);\n    };\n    // 处理模板选择完成\n    const handleTemplateSelect = (modalData)=>{\n        setPublishModalData(modalData);\n        setIsTemplateModalOpen(false);\n        setIsPublishTaskModalOpen(true);\n    };\n    // 处理最终发布任务确认 - 使用工具函数重构\n    const handlePublishTaskConfirm = async (taskData)=>{\n        try {\n            var _publishModalData_selectedTemplate;\n            console.log(\"发布任务数据:\", taskData);\n            console.log(\"模板数据:\", publishModalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const userId = userData ? JSON.parse(userData).userId : 0;\n            // 准备发布参数\n            const values = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskContent: taskData.taskDescription || \"\",\n                startDate: new Date(),\n                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                selfAssessmentItems: taskData.selfAssessmentItems || [],\n                allowLateSubmission: true,\n                templateId: ((_publishModalData_selectedTemplate = publishModalData.selectedTemplate) === null || _publishModalData_selectedTemplate === void 0 ? void 0 : _publishModalData_selectedTemplate.id) || 0,\n                lateSubmissionPolicy: {\n                    deductionPerDay: 5,\n                    maxDeductionDays: 7,\n                    minScore: 0\n                }\n            };\n            const selectedStudentIds = publishModalData.selectedStudents.length > 0 ? publishModalData.selectedStudents : students.map((s)=>s.id);\n            // 调用工具函数发布任务\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.publishTask)(values, selectedClass, userId, selectedStudentIds, students, [] // 附件列表\n            );\n            if (success) {\n                // 关闭所有弹窗并重置状态\n                setIsPublishTaskModalOpen(false);\n                setPublishModalData({\n                    selectedDistribution: \"\",\n                    energyAmount: \"\",\n                    selectedTemplate: {\n                        id: 0\n                    },\n                    selectedStudents: []\n                });\n                // 刷新任务列表\n                await loadTasksData();\n                // 触发全局任务发布事件\n                const eventData = {\n                    taskId: 0,\n                    taskName: taskData.taskName,\n                    classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                    className: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className,\n                    teacherId: userId\n                };\n                _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__[\"default\"].emit(_app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__.TASK_EVENTS.TASK_PUBLISHED, eventData);\n            }\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].error({\n                message: \"任务发布失败\",\n                description: \"网络错误或服务器异常，请稍后重试\"\n            });\n        }\n    };\n    // 处理任务查看\n    const handleTaskView = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleViewTask)(task);\n            setSelectedTaskForDetail(taskDetail);\n            setIsTaskDetailModalVisible(true);\n        } catch (error) {\n            console.error(\"查看任务失败:\", error);\n            setSelectedTaskForDetail(task);\n            setIsTaskDetailModalVisible(true);\n        }\n    };\n    // 处理任务编辑\n    const handleTaskEdit = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleEditTask)(task);\n            setSelectedTaskForEdit(taskDetail);\n            setIsEditTaskModalVisible(true);\n        } catch (error) {\n            console.error(\"编辑任务失败:\", error);\n            setSelectedTaskForEdit(task);\n            setIsEditTaskModalVisible(true);\n        }\n    };\n    // 处理任务删除\n    const handleTaskDelete = async (task)=>{\n        const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleDeleteTask)(task, loadTasksData);\n        if (success) {\n            console.log(\"任务删除成功\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().classTasksContainer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainLayout),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().leftSidebar), \" \").concat(isSidebarCollapsed ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed) : \"\"),\n                    children: isSidebarCollapsed ? /* 收缩状态：显示白色长方形条 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsedSidebar),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expandBtn),\n                            onClick: ()=>setIsSidebarCollapsed(false),\n                            title: \"展开筛选\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarTitle),\n                                        children: \"筛选\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                        onClick: ()=>setIsSidebarCollapsed(true),\n                                        title: \"收缩筛选\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterContent), \" \").concat(isFilterExpanded ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expanded) : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedSchool ? selectedSchool.schoolName : \"全部学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsSchoolFilterExpanded(!isSchoolFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isSchoolFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"加载中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ff4d4f\"\n                                                    },\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 21\n                                                }, undefined) : !Array.isArray(schools) || schools.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"暂无数据\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedSchool === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                            onClick: ()=>handleSchoolChange(null),\n                                                            title: \"全部学校\",\n                                                            children: \"全部学校\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>handleSchoolChange(school),\n                                                                title: school.schoolName,\n                                                                children: school.schoolName\n                                                            }, school.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedClass ? selectedClass.className : \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsClassFilterExpanded(!isClassFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isClassFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: [\n                                                    (()=>{\n                                                        return null;\n                                                    })(),\n                                                    !Array.isArray(classes) || classes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"暂无班级数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedClass === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>{\n                                                                    console.log(\"全部班级按钮被点击\");\n                                                                    handleClassChange(null);\n                                                                },\n                                                                title: \"全部班级\",\n                                                                children: \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === classItem.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                    onClick: ()=>{\n                                                                        console.log(\"班级按钮被点击:\", classItem.className);\n                                                                        handleClassChange(classItem);\n                                                                    },\n                                                                    title: classItem.className,\n                                                                    children: classItem.className\n                                                                }, classItem.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"所有类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: statusFilter\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsStatusFilterExpanded(!isStatusFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isStatusFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(statusFilter === status ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                        onClick: ()=>setStatusFilter(status),\n                                                        title: status,\n                                                        children: status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateRange),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: startDate,\n                                                                onChange: handleStartDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: \"\",\n                                                                title: \"开始日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !startDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"开始\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 36\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateSeparator),\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: endDate,\n                                                                onChange: handleEndDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: startDate || \"\",\n                                                                title: \"结束日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !endDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"结束\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 34\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    (startDate || endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().clearDateBtn),\n                                                        onClick: ()=>{\n                                                            setStartDate(\"\");\n                                                            setEndDate(\"\");\n                                                        },\n                                                        title: \"清除日期筛选\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().rightContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchBox),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: 18,\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchIcon)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索任务名称、任务内容...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchInput)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().publishTaskBtn),\n                                    onClick: handlePublishTaskClick,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 894,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"发布任务\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 879,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tasksTable),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableScrollContainer),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableHeader),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"剩余时间\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"完成率\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"操作\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableBody),\n                                            children: getFilteredTasks().length > 0 ? getFilteredTasks().map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableRow),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: task.taskName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().statusBadge), \" \").concat(getStatusClassName(task.status)),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 918,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: (()=>{\n                                                                const remainingTime = calculateRemainingTime(task);\n                                                                let className = (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTime);\n                                                                if (remainingTime === \"-\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished));\n                                                                } else if (remainingTime === \"已超时\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue));\n                                                                } else if (remainingTime.includes(\"小时\")) {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent));\n                                                                } else {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal));\n                                                                }\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: className,\n                                                                    children: remainingTime\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 23\n                                                                }, undefined);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: [\n                                                                task.completionRate,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actions),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().viewBtn)),\n                                                                        onClick: ()=>handleTaskView(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 953,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"查看\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().editBtn)),\n                                                                        onClick: ()=>handleTaskEdit(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"编辑\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().deleteBtn)),\n                                                                        onClick: ()=>handleTaskDelete(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"删除\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 963,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 15\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyState),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyIcon),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"64\",\n                                                            height: \"64\",\n                                                            viewBox: \"0 0 64 64\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"32\",\n                                                                    cy: \"32\",\n                                                                    r: \"30\",\n                                                                    stroke: \"#E5E7EB\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"#F9FAFB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 978,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M24 28h16M24 32h12M24 36h8\",\n                                                                    stroke: \"#9CA3AF\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"44\",\n                                                                    cy: \"20\",\n                                                                    r: \"8\",\n                                                                    fill: \"#FEF3C7\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M41 20h6M44 17v6\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 981,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyTitle),\n                                                        children: \"暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyDescription),\n                                                        children: classes.length === 0 ? \"暂无班级数据\" : selectedClass === null ? \"当前显示所有班级的任务数据\" : \"当前班级暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 975,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 901,\n                                columnNumber: 9\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            isOpen: isSchoolModalOpen,\n                            onClose: ()=>setIsSchoolModalOpen(false),\n                            onSchoolSelect: handleSchoolSelect,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            isOpen: isClassModalOpen,\n                            onClose: ()=>setIsClassModalOpen(false),\n                            onBack: ()=>{\n                                setIsClassModalOpen(false);\n                                setIsSchoolModalOpen(true);\n                            },\n                            onClassSelect: handleClassSelect,\n                            selectedSchool: modalSelectedSchool,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1004,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            isOpen: isTemplateModalOpen,\n                            onClose: ()=>setIsTemplateModalOpen(false),\n                            onBack: ()=>{\n                                setIsTemplateModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            selectedSchool: modalSelectedSchool,\n                            selectedClass: selectedClass,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1016,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isOpen: isPublishTaskModalOpen,\n                            onClose: ()=>setIsPublishTaskModalOpen(false),\n                            onBack: ()=>{\n                                setIsPublishTaskModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            modalData: publishModalData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__.TaskDetailModal, {\n                            task: selectedTaskForDetail,\n                            visible: isTaskDetailModalVisible,\n                            onClose: ()=>{\n                                setIsTaskDetailModalVisible(false);\n                                setSelectedTaskForDetail(null);\n                            },\n                            students: students.map((student)=>({\n                                    id: student.id,\n                                    userId: student.id,\n                                    studentNumber: \"\".concat(student.id),\n                                    nickName: student.name,\n                                    avatarUrl: \"\",\n                                    availablePoints: 0,\n                                    totalPoints: 0,\n                                    classId: student.classId,\n                                    className: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className) || \"\",\n                                    schoolId: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) || 0,\n                                    schoolName: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName) || \"\"\n                                })),\n                            onRefresh: loadTasksData,\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1041,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__.EditTaskModal, {\n                            visible: isEditTaskModalVisible,\n                            task: selectedTaskForEdit,\n                            onClose: ()=>{\n                                setIsEditTaskModalVisible(false);\n                                setSelectedTaskForEdit(null);\n                            },\n                            onSuccess: ()=>{\n                                // 编辑成功后刷新任务列表\n                                loadTasksData();\n                            },\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 877,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n            lineNumber: 648,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n        lineNumber: 646,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassTasks, \"mULc1hhR6Iaw9KD3Igc769HEwBc=\");\n_c = ClassTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassTasks);\nvar _c;\n$RefreshReg$(_c, \"ClassTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\n"));

/***/ })

});