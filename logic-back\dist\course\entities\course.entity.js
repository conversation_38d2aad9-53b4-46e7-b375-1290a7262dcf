"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Course = void 0;
const typeorm_1 = require("typeorm");
const course_series_entity_1 = require("./course-series.entity");
let Course = class Course {
    id;
    seriesId;
    title;
    description;
    coverImage;
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    contentConfig;
    teachingInfo;
    additionalResources;
    orderIndex;
    status;
    creatorId;
    videoName;
    firstTeachingTitle;
    resourcesCount;
    createdAt;
    updatedAt;
    series;
};
exports.Course = Course;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '课程ID' }),
    __metadata("design:type", Number)
], Course.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_series'),
    (0, typeorm_1.Column)({ type: 'int', name: 'series_id', comment: '所属系列ID' }),
    __metadata("design:type", Number)
], Course.prototype, "seriesId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, comment: '课程标题' }),
    __metadata("design:type", String)
], Course.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '课程简介' }),
    __metadata("design:type", String)
], Course.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, name: 'cover_image', nullable: true, comment: '课程封面' }),
    __metadata("design:type", String)
], Course.prototype, "coverImage", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_content_types'),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'has_video', default: 0, comment: '是否包含视频：0=否，1=是' }),
    __metadata("design:type", Number)
], Course.prototype, "hasVideo", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_content_types'),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'has_document', default: 0, comment: '是否包含文档：0=否，1=是' }),
    __metadata("design:type", Number)
], Course.prototype, "hasDocument", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_content_types'),
    (0, typeorm_1.Column)({ type: 'tinyint', name: 'has_audio', default: 0, comment: '是否包含音频：0=否，1=是' }),
    __metadata("design:type", Number)
], Course.prototype, "hasAudio", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_video_duration'),
    (0, typeorm_1.Column)({ type: 'int', name: 'video_duration', default: 0, comment: '视频时长(秒)' }),
    __metadata("design:type", Number)
], Course.prototype, "videoDuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', name: 'content_config', nullable: true, comment: '详细内容配置' }),
    __metadata("design:type", Object)
], Course.prototype, "contentConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', name: 'teaching_info', nullable: true, comment: '教学相关信息' }),
    __metadata("design:type", Object)
], Course.prototype, "teachingInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', name: 'additional_resources', nullable: true, comment: '辅助学习资源' }),
    __metadata("design:type", Object)
], Course.prototype, "additionalResources", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_series_status_order'),
    (0, typeorm_1.Column)({ type: 'int', name: 'order_index', default: 0, comment: '在系列中的顺序' }),
    __metadata("design:type", Number)
], Course.prototype, "orderIndex", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Index)('idx_series_status_order'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 0, comment: '状态：0=草稿，1=已发布，2=已归档' }),
    __metadata("design:type", Number)
], Course.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_creator'),
    (0, typeorm_1.Column)({ type: 'int', name: 'creator_id', nullable: true, comment: '创建者ID' }),
    __metadata("design:type", Number)
], Course.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_video_name'),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        name: 'video_name',
        nullable: true,
        comment: '视频文件名',
        select: false,
    }),
    __metadata("design:type", String)
], Course.prototype, "videoName", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_first_teaching_title'),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        name: 'first_teaching_title',
        nullable: true,
        comment: '第一个教学信息标题',
        select: false,
    }),
    __metadata("design:type", String)
], Course.prototype, "firstTeachingTitle", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_resources_count'),
    (0, typeorm_1.Column)({
        type: 'int',
        name: 'resources_count',
        nullable: true,
        comment: '附加资源数量',
        select: false,
    }),
    __metadata("design:type", Number)
], Course.prototype, "resourcesCount", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_series_status_order'),
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], Course.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], Course.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_series_entity_1.CourseSeries, series => series.id),
    (0, typeorm_1.JoinColumn)({ name: 'series_id' }),
    __metadata("design:type", course_series_entity_1.CourseSeries)
], Course.prototype, "series", void 0);
exports.Course = Course = __decorate([
    (0, typeorm_1.Entity)('courses')
], Course);
//# sourceMappingURL=course.entity.js.map