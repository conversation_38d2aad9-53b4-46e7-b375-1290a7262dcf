"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteTagResponseDto = exports.DeleteTagDataDto = exports.TagDetailResponseDto = exports.TagDetailDataDto = exports.UpdateTagDto = exports.CreateTagDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateTagDto {
    name;
    color = '#007bff';
    category;
    description;
    status = 1;
}
exports.CreateTagDto = CreateTagDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签名称', example: '编程', maxLength: 50 }),
    (0, class_validator_1.IsNotEmpty)({ message: '标签名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '标签名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '标签名称长度必须在1-50个字符之间' }),
    __metadata("design:type", String)
], CreateTagDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签颜色', example: '#007bff', default: '#007bff' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '标签颜色必须是字符串' }),
    (0, class_validator_1.Matches)(/^#[0-9A-Fa-f]{6}$/, { message: '标签颜色必须是有效的十六进制颜色值，如 #007bff' }),
    __metadata("design:type", String)
], CreateTagDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 1, enum: [0, 1, 2, 3] }),
    (0, class_validator_1.IsNotEmpty)({ message: '标签分类不能为空' }),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '标签分类必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1, 2, 3], { message: '标签分类必须是 0=难度，1=类型，2=特色，3=其他 中的一个' }),
    __metadata("design:type", Number)
], CreateTagDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签描述', example: '编程相关课程' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '标签描述必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '标签描述长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateTagDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签状态：0=禁用，1=启用', example: 1, default: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '标签状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '标签状态必须是 0=禁用 或 1=启用' }),
    __metadata("design:type", Number)
], CreateTagDto.prototype, "status", void 0);
class UpdateTagDto {
    name;
    color;
    category;
    description;
    status;
}
exports.UpdateTagDto = UpdateTagDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签名称', example: '编程', maxLength: 50 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '标签名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '标签名称长度必须在1-50个字符之间' }),
    __metadata("design:type", String)
], UpdateTagDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签颜色', example: '#007bff' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '标签颜色必须是字符串' }),
    (0, class_validator_1.Matches)(/^#[0-9A-Fa-f]{6}$/, { message: '标签颜色必须是有效的十六进制颜色值，如 #007bff' }),
    __metadata("design:type", String)
], UpdateTagDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 1, enum: [0, 1, 2, 3] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '标签分类必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1, 2, 3], { message: '标签分类必须是 0=难度，1=类型，2=特色，3=其他 中的一个' }),
    __metadata("design:type", Number)
], UpdateTagDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签描述', example: '编程相关课程' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '标签描述必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '标签描述长度不能超过500个字符' }),
    __metadata("design:type", String)
], UpdateTagDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签状态：0=禁用，1=启用', example: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)({}, { message: '标签状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '标签状态必须是 0=禁用 或 1=启用' }),
    __metadata("design:type", Number)
], UpdateTagDto.prototype, "status", void 0);
class TagDetailDataDto {
    id;
    name;
    color;
    category;
    categoryLabel;
    description;
    usageCount;
    status;
    statusLabel;
    createdAt;
    updatedAt;
}
exports.TagDetailDataDto = TagDetailDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签ID', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDataDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签名称', example: '编程' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签颜色', example: '#007bff' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDataDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类标签', example: '类型' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "categoryLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签描述', example: '编程相关课程' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '使用次数', example: 25 }),
    __metadata("design:type", Number)
], TagDetailDataDto.prototype, "usageCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签状态：0=禁用，1=启用', example: 1 }),
    __metadata("design:type", Number)
], TagDetailDataDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签状态标签', example: '启用' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-10T10:30:00Z' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-01-10T10:30:00Z' }),
    __metadata("design:type", String)
], TagDetailDataDto.prototype, "updatedAt", void 0);
class TagDetailResponseDto {
    code;
    message;
    data;
}
exports.TagDetailResponseDto = TagDetailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], TagDetailResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], TagDetailResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '标签详情数据',
        type: () => TagDetailDataDto
    }),
    __metadata("design:type", TagDetailDataDto)
], TagDetailResponseDto.prototype, "data", void 0);
class DeleteTagDataDto {
    success;
}
exports.DeleteTagDataDto = DeleteTagDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '删除结果', example: true }),
    __metadata("design:type", Boolean)
], DeleteTagDataDto.prototype, "success", void 0);
class DeleteTagResponseDto {
    code;
    message;
    data;
}
exports.DeleteTagResponseDto = DeleteTagResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], DeleteTagResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: '标签删除成功' }),
    __metadata("design:type", String)
], DeleteTagResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应数据', type: () => DeleteTagDataDto }),
    __metadata("design:type", DeleteTagDataDto)
], DeleteTagResponseDto.prototype, "data", void 0);
//# sourceMappingURL=tag-management.dto.js.map