"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = void 0;
const common_1 = require("@nestjs/common");
class ValidationUtils {
    static validateTitle(title, fieldName = '标题') {
        if (!title || title.trim() === '') {
            throw new common_1.BadRequestException(`${fieldName}不能为空`);
        }
    }
    static validatePermission(resourceCreatorId, currentUserId, action = '操作') {
        if (resourceCreatorId !== currentUserId) {
            throw new common_1.ForbiddenException(`无权限${action}此资源`);
        }
    }
    static validateRequiredId(id, fieldName) {
        if (!id || id <= 0) {
            throw new common_1.BadRequestException(`必须指定有效的${fieldName}`);
        }
    }
    static validateCourseData(courseData) {
        this.validateTitle(courseData.title, '课程标题');
        this.validateRequiredId(courseData.seriesId, '所属系列ID');
    }
    static validateSeriesData(seriesData) {
        this.validateTitle(seriesData.title, '系列名称');
    }
    static validateTaskTemplateData(templateData) {
        this.validateTitle(templateData.taskName, '任务名称');
    }
    static validateAndNormalizePagination(page, pageSize) {
        const normalizedPage = Math.max(1, page || 1);
        let normalizedPageSize;
        if (pageSize === undefined || pageSize === null) {
            normalizedPageSize = 10;
        }
        else if (pageSize <= 0) {
            normalizedPageSize = 1;
        }
        else if (pageSize > 100) {
            normalizedPageSize = 100;
        }
        else {
            normalizedPageSize = pageSize;
        }
        return {
            page: normalizedPage,
            pageSize: normalizedPageSize
        };
    }
}
exports.ValidationUtils = ValidationUtils;
//# sourceMappingURL=validation.utils.js.map