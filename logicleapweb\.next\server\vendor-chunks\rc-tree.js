"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-tree";
exports.ids = ["vendor-chunks/rc-tree"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-tree/es/DropIndicator.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-tree/es/DropIndicator.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropIndicator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction DropIndicator(_ref) {\n  var dropPosition = _ref.dropPosition,\n    dropLevelOffset = _ref.dropLevelOffset,\n    indent = _ref.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: style\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9Ecm9wSW5kaWNhdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUNoQjtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnREFBbUI7QUFDekM7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9Ecm9wSW5kaWNhdG9yLmpzPzFhYmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRHJvcEluZGljYXRvcihfcmVmKSB7XG4gIHZhciBkcm9wUG9zaXRpb24gPSBfcmVmLmRyb3BQb3NpdGlvbixcbiAgICBkcm9wTGV2ZWxPZmZzZXQgPSBfcmVmLmRyb3BMZXZlbE9mZnNldCxcbiAgICBpbmRlbnQgPSBfcmVmLmluZGVudDtcbiAgdmFyIHN0eWxlID0ge1xuICAgIHBvaW50ZXJFdmVudHM6ICdub25lJyxcbiAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICByaWdodDogMCxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZWQnLFxuICAgIGhlaWdodDogMlxuICB9O1xuICBzd2l0Y2ggKGRyb3BQb3NpdGlvbikge1xuICAgIGNhc2UgLTE6XG4gICAgICBzdHlsZS50b3AgPSAwO1xuICAgICAgc3R5bGUubGVmdCA9IC1kcm9wTGV2ZWxPZmZzZXQgKiBpbmRlbnQ7XG4gICAgICBicmVhaztcbiAgICBjYXNlIDE6XG4gICAgICBzdHlsZS5ib3R0b20gPSAwO1xuICAgICAgc3R5bGUubGVmdCA9IC1kcm9wTGV2ZWxPZmZzZXQgKiBpbmRlbnQ7XG4gICAgICBicmVhaztcbiAgICBjYXNlIDA6XG4gICAgICBzdHlsZS5ib3R0b20gPSAwO1xuICAgICAgc3R5bGUubGVmdCA9IGluZGVudDtcbiAgICAgIGJyZWFrO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgc3R5bGU6IHN0eWxlXG4gIH0pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/DropIndicator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/Indent.js":
/*!*******************************************!*\
  !*** ./node_modules/rc-tree/es/Indent.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar Indent = function Indent(_ref) {\n  var prefixCls = _ref.prefixCls,\n    level = _ref.level,\n    isStart = _ref.isStart,\n    isEnd = _ref.isEnd;\n  var baseClassName = \"\".concat(prefixCls, \"-indent-unit\");\n  var list = [];\n  for (var i = 0; i < level; i += 1) {\n    list.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"span\", {\n      key: i,\n      className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(baseClassName, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, \"\".concat(baseClassName, \"-start\"), isStart[i]), \"\".concat(baseClassName, \"-end\"), isEnd[i]))\n    }));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"span\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, list);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.memo(Indent));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9JbmRlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdFO0FBQ3BDO0FBQ0w7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsV0FBVztBQUM3Qiw0QkFBNEIsZ0RBQW1CO0FBQy9DO0FBQ0EsaUJBQWlCLGlEQUFVLGdCQUFnQixxRkFBZSxDQUFDLHFGQUFlLEdBQUc7QUFDN0UsS0FBSztBQUNMO0FBQ0Esc0JBQXNCLGdEQUFtQjtBQUN6QztBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsOEVBQTRCLHVDQUFVLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9JbmRlbnQuanM/ZDg1MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgSW5kZW50ID0gZnVuY3Rpb24gSW5kZW50KF9yZWYpIHtcbiAgdmFyIHByZWZpeENscyA9IF9yZWYucHJlZml4Q2xzLFxuICAgIGxldmVsID0gX3JlZi5sZXZlbCxcbiAgICBpc1N0YXJ0ID0gX3JlZi5pc1N0YXJ0LFxuICAgIGlzRW5kID0gX3JlZi5pc0VuZDtcbiAgdmFyIGJhc2VDbGFzc05hbWUgPSBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWluZGVudC11bml0XCIpO1xuICB2YXIgbGlzdCA9IFtdO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IGxldmVsOyBpICs9IDEpIHtcbiAgICBsaXN0LnB1c2goIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3BhblwiLCB7XG4gICAgICBrZXk6IGksXG4gICAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMoYmFzZUNsYXNzTmFtZSwgX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eSh7fSwgXCJcIi5jb25jYXQoYmFzZUNsYXNzTmFtZSwgXCItc3RhcnRcIiksIGlzU3RhcnRbaV0pLCBcIlwiLmNvbmNhdChiYXNlQ2xhc3NOYW1lLCBcIi1lbmRcIiksIGlzRW5kW2ldKSlcbiAgICB9KSk7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3BhblwiLCB7XG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItaW5kZW50XCIpXG4gIH0sIGxpc3QpO1xufTtcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5tZW1vKEluZGVudCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/Indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/MotionTreeNode.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-tree/es/MotionTreeNode.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectDestructuringEmpty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectDestructuringEmpty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _contextTypes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./contextTypes */ \"(ssr)/./node_modules/rc-tree/es/contextTypes.js\");\n/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TreeNode */ \"(ssr)/./node_modules/rc-tree/es/TreeNode.js\");\n/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./useUnmount */ \"(ssr)/./node_modules/rc-tree/es/useUnmount.js\");\n/* harmony import */ var _utils_treeUtil__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/treeUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\");\n\n\n\n\nvar _excluded = [\"className\", \"style\", \"motion\", \"motionNodes\", \"motionType\", \"onMotionStart\", \"onMotionEnd\", \"active\", \"treeNodeRequiredProps\"];\n\n\n\n\n\n\n\n\nvar MotionTreeNode = function MotionTreeNode(_ref, ref) {\n  var className = _ref.className,\n    style = _ref.style,\n    motion = _ref.motion,\n    motionNodes = _ref.motionNodes,\n    motionType = _ref.motionType,\n    onOriginMotionStart = _ref.onMotionStart,\n    onOriginMotionEnd = _ref.onMotionEnd,\n    active = _ref.active,\n    treeNodeRequiredProps = _ref.treeNodeRequiredProps,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_7__.useState(true),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    visible = _React$useState2[0],\n    setVisible = _React$useState2[1];\n  var _React$useContext = react__WEBPACK_IMPORTED_MODULE_7__.useContext(_contextTypes__WEBPACK_IMPORTED_MODULE_8__.TreeContext),\n    prefixCls = _React$useContext.prefixCls;\n\n  // Calculate target visible here.\n  // And apply in effect to make `leave` motion work.\n  var targetVisible = motionNodes && motionType !== 'hide';\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(function () {\n    if (motionNodes) {\n      if (targetVisible !== visible) {\n        setVisible(targetVisible);\n      }\n    }\n  }, [motionNodes]);\n  var triggerMotionStart = function triggerMotionStart() {\n    if (motionNodes) {\n      onOriginMotionStart();\n    }\n  };\n\n  // Should only trigger once\n  var triggerMotionEndRef = react__WEBPACK_IMPORTED_MODULE_7__.useRef(false);\n  var triggerMotionEnd = function triggerMotionEnd() {\n    if (motionNodes && !triggerMotionEndRef.current) {\n      triggerMotionEndRef.current = true;\n      onOriginMotionEnd();\n    }\n  };\n\n  // Effect if unmount\n  (0,_useUnmount__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(triggerMotionStart, triggerMotionEnd);\n\n  // Motion end event\n  var onVisibleChanged = function onVisibleChanged(nextVisible) {\n    if (targetVisible === nextVisible) {\n      triggerMotionEnd();\n    }\n  };\n  if (motionNodes) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_5__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n      ref: ref,\n      visible: visible\n    }, motion, {\n      motionAppear: motionType === 'show',\n      onVisibleChanged: onVisibleChanged\n    }), function (_ref2, motionRef) {\n      var motionClassName = _ref2.className,\n        motionStyle = _ref2.style;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(\"div\", {\n        ref: motionRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(\"\".concat(prefixCls, \"-treenode-motion\"), motionClassName),\n        style: motionStyle\n      }, motionNodes.map(function (treeNode) {\n        var restProps = Object.assign({}, ((0,_babel_runtime_helpers_esm_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(treeNode.data), treeNode.data)),\n          title = treeNode.title,\n          key = treeNode.key,\n          isStart = treeNode.isStart,\n          isEnd = treeNode.isEnd;\n        delete restProps.children;\n        var treeNodeProps = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_11__.getTreeNodeProps)(key, treeNodeRequiredProps);\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_TreeNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, treeNodeProps, {\n          title: title,\n          active: active,\n          data: treeNode.data,\n          key: key,\n          isStart: isStart,\n          isEnd: isEnd\n        }));\n      }));\n    });\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_TreeNode__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    domRef: ref,\n    className: className,\n    style: style\n  }, props, {\n    active: active\n  }));\n};\nMotionTreeNode.displayName = 'MotionTreeNode';\nvar RefMotionTreeNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.forwardRef(MotionTreeNode);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefMotionTreeNode);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/MotionTreeNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/NodeList.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-tree/es/NodeList.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MOTION_KEY: () => (/* binding */ MOTION_KEY),\n/* harmony export */   MotionEntity: () => (/* binding */ MotionEntity),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getMinimumRangeTransitionRange: () => (/* binding */ getMinimumRangeTransitionRange)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectDestructuringEmpty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectDestructuringEmpty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_virtual_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-virtual-list */ \"(ssr)/./node_modules/rc-virtual-list/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _MotionTreeNode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MotionTreeNode */ \"(ssr)/./node_modules/rc-tree/es/MotionTreeNode.js\");\n/* harmony import */ var _utils_diffUtil__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/diffUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/diffUtil.js\");\n/* harmony import */ var _utils_treeUtil__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/treeUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"data\", \"selectable\", \"checkable\", \"expandedKeys\", \"selectedKeys\", \"checkedKeys\", \"loadedKeys\", \"loadingKeys\", \"halfCheckedKeys\", \"keyEntities\", \"disabled\", \"dragging\", \"dragOverNodeKey\", \"dropPosition\", \"motion\", \"height\", \"itemHeight\", \"virtual\", \"focusable\", \"activeItem\", \"focused\", \"tabIndex\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onActiveChange\", \"onListChangeStart\", \"onListChangeEnd\"];\n/**\n * Handle virtual list of the TreeNodes.\n */\n\n\n\n\n\n\n\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar noop = function noop() {};\nvar MOTION_KEY = \"RC_TREE_MOTION_\".concat(Math.random());\nvar MotionNode = {\n  key: MOTION_KEY\n};\nvar MotionEntity = {\n  key: MOTION_KEY,\n  level: 0,\n  index: 0,\n  pos: '0',\n  node: MotionNode,\n  nodes: [MotionNode]\n};\nvar MotionFlattenData = {\n  parent: null,\n  children: [],\n  pos: MotionEntity.pos,\n  data: MotionNode,\n  title: null,\n  key: MOTION_KEY,\n  /** Hold empty list here since we do not use it */\n  isStart: [],\n  isEnd: []\n};\n/**\n * We only need get visible content items to play the animation.\n */\nfunction getMinimumRangeTransitionRange(list, virtual, height, itemHeight) {\n  if (virtual === false || !height) {\n    return list;\n  }\n  return list.slice(0, Math.ceil(height / itemHeight) + 1);\n}\nfunction itemKey(item) {\n  var key = item.key,\n    pos = item.pos;\n  return (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_9__.getKey)(key, pos);\n}\nfunction getAccessibilityPath(item) {\n  var path = String(item.data.key);\n  var current = item;\n  while (current.parent) {\n    current = current.parent;\n    path = \"\".concat(current.data.key, \" > \").concat(path);\n  }\n  return path;\n}\nvar NodeList = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    data = props.data,\n    selectable = props.selectable,\n    checkable = props.checkable,\n    expandedKeys = props.expandedKeys,\n    selectedKeys = props.selectedKeys,\n    checkedKeys = props.checkedKeys,\n    loadedKeys = props.loadedKeys,\n    loadingKeys = props.loadingKeys,\n    halfCheckedKeys = props.halfCheckedKeys,\n    keyEntities = props.keyEntities,\n    disabled = props.disabled,\n    dragging = props.dragging,\n    dragOverNodeKey = props.dragOverNodeKey,\n    dropPosition = props.dropPosition,\n    motion = props.motion,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    virtual = props.virtual,\n    focusable = props.focusable,\n    activeItem = props.activeItem,\n    focused = props.focused,\n    tabIndex = props.tabIndex,\n    onKeyDown = props.onKeyDown,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onActiveChange = props.onActiveChange,\n    onListChangeStart = props.onListChangeStart,\n    onListChangeEnd = props.onListChangeEnd,\n    domProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n\n  // =============================== Ref ================================\n  var listRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(null);\n  var indentMeasurerRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_6__.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: function scrollTo(scroll) {\n        listRef.current.scrollTo(scroll);\n      },\n      getIndentWidth: function getIndentWidth() {\n        return indentMeasurerRef.current.offsetWidth;\n      }\n    };\n  });\n\n  // ============================== Motion ==============================\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState(expandedKeys),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    prevExpandedKeys = _React$useState2[0],\n    setPrevExpandedKeys = _React$useState2[1];\n  var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_6__.useState(data),\n    _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState3, 2),\n    prevData = _React$useState4[0],\n    setPrevData = _React$useState4[1];\n  var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_6__.useState(data),\n    _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState5, 2),\n    transitionData = _React$useState6[0],\n    setTransitionData = _React$useState6[1];\n  var _React$useState7 = react__WEBPACK_IMPORTED_MODULE_6__.useState([]),\n    _React$useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState7, 2),\n    transitionRange = _React$useState8[0],\n    setTransitionRange = _React$useState8[1];\n  var _React$useState9 = react__WEBPACK_IMPORTED_MODULE_6__.useState(null),\n    _React$useState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState9, 2),\n    motionType = _React$useState10[0],\n    setMotionType = _React$useState10[1];\n\n  // When motion end but data change, this will makes data back to previous one\n  var dataRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(data);\n  dataRef.current = data;\n  function onMotionEnd() {\n    var latestData = dataRef.current;\n    setPrevData(latestData);\n    setTransitionData(latestData);\n    setTransitionRange([]);\n    setMotionType(null);\n    onListChangeEnd();\n  }\n\n  // Do animation if expanded keys changed\n  // layoutEffect here to avoid blink of node removing\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n    setPrevExpandedKeys(expandedKeys);\n    var diffExpanded = (0,_utils_diffUtil__WEBPACK_IMPORTED_MODULE_8__.findExpandedKeys)(prevExpandedKeys, expandedKeys);\n    if (diffExpanded.key !== null) {\n      if (diffExpanded.add) {\n        var keyIndex = prevData.findIndex(function (_ref) {\n          var key = _ref.key;\n          return key === diffExpanded.key;\n        });\n        var rangeNodes = getMinimumRangeTransitionRange((0,_utils_diffUtil__WEBPACK_IMPORTED_MODULE_8__.getExpandRange)(prevData, data, diffExpanded.key), virtual, height, itemHeight);\n        var newTransitionData = prevData.slice();\n        newTransitionData.splice(keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(newTransitionData);\n        setTransitionRange(rangeNodes);\n        setMotionType('show');\n      } else {\n        var _keyIndex = data.findIndex(function (_ref2) {\n          var key = _ref2.key;\n          return key === diffExpanded.key;\n        });\n        var _rangeNodes = getMinimumRangeTransitionRange((0,_utils_diffUtil__WEBPACK_IMPORTED_MODULE_8__.getExpandRange)(data, prevData, diffExpanded.key), virtual, height, itemHeight);\n        var _newTransitionData = data.slice();\n        _newTransitionData.splice(_keyIndex + 1, 0, MotionFlattenData);\n        setTransitionData(_newTransitionData);\n        setTransitionRange(_rangeNodes);\n        setMotionType('hide');\n      }\n    } else if (prevData !== data) {\n      // If whole data changed, we just refresh the list\n      setPrevData(data);\n      setTransitionData(data);\n    }\n  }, [expandedKeys, data]);\n\n  // We should clean up motion if is changed by dragging\n  react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function () {\n    if (!dragging) {\n      onMotionEnd();\n    }\n  }, [dragging]);\n  var mergedData = motion ? transitionData : data;\n  var treeNodeRequiredProps = {\n    expandedKeys: expandedKeys,\n    selectedKeys: selectedKeys,\n    loadedKeys: loadedKeys,\n    loadingKeys: loadingKeys,\n    checkedKeys: checkedKeys,\n    halfCheckedKeys: halfCheckedKeys,\n    dragOverNodeKey: dragOverNodeKey,\n    dropPosition: dropPosition,\n    keyEntities: keyEntities\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(react__WEBPACK_IMPORTED_MODULE_6__.Fragment, null, focused && activeItem && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, getAccessibilityPath(activeItem)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"input\", {\n    style: HIDDEN_STYLE,\n    disabled: focusable === false || disabled,\n    tabIndex: focusable !== false ? tabIndex : null,\n    onKeyDown: onKeyDown,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    value: \"\",\n    onChange: noop,\n    \"aria-label\": \"for screen reader\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-treenode\"),\n    \"aria-hidden\": true,\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      visibility: 'hidden',\n      height: 0,\n      overflow: 'hidden',\n      border: 0,\n      padding: 0\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-indent\")\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n    ref: indentMeasurerRef,\n    className: \"\".concat(prefixCls, \"-indent-unit\")\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(rc_virtual_list__WEBPACK_IMPORTED_MODULE_5__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, domProps, {\n    data: mergedData,\n    itemKey: itemKey,\n    height: height,\n    fullHeight: false,\n    virtual: virtual,\n    itemHeight: itemHeight,\n    prefixCls: \"\".concat(prefixCls, \"-list\"),\n    ref: listRef,\n    onVisibleChange: function onVisibleChange(originList) {\n      // The best match is using `fullList` - `originList` = `restList`\n      // and check the `restList` to see if has the MOTION_KEY node\n      // but this will cause performance issue for long list compare\n      // we just check `originList` and repeat trigger `onMotionEnd`\n      if (originList.every(function (item) {\n        return itemKey(item) !== MOTION_KEY;\n      })) {\n        onMotionEnd();\n      }\n    }\n  }), function (treeNode) {\n    var pos = treeNode.pos,\n      restProps = Object.assign({}, ((0,_babel_runtime_helpers_esm_objectDestructuringEmpty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(treeNode.data), treeNode.data)),\n      title = treeNode.title,\n      key = treeNode.key,\n      isStart = treeNode.isStart,\n      isEnd = treeNode.isEnd;\n    var mergedKey = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_9__.getKey)(key, pos);\n    delete restProps.key;\n    delete restProps.children;\n    var treeNodeProps = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_9__.getTreeNodeProps)(mergedKey, treeNodeRequiredProps);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_MotionTreeNode__WEBPACK_IMPORTED_MODULE_7__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, treeNodeProps, {\n      title: title,\n      active: !!activeItem && key === activeItem.key,\n      pos: pos,\n      data: treeNode.data,\n      isStart: isStart,\n      isEnd: isEnd,\n      motion: motion,\n      motionNodes: key === MOTION_KEY ? transitionRange : null,\n      motionType: motionType,\n      onMotionStart: onListChangeStart,\n      onMotionEnd: onMotionEnd,\n      treeNodeRequiredProps: treeNodeRequiredProps,\n      onMouseMove: function onMouseMove() {\n        onActiveChange(null);\n      }\n    }));\n  }));\n});\nNodeList.displayName = 'NodeList';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NodeList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/NodeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/Tree.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tree/es/Tree.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _contextTypes__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./contextTypes */ \"(ssr)/./node_modules/rc-tree/es/contextTypes.js\");\n/* harmony import */ var _DropIndicator__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./DropIndicator */ \"(ssr)/./node_modules/rc-tree/es/DropIndicator.js\");\n/* harmony import */ var _NodeList__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./NodeList */ \"(ssr)/./node_modules/rc-tree/es/NodeList.js\");\n/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./TreeNode */ \"(ssr)/./node_modules/rc-tree/es/TreeNode.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/rc-tree/es/util.js\");\n/* harmony import */ var _utils_conductUtil__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./utils/conductUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/conductUtil.js\");\n/* harmony import */ var _utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./utils/keyUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\");\n/* harmony import */ var _utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./utils/treeUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\");\n\n\n\n\n\n\n\n\n\n\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Tree, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Tree);\n  function Tree() {\n    var _this;\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"destroyed\", false);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"delayedDragEnterLogic\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"loadingRetryTimes\", {});\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"state\", {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      // the drop position of abstract-drop-node, inside 0, top -1, bottom 1\n      dropContainerKey: null,\n      // the container key of abstract-drop-node if dropPosition is -1 or 1\n      dropLevelOffset: null,\n      // the drop level offset of abstract-drag-over-node\n      dropTargetPos: null,\n      // the pos of abstract-drop-node\n      dropAllowed: true,\n      // if drop to abstract-drop-node is allowed\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.fillFieldNames)()\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"dragStartMousePosition\", null);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"dragNode\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"currentMouseOverDroppableNodeKey\", null);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"listRef\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_14__.createRef());\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDragStart\", function (event, node) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = node.props.eventKey;\n      _this.dragNode = node;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: (0,_util__WEBPACK_IMPORTED_MODULE_19__.getDragChildrenKeys)(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 || onDragStart({\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props)\n      });\n    });\n    /**\n     * [Legacy] Select handler is smaller than node,\n     * so that this will trigger when drag enter node or select handler.\n     * This is a little tricky if customize css without padding.\n     * Better for use mouse move event to refresh drag state.\n     * But let's just keep it to avoid event trigger logic change.\n     */\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDragEnter\", function (event, node) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var _node$props = node.props,\n        pos = _node$props.pos,\n        eventKey = _node$props.eventKey;\n      var _assertThisInitialize = (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this),\n        dragNode = _assertThisInitialize.dragNode;\n\n      // record the key of node which is latest entered, used in dragleave event.\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!dragNode) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = (0,_util__WEBPACK_IMPORTED_MODULE_19__.calcDropPosition)(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.indexOf(dropTargetKey) !== -1 ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Side effect for delay drag\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (dragNode.props.eventKey !== node.props.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) return;\n          var newExpandedKeys = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(expandedKeys);\n          var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(keyEntities, node.props.eventKey);\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(expandedKeys, node.props.eventKey);\n          }\n          if (!_this.props.hasOwnProperty('expandedKeys')) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 || onExpand(newExpandedKeys, {\n            node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      }\n\n      // Skip if drag node is self\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Update drag over node and drag state\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 || onDragEnter({\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props),\n        expandedKeys: expandedKeys\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDragOver\", function (event, node) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      var _assertThisInitialize2 = (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this),\n        dragNode = _assertThisInitialize2.dragNode;\n      if (!dragNode) {\n        return;\n      }\n      var _calcDropPosition2 = (0,_util__WEBPACK_IMPORTED_MODULE_19__.calcDropPosition)(event, dragNode, node, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.indexOf(dropTargetKey) !== -1 || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed calculated by calcDropPosition\n        return;\n      }\n\n      // Update drag position\n\n      if (dragNode.props.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 || onDragOver({\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props)\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDragLeave\", function (event, node) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === node.props.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 || onDragLeave({\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props)\n      });\n    });\n    // since stopPropagation() is called in treeNode\n    // if onWindowDrag is called, whice means state is keeped, drag state should be cleared\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onWindowDragEnd\", function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    // if onNodeDragEnd is called, onWindowDragEnd won't be called since stopPropagation() is called\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDragEnd\", function (event, node) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(node.props)\n      });\n      _this.dragNode = null;\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDrop\", function (event, node) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) return;\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.getTreeNodeProps)(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.key) === dropTargetKey,\n        data: (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(_this.state.keyEntities, dropTargetKey).node\n      });\n      var dropToChild = dragChildrenKeys.indexOf(dropTargetKey) !== -1;\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = (0,_util__WEBPACK_IMPORTED_MODULE_19__.posToArr)(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(abstractDropNodeProps),\n        dragNode: _this.dragNode ? (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)(_this.dragNode.props) : null,\n        dragNodesKeys: [_this.dragNode.props.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 || onDrop(dropResult);\n      }\n      _this.dragNode = null;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"cleanDragState\", function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"triggerExpandActionExpand\", function (e, treeNode) {\n      var _this$state5 = _this.state,\n        expandedKeys = _this$state5.expandedKeys,\n        flattenNodes = _this$state5.flattenNodes;\n      var expanded = treeNode.expanded,\n        key = treeNode.key,\n        isLeaf = treeNode.isLeaf;\n      if (isLeaf || e.shiftKey || e.metaKey || e.ctrlKey) {\n        return;\n      }\n      var node = flattenNodes.filter(function (nodeItem) {\n        return nodeItem.key === key;\n      })[0];\n      var eventNode = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.getTreeNodeProps)(key, _this.getTreeNodeRequiredProps())), {}, {\n        data: node.data\n      }));\n      _this.setExpandedKeys(expanded ? (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(expandedKeys, key) : (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(expandedKeys, key));\n      _this.onNodeExpand(e, eventNode);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeClick\", function (e, treeNode) {\n      var _this$props3 = _this.props,\n        onClick = _this$props3.onClick,\n        expandAction = _this$props3.expandAction;\n      if (expandAction === 'click') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onClick === null || onClick === void 0 || onClick(e, treeNode);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeDoubleClick\", function (e, treeNode) {\n      var _this$props4 = _this.props,\n        onDoubleClick = _this$props4.onDoubleClick,\n        expandAction = _this$props4.expandAction;\n      if (expandAction === 'doubleClick') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onDoubleClick === null || onDoubleClick === void 0 || onDoubleClick(e, treeNode);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeSelect\", function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        fieldNames = _this$state6.fieldNames;\n      var _this$props5 = _this.props,\n        onSelect = _this$props5.onSelect,\n        multiple = _this$props5.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected;\n\n      // Update selected keys\n      if (!targetSelected) {\n        selectedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(selectedKeys, key);\n      }\n\n      // [Legacy] Not found related usage in doc or upper libs\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(keyEntities, selectedKey);\n        if (!entity) return null;\n        return entity.node;\n      }).filter(function (node) {\n        return node;\n      });\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 || onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeCheck\", function (e, treeNode, checked) {\n      var _this$state7 = _this.state,\n        keyEntities = _this$state7.keyEntities,\n        oriCheckedKeys = _this$state7.checkedKeys,\n        oriHalfCheckedKeys = _this$state7.halfCheckedKeys;\n      var _this$props6 = _this.props,\n        checkStrictly = _this$props6.checkStrictly,\n        onCheck = _this$props6.onCheck;\n      var key = treeNode.key;\n\n      // Prepare trigger arguments\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(oriCheckedKeys, key) : (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(oriCheckedKeys, key);\n        var halfCheckedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(keyEntities, checkedKey);\n        }).filter(function (entity) {\n          return entity;\n        }).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = (0,_utils_conductUtil__WEBPACK_IMPORTED_MODULE_20__.conductCheck)([].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n        // If remove, we do it again to correction\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = (0,_utils_conductUtil__WEBPACK_IMPORTED_MODULE_20__.conductCheck)(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys;\n\n        // [Legacy] This is used for `rc-tree-select`\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(keyEntities, checkedKey);\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 || onCheck(checkedObj, eventObj);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeLoad\", function (treeNode) {\n      var _entity$children;\n      var key = treeNode.key;\n      var keyEntities = _this.state.keyEntities;\n\n      // Skip if has children already\n      var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_21__[\"default\"])(keyEntities, key);\n      if (entity !== null && entity !== void 0 && (_entity$children = entity.children) !== null && _entity$children !== void 0 && _entity$children.length) {\n        return;\n      }\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props7 = _this.props,\n            loadData = _this$props7.loadData,\n            onLoad = _this$props7.onLoad;\n          if (!loadData || loadedKeys.indexOf(key) !== -1 || loadingKeys.indexOf(key) !== -1) {\n            return null;\n          }\n\n          // Process load data\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(currentLoadedKeys, key);\n\n            // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n            onLoad === null || onLoad === void 0 || onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(prevState.loadingKeys, key)\n              };\n            });\n\n            // If exceed max retry times, we give up retry\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(loadingKeys, key)\n          };\n        });\n      });\n\n      // Not care warning if we ignore this\n      loadPromise.catch(function () {});\n      return loadPromise;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeMouseEnter\", function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        event: event,\n        node: node\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeMouseLeave\", function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        event: event,\n        node: node\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeContextMenu\", function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onFocus\", function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 || onFocus.apply(void 0, args);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onBlur\", function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 || onBlur.apply(void 0, args);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"getTreeNodeRequiredProps\", function () {\n      var _this$state8 = _this.state,\n        expandedKeys = _this$state8.expandedKeys,\n        selectedKeys = _this$state8.selectedKeys,\n        loadedKeys = _this$state8.loadedKeys,\n        loadingKeys = _this$state8.loadingKeys,\n        checkedKeys = _this$state8.checkedKeys,\n        halfCheckedKeys = _this$state8.halfCheckedKeys,\n        dragOverNodeKey = _this$state8.dragOverNodeKey,\n        dropPosition = _this$state8.dropPosition,\n        keyEntities = _this$state8.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    });\n    // =========================== Expanded ===========================\n    /** Set uncontrolled `expandedKeys`. This will also auto update `flattenNodes`. */\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"setExpandedKeys\", function (expandedKeys) {\n      var _this$state9 = _this.state,\n        treeData = _this$state9.treeData,\n        fieldNames = _this$state9.fieldNames;\n      var flattenNodes = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.flattenTreeData)(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onNodeExpand\", function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state10 = _this.state,\n        listChanging = _this$state10.listChanging,\n        fieldNames = _this$state10.fieldNames;\n      var _this$props8 = _this.props,\n        onExpand = _this$props8.onExpand,\n        loadData = _this$props8.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key];\n\n      // Do nothing when motion is in progress\n      if (listChanging) {\n        return;\n      }\n\n      // Update selected keys\n      var index = expandedKeys.indexOf(key);\n      var targetExpanded = !expanded;\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(expanded && index !== -1 || !expanded && index === -1, 'Expand state not sync with index check');\n      if (targetExpanded) {\n        expandedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrAdd)(expandedKeys, key);\n      } else {\n        expandedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(expandedKeys, key);\n      }\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 || onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      });\n\n      // Async Load data\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.flattenTreeData)(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = (0,_util__WEBPACK_IMPORTED_MODULE_19__.arrDel)(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onListChangeStart\", function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onListChangeEnd\", function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    });\n    // =========================== Keyboard ===========================\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onActiveChange\", function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var _this$props9 = _this.props,\n        onActiveChange = _this$props9.onActiveChange,\n        _this$props9$itemScro = _this$props9.itemScrollOffset,\n        itemScrollOffset = _this$props9$itemScro === void 0 ? 0 : _this$props9$itemScro;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey,\n          offset: itemScrollOffset\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 || onActiveChange(newActiveKey);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"getActiveItem\", function () {\n      var _this$state11 = _this.state,\n        activeKey = _this$state11.activeKey,\n        flattenNodes = _this$state11.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"offsetActiveKey\", function (offset) {\n      var _this$state12 = _this.state,\n        flattenNodes = _this$state12.flattenNodes,\n        activeKey = _this$state12.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      });\n\n      // Align with index\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var _key4 = item.key;\n        _this.onActiveChange(_key4);\n      } else {\n        _this.onActiveChange(null);\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"onKeyDown\", function (event) {\n      var _this$state13 = _this.state,\n        activeKey = _this$state13.activeKey,\n        expandedKeys = _this$state13.expandedKeys,\n        checkedKeys = _this$state13.checkedKeys,\n        fieldNames = _this$state13.fieldNames;\n      var _this$props10 = _this.props,\n        onKeyDown = _this$props10.onKeyDown,\n        checkable = _this$props10.checkable,\n        selectable = _this$props10.selectable;\n\n      // >>>>>>>>>> Direction\n      switch (event.which) {\n        case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      }\n\n      // >>>>>>>>>> Expand & Selection\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data[fieldNames.children] || []).length;\n        var eventNode = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertNodePropsToEventData)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.getTreeNodeProps)(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].key);\n              }\n              event.preventDefault();\n              break;\n            }\n\n          // Selection\n          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].ENTER:\n          case rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_11__[\"default\"].SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n    });\n    /**\n     * Only update the value which is not in props\n     */\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"setUncontrolledState\", function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (_this.props.hasOwnProperty(name)) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, newState), forceState));\n        }\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(_this), \"scrollTo\", function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    });\n    return _this;\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var _this$props11 = this.props,\n        activeKey = _this$props11.activeKey,\n        _this$props11$itemScr = _this$props11.itemScrollOffset,\n        itemScrollOffset = _this$props11$itemScr === void 0 ? 0 : _this$props11$itemScr;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey,\n            offset: itemScrollOffset\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state14 = this.state,\n        focused = _this$state14.focused,\n        flattenNodes = _this$state14.flattenNodes,\n        keyEntities = _this$state14.keyEntities,\n        draggingNodeKey = _this$state14.draggingNodeKey,\n        activeKey = _this$state14.activeKey,\n        dropLevelOffset = _this$state14.dropLevelOffset,\n        dropContainerKey = _this$state14.dropContainerKey,\n        dropTargetKey = _this$state14.dropTargetKey,\n        dropPosition = _this$state14.dropPosition,\n        dragOverNodeKey = _this$state14.dragOverNodeKey,\n        indent = _this$state14.indent;\n      var _this$props12 = this.props,\n        prefixCls = _this$props12.prefixCls,\n        className = _this$props12.className,\n        style = _this$props12.style,\n        showLine = _this$props12.showLine,\n        focusable = _this$props12.focusable,\n        _this$props12$tabInde = _this$props12.tabIndex,\n        tabIndex = _this$props12$tabInde === void 0 ? 0 : _this$props12$tabInde,\n        selectable = _this$props12.selectable,\n        showIcon = _this$props12.showIcon,\n        icon = _this$props12.icon,\n        switcherIcon = _this$props12.switcherIcon,\n        draggable = _this$props12.draggable,\n        checkable = _this$props12.checkable,\n        checkStrictly = _this$props12.checkStrictly,\n        disabled = _this$props12.disabled,\n        motion = _this$props12.motion,\n        loadData = _this$props12.loadData,\n        filterTreeNode = _this$props12.filterTreeNode,\n        height = _this$props12.height,\n        itemHeight = _this$props12.itemHeight,\n        virtual = _this$props12.virtual,\n        titleRender = _this$props12.titleRender,\n        dropIndicatorRender = _this$props12.dropIndicatorRender,\n        onContextMenu = _this$props12.onContextMenu,\n        onScroll = _this$props12.onScroll,\n        direction = _this$props12.direction,\n        rootClassName = _this$props12.rootClassName,\n        rootStyle = _this$props12.rootStyle;\n      var domProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(this.props, {\n        aria: true,\n        data: true\n      });\n\n      // It's better move to hooks but we just simply keep here\n      var draggableConfig;\n      if (draggable) {\n        if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_14__.createElement(_contextTypes__WEBPACK_IMPORTED_MODULE_15__.TreeContext.Provider, {\n        value: {\n          prefixCls: prefixCls,\n          selectable: selectable,\n          showIcon: showIcon,\n          icon: icon,\n          switcherIcon: switcherIcon,\n          draggable: draggableConfig,\n          draggingNodeKey: draggingNodeKey,\n          checkable: checkable,\n          checkStrictly: checkStrictly,\n          disabled: disabled,\n          keyEntities: keyEntities,\n          dropLevelOffset: dropLevelOffset,\n          dropContainerKey: dropContainerKey,\n          dropTargetKey: dropTargetKey,\n          dropPosition: dropPosition,\n          dragOverNodeKey: dragOverNodeKey,\n          indent: indent,\n          direction: direction,\n          dropIndicatorRender: dropIndicatorRender,\n          loadData: loadData,\n          filterTreeNode: filterTreeNode,\n          titleRender: titleRender,\n          onNodeClick: this.onNodeClick,\n          onNodeDoubleClick: this.onNodeDoubleClick,\n          onNodeExpand: this.onNodeExpand,\n          onNodeSelect: this.onNodeSelect,\n          onNodeCheck: this.onNodeCheck,\n          onNodeLoad: this.onNodeLoad,\n          onNodeMouseEnter: this.onNodeMouseEnter,\n          onNodeMouseLeave: this.onNodeMouseLeave,\n          onNodeContextMenu: this.onNodeContextMenu,\n          onNodeDragStart: this.onNodeDragStart,\n          onNodeDragEnter: this.onNodeDragEnter,\n          onNodeDragOver: this.onNodeDragOver,\n          onNodeDragLeave: this.onNodeDragLeave,\n          onNodeDragEnd: this.onNodeDragEnd,\n          onNodeDrop: this.onNodeDrop\n        }\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_14__.createElement(\"div\", {\n        role: \"tree\",\n        className: classnames__WEBPACK_IMPORTED_MODULE_10___default()(prefixCls, className, rootClassName, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, \"\".concat(prefixCls, \"-show-line\"), showLine), \"\".concat(prefixCls, \"-focused\"), focused), \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null)),\n        style: rootStyle\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_14__.createElement(_NodeList__WEBPACK_IMPORTED_MODULE_17__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && props.hasOwnProperty(name) || prevProps && prevProps[name] !== props[name];\n      }\n\n      // ================== Tree Node ==================\n      var treeData;\n\n      // fieldNames\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.fillFieldNames)(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      }\n\n      // Check if `treeData` or `children` changed and save into the state.\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertTreeToData)(props.children);\n      }\n\n      // Save flatten nodes info and convert `treeData` into keyEntities\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.convertDataToEntities)(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, _NodeList__WEBPACK_IMPORTED_MODULE_17__.MOTION_KEY, _NodeList__WEBPACK_IMPORTED_MODULE_17__.MotionEntity), entitiesMap.keyEntities);\n\n        // Warning if treeNode not provide key\n        if (true) {\n          (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.warningWithoutKey)(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities;\n\n      // ================ expandedKeys =================\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? (0,_util__WEBPACK_IMPORTED_MODULE_19__.conductExpandParent)(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, keyEntities);\n        delete cloneKeyEntities[_NodeList__WEBPACK_IMPORTED_MODULE_17__.MOTION_KEY];\n\n        // Only take the key who has the children to enhance the performance\n        var nextExpandedKeys = [];\n        Object.keys(cloneKeyEntities).forEach(function (key) {\n          var entity = cloneKeyEntities[key];\n          if (entity.children && entity.children.length) {\n            nextExpandedKeys.push(entity.key);\n          }\n        });\n        newState.expandedKeys = nextExpandedKeys;\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? (0,_util__WEBPACK_IMPORTED_MODULE_19__.conductExpandParent)(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      }\n\n      // ================ flattenNodes =================\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_22__.flattenTreeData)(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      }\n\n      // ================ selectedKeys =================\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.calcSelectedKeys)(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = (0,_util__WEBPACK_IMPORTED_MODULE_19__.calcSelectedKeys)(props.defaultSelectedKeys, props);\n        }\n      }\n\n      // ================= checkedKeys =================\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = (0,_util__WEBPACK_IMPORTED_MODULE_19__.parseCheckedKeys)(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = (0,_util__WEBPACK_IMPORTED_MODULE_19__.parseCheckedKeys)(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = (0,_util__WEBPACK_IMPORTED_MODULE_19__.parseCheckedKeys)(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = (0,_utils_conductUtil__WEBPACK_IMPORTED_MODULE_20__.conductCheck)(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      }\n\n      // ================= loadedKeys ==================\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(react__WEBPACK_IMPORTED_MODULE_14__.Component);\n(0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(Tree, \"defaultProps\", {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: _DropIndicator__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n  allowDrop: function allowDrop() {\n    return true;\n  },\n  expandAction: false\n});\n(0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(Tree, \"TreeNode\", _TreeNode__WEBPACK_IMPORTED_MODULE_18__[\"default\"]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Tree);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9UcmVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ0Y7QUFDYTtBQUNTO0FBQ047QUFDTjtBQUNvQjtBQUMxQjtBQUNNO0FBQ007QUFDeEU7QUFDQTs7QUFFb0M7QUFDSztBQUNJO0FBQ0o7QUFDVjtBQUNjO0FBQ0Q7QUFDb0I7QUFDOUI7QUFDZ0g7QUFDL0Y7QUFDWDtBQUN1STtBQUMvSztBQUNBO0FBQ0EsRUFBRSwrRUFBUztBQUNYLGVBQWUsa0ZBQVk7QUFDM0I7QUFDQTtBQUNBLElBQUkscUZBQWU7QUFDbkIseUVBQXlFLGFBQWE7QUFDdEY7QUFDQTtBQUNBO0FBQ0EsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQyxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDLElBQUkscUZBQWUsQ0FBQyw0RkFBc0IsZ0NBQWdDO0FBQzFFLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUMscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixnRUFBYztBQUNoQyxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQyxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUMsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQixpQ0FBaUMsNkNBQWU7QUFDMUYsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw4Q0FBTTtBQUNsQztBQUNBO0FBQ0EsMEJBQTBCLDJEQUFtQjtBQUM3QztBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsNkVBQTJCO0FBQ3pDLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsNEZBQXNCO0FBQ3hEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsd0RBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyx3RkFBa0I7QUFDbEQsdUJBQXVCLDJEQUFTO0FBQ2hDO0FBQ0EsOEJBQThCLDhDQUFNO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNkVBQTJCO0FBQzdDO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxjQUFjLDZFQUEyQjtBQUN6QztBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw0RkFBc0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isd0RBQWdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLGNBQWMsNkVBQTJCO0FBQ3pDLE9BQU87QUFDUCxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDZFQUEyQjtBQUN6QyxPQUFPO0FBQ1AsS0FBSztBQUNMO0FBQ0E7QUFDQSxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxjQUFjLDZFQUEyQjtBQUN6QyxPQUFPO0FBQ1A7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0Esa0NBQWtDLG9GQUFhLENBQUMsb0ZBQWEsR0FBRyxFQUFFLGtFQUFnQixzREFBc0Q7QUFDeEk7QUFDQSxjQUFjLDJEQUFTO0FBQ3ZCLE9BQU87QUFDUDtBQUNBLE1BQU0sK0RBQU87QUFDYixtQkFBbUIsZ0RBQVE7QUFDM0I7QUFDQTtBQUNBLGNBQWMsNkVBQTJCO0FBQ3pDLG1DQUFtQyw2RUFBMkI7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1Asc0JBQXNCLDZFQUEyQixDQUFDLG9GQUFhLENBQUMsb0ZBQWEsR0FBRyxFQUFFLGtFQUFnQiw0Q0FBNEM7QUFDOUk7QUFDQSxPQUFPO0FBQ1AsdUNBQXVDLDhDQUFNLHNCQUFzQiw4Q0FBTTtBQUN6RTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsdUJBQXVCLDhDQUFNO0FBQzdCLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUix1QkFBdUIsOENBQU07QUFDN0I7O0FBRUE7QUFDQTtBQUNBLHFCQUFxQiwyREFBUztBQUM5QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLDhDQUFNLHdCQUF3Qiw4Q0FBTTtBQUN4RSw4QkFBOEIsOENBQU07QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiwyREFBUztBQUMxQixTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQSw0QkFBNEIsaUVBQVksV0FBVyx3RkFBa0I7QUFDckU7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixpRUFBWTtBQUMzQztBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsMkRBQVM7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiwyREFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw4Q0FBTTs7QUFFdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSw2QkFBNkIsOENBQU07QUFDbkM7QUFDQSxhQUFhO0FBQ2I7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLDZCQUE2Qiw4Q0FBTTtBQUNuQztBQUNBLGFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLCtEQUFPO0FBQ3JCO0FBQ0EsNEJBQTRCLDhDQUFNO0FBQ2xDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSx5QkFBeUIsOENBQU07QUFDL0I7QUFDQSxTQUFTO0FBQ1QsT0FBTzs7QUFFUDtBQUNBLHNDQUFzQztBQUN0QztBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCw2RUFBNkUsZUFBZTtBQUM1RjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSw2RUFBNkUsZUFBZTtBQUM1RjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLGlFQUFlO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTSwrREFBTztBQUNiO0FBQ0EsdUJBQXVCLDhDQUFNO0FBQzdCLFFBQVE7QUFDUix1QkFBdUIsOENBQU07QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsaUVBQWU7QUFDcEQ7QUFDQTtBQUNBLGFBQWE7QUFDYixXQUFXO0FBQ1g7QUFDQSx3Q0FBd0MsOENBQU07QUFDOUM7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQSxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSSxxRkFBZSxDQUFDLDRGQUFzQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsYUFBYSwyREFBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSwyREFBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw2RUFBMkIsQ0FBQyxvRkFBYSxDQUFDLG9GQUFhLEdBQUcsRUFBRSxrRUFBZ0IsdUNBQXVDO0FBQzNJO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGVBQWUsMkRBQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwyREFBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckMsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxlQUFlLDJEQUFPO0FBQ3RCLGVBQWUsMkRBQU87QUFDdEI7QUFDQTtBQUNBLG9DQUFvQztBQUNwQyxnQkFBZ0I7QUFDaEIscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxJQUFJLHFGQUFlLENBQUMsNEZBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EseUJBQXlCLG9GQUFhLENBQUMsb0ZBQWEsR0FBRztBQUN2RDtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUkscUZBQWUsQ0FBQyw0RkFBc0I7QUFDMUM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEVBQUUsa0ZBQVk7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixpRUFBUztBQUM5QjtBQUNBO0FBQ0EsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQSxZQUFZLDZFQUFPO0FBQ25CO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsaURBQW1CLENBQUMsdURBQVc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTyxlQUFlLGlEQUFtQjtBQUN6QztBQUNBLG1CQUFtQixrREFBVSxzQ0FBc0MscUZBQWUsQ0FBQyxxRkFBZSxDQUFDLHFGQUFlLEdBQUc7QUFDckg7QUFDQSxPQUFPLGVBQWUsaURBQW1CLENBQUMsa0RBQVEsRUFBRSw4RUFBUTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGdFQUFjO0FBQ25DO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSLFFBQVEsK0RBQU87QUFDZixtQkFBbUIsbUVBQWlCO0FBQ3BDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix1RUFBcUI7QUFDL0M7QUFDQSxTQUFTO0FBQ1QsK0JBQStCLG9GQUFhLENBQUMscUZBQWUsR0FBRyxFQUFFLGtEQUFVLEVBQUUsb0RBQVk7O0FBRXpGO0FBQ0EsWUFBWSxJQUFxQztBQUNqRCxVQUFVLG1FQUFpQjtBQUMzQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9HQUFvRywyREFBbUI7QUFDdkgsUUFBUTtBQUNSLCtCQUErQixvRkFBYSxHQUFHO0FBQy9DLGdDQUFnQyxrREFBVTs7QUFFMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxRQUFRO0FBQ1Isc0ZBQXNGLDJEQUFtQjtBQUN6RztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsMkJBQTJCLGlFQUFlO0FBQzFDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLHdEQUFnQjtBQUNsRCxVQUFVO0FBQ1Ysa0NBQWtDLHdEQUFnQjtBQUNsRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHdEQUFnQjtBQUM3QyxVQUFVO0FBQ1YsNkJBQTZCLHdEQUFnQjtBQUM3QyxVQUFVO0FBQ1Y7QUFDQSw2QkFBNkIsd0RBQWdCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsaUVBQVk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQyxDQUFDLDZDQUFlO0FBQ2pCLHFGQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHVEQUFhO0FBQ3BDO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxDQUFDO0FBQ0QscUZBQWUsbUJBQW1CLGtEQUFRO0FBQzFDLGlFQUFlLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9UcmVlLmpzPzhhMGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX3R5cGVvZiBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdHlwZW9mXCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXlcIjtcbmltcG9ydCBfY2xhc3NDYWxsQ2hlY2sgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NsYXNzQ2FsbENoZWNrXCI7XG5pbXBvcnQgX2NyZWF0ZUNsYXNzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9jcmVhdGVDbGFzc1wiO1xuaW1wb3J0IF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2Fzc2VydFRoaXNJbml0aWFsaXplZFwiO1xuaW1wb3J0IF9pbmhlcml0cyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW5oZXJpdHNcIjtcbmltcG9ydCBfY3JlYXRlU3VwZXIgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2NyZWF0ZVN1cGVyXCI7XG5pbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuLy8gVE9ETzogaHR0cHM6Ly93d3cudzMub3JnL1RSLzIwMTcvTk9URS13YWktYXJpYS1wcmFjdGljZXMtMS4xLTIwMTcxMjE0L2V4YW1wbGVzL3RyZWV2aWV3L3RyZWV2aWV3LTIvdHJlZXZpZXctMmEuaHRtbFxuLy8gRnVsbHkgYWNjZXNzaWJpbGl0eSBzdXBwb3J0XG5cbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IEtleUNvZGUgZnJvbSBcInJjLXV0aWwvZXMvS2V5Q29kZVwiO1xuaW1wb3J0IHBpY2tBdHRycyBmcm9tIFwicmMtdXRpbC9lcy9waWNrQXR0cnNcIjtcbmltcG9ydCB3YXJuaW5nIGZyb20gXCJyYy11dGlsL2VzL3dhcm5pbmdcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFRyZWVDb250ZXh0IH0gZnJvbSBcIi4vY29udGV4dFR5cGVzXCI7XG5pbXBvcnQgRHJvcEluZGljYXRvciBmcm9tIFwiLi9Ecm9wSW5kaWNhdG9yXCI7XG5pbXBvcnQgTm9kZUxpc3QsIHsgTU9USU9OX0tFWSwgTW90aW9uRW50aXR5IH0gZnJvbSBcIi4vTm9kZUxpc3RcIjtcbmltcG9ydCBUcmVlTm9kZSBmcm9tIFwiLi9UcmVlTm9kZVwiO1xuaW1wb3J0IHsgYXJyQWRkLCBhcnJEZWwsIGNhbGNEcm9wUG9zaXRpb24sIGNhbGNTZWxlY3RlZEtleXMsIGNvbmR1Y3RFeHBhbmRQYXJlbnQsIGdldERyYWdDaGlsZHJlbktleXMsIHBhcnNlQ2hlY2tlZEtleXMsIHBvc1RvQXJyIH0gZnJvbSBcIi4vdXRpbFwiO1xuaW1wb3J0IHsgY29uZHVjdENoZWNrIH0gZnJvbSBcIi4vdXRpbHMvY29uZHVjdFV0aWxcIjtcbmltcG9ydCBnZXRFbnRpdHkgZnJvbSBcIi4vdXRpbHMva2V5VXRpbFwiO1xuaW1wb3J0IHsgY29udmVydERhdGFUb0VudGl0aWVzLCBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEsIGNvbnZlcnRUcmVlVG9EYXRhLCBmaWxsRmllbGROYW1lcywgZmxhdHRlblRyZWVEYXRhLCBnZXRUcmVlTm9kZVByb3BzLCB3YXJuaW5nV2l0aG91dEtleSB9IGZyb20gXCIuL3V0aWxzL3RyZWVVdGlsXCI7XG52YXIgTUFYX1JFVFJZX1RJTUVTID0gMTA7XG52YXIgVHJlZSA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX1JlYWN0JENvbXBvbmVudCkge1xuICBfaW5oZXJpdHMoVHJlZSwgX1JlYWN0JENvbXBvbmVudCk7XG4gIHZhciBfc3VwZXIgPSBfY3JlYXRlU3VwZXIoVHJlZSk7XG4gIGZ1bmN0aW9uIFRyZWUoKSB7XG4gICAgdmFyIF90aGlzO1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBUcmVlKTtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgX2FyZ3MgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBfYXJnc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG4gICAgX3RoaXMgPSBfc3VwZXIuY2FsbC5hcHBseShfc3VwZXIsIFt0aGlzXS5jb25jYXQoX2FyZ3MpKTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwiZGVzdHJveWVkXCIsIGZhbHNlKTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwiZGVsYXllZERyYWdFbnRlckxvZ2ljXCIsIHZvaWQgMCk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcImxvYWRpbmdSZXRyeVRpbWVzXCIsIHt9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwic3RhdGVcIiwge1xuICAgICAga2V5RW50aXRpZXM6IHt9LFxuICAgICAgaW5kZW50OiBudWxsLFxuICAgICAgc2VsZWN0ZWRLZXlzOiBbXSxcbiAgICAgIGNoZWNrZWRLZXlzOiBbXSxcbiAgICAgIGhhbGZDaGVja2VkS2V5czogW10sXG4gICAgICBsb2FkZWRLZXlzOiBbXSxcbiAgICAgIGxvYWRpbmdLZXlzOiBbXSxcbiAgICAgIGV4cGFuZGVkS2V5czogW10sXG4gICAgICBkcmFnZ2luZ05vZGVLZXk6IG51bGwsXG4gICAgICBkcmFnQ2hpbGRyZW5LZXlzOiBbXSxcbiAgICAgIC8vIGRyb3BUYXJnZXRLZXkgaXMgdGhlIGtleSBvZiBhYnN0cmFjdC1kcm9wLW5vZGVcbiAgICAgIC8vIHRoZSBhYnN0cmFjdC1kcm9wLW5vZGUgaXMgdGhlIHJlYWwgZHJvcCBub2RlIHdoZW4gZHJhZyBhbmQgZHJvcFxuICAgICAgLy8gbm90IHRoZSBET00gZHJhZyBvdmVyIG5vZGVcbiAgICAgIGRyb3BUYXJnZXRLZXk6IG51bGwsXG4gICAgICBkcm9wUG9zaXRpb246IG51bGwsXG4gICAgICAvLyB0aGUgZHJvcCBwb3NpdGlvbiBvZiBhYnN0cmFjdC1kcm9wLW5vZGUsIGluc2lkZSAwLCB0b3AgLTEsIGJvdHRvbSAxXG4gICAgICBkcm9wQ29udGFpbmVyS2V5OiBudWxsLFxuICAgICAgLy8gdGhlIGNvbnRhaW5lciBrZXkgb2YgYWJzdHJhY3QtZHJvcC1ub2RlIGlmIGRyb3BQb3NpdGlvbiBpcyAtMSBvciAxXG4gICAgICBkcm9wTGV2ZWxPZmZzZXQ6IG51bGwsXG4gICAgICAvLyB0aGUgZHJvcCBsZXZlbCBvZmZzZXQgb2YgYWJzdHJhY3QtZHJhZy1vdmVyLW5vZGVcbiAgICAgIGRyb3BUYXJnZXRQb3M6IG51bGwsXG4gICAgICAvLyB0aGUgcG9zIG9mIGFic3RyYWN0LWRyb3Atbm9kZVxuICAgICAgZHJvcEFsbG93ZWQ6IHRydWUsXG4gICAgICAvLyBpZiBkcm9wIHRvIGFic3RyYWN0LWRyb3Atbm9kZSBpcyBhbGxvd2VkXG4gICAgICAvLyB0aGUgYWJzdHJhY3QtZHJhZy1vdmVyLW5vZGVcbiAgICAgIC8vIGlmIG1vdXNlIGlzIG9uIHRoZSBib3R0b20gb2YgdG9wIGRvbSBub2RlIG9yIG5vIHRoZSB0b3Agb2YgdGhlIGJvdHRvbSBkb20gbm9kZVxuICAgICAgLy8gYWJzdHJhY3QtZHJhZy1vdmVyLW5vZGUgaXMgdGhlIHRvcCBub2RlXG4gICAgICBkcmFnT3Zlck5vZGVLZXk6IG51bGwsXG4gICAgICB0cmVlRGF0YTogW10sXG4gICAgICBmbGF0dGVuTm9kZXM6IFtdLFxuICAgICAgZm9jdXNlZDogZmFsc2UsXG4gICAgICBhY3RpdmVLZXk6IG51bGwsXG4gICAgICBsaXN0Q2hhbmdpbmc6IGZhbHNlLFxuICAgICAgcHJldlByb3BzOiBudWxsLFxuICAgICAgZmllbGROYW1lczogZmlsbEZpZWxkTmFtZXMoKVxuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJkcmFnU3RhcnRNb3VzZVBvc2l0aW9uXCIsIG51bGwpO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJkcmFnTm9kZVwiLCB2b2lkIDApO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJjdXJyZW50TW91c2VPdmVyRHJvcHBhYmxlTm9kZUtleVwiLCBudWxsKTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwibGlzdFJlZlwiLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlUmVmKCkpO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVEcmFnU3RhcnRcIiwgZnVuY3Rpb24gKGV2ZW50LCBub2RlKSB7XG4gICAgICB2YXIgX3RoaXMkc3RhdGUgPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAgZXhwYW5kZWRLZXlzID0gX3RoaXMkc3RhdGUuZXhwYW5kZWRLZXlzLFxuICAgICAgICBrZXlFbnRpdGllcyA9IF90aGlzJHN0YXRlLmtleUVudGl0aWVzO1xuICAgICAgdmFyIG9uRHJhZ1N0YXJ0ID0gX3RoaXMucHJvcHMub25EcmFnU3RhcnQ7XG4gICAgICB2YXIgZXZlbnRLZXkgPSBub2RlLnByb3BzLmV2ZW50S2V5O1xuICAgICAgX3RoaXMuZHJhZ05vZGUgPSBub2RlO1xuICAgICAgX3RoaXMuZHJhZ1N0YXJ0TW91c2VQb3NpdGlvbiA9IHtcbiAgICAgICAgeDogZXZlbnQuY2xpZW50WCxcbiAgICAgICAgeTogZXZlbnQuY2xpZW50WVxuICAgICAgfTtcbiAgICAgIHZhciBuZXdFeHBhbmRlZEtleXMgPSBhcnJEZWwoZXhwYW5kZWRLZXlzLCBldmVudEtleSk7XG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGRyYWdnaW5nTm9kZUtleTogZXZlbnRLZXksXG4gICAgICAgIGRyYWdDaGlsZHJlbktleXM6IGdldERyYWdDaGlsZHJlbktleXMoZXZlbnRLZXksIGtleUVudGl0aWVzKSxcbiAgICAgICAgaW5kZW50OiBfdGhpcy5saXN0UmVmLmN1cnJlbnQuZ2V0SW5kZW50V2lkdGgoKVxuICAgICAgfSk7XG4gICAgICBfdGhpcy5zZXRFeHBhbmRlZEtleXMobmV3RXhwYW5kZWRLZXlzKTtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdkcmFnZW5kJywgX3RoaXMub25XaW5kb3dEcmFnRW5kKTtcbiAgICAgIG9uRHJhZ1N0YXJ0ID09PSBudWxsIHx8IG9uRHJhZ1N0YXJ0ID09PSB2b2lkIDAgfHwgb25EcmFnU3RhcnQoe1xuICAgICAgICBldmVudDogZXZlbnQsXG4gICAgICAgIG5vZGU6IGNvbnZlcnROb2RlUHJvcHNUb0V2ZW50RGF0YShub2RlLnByb3BzKVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgLyoqXG4gICAgICogW0xlZ2FjeV0gU2VsZWN0IGhhbmRsZXIgaXMgc21hbGxlciB0aGFuIG5vZGUsXG4gICAgICogc28gdGhhdCB0aGlzIHdpbGwgdHJpZ2dlciB3aGVuIGRyYWcgZW50ZXIgbm9kZSBvciBzZWxlY3QgaGFuZGxlci5cbiAgICAgKiBUaGlzIGlzIGEgbGl0dGxlIHRyaWNreSBpZiBjdXN0b21pemUgY3NzIHdpdGhvdXQgcGFkZGluZy5cbiAgICAgKiBCZXR0ZXIgZm9yIHVzZSBtb3VzZSBtb3ZlIGV2ZW50IHRvIHJlZnJlc2ggZHJhZyBzdGF0ZS5cbiAgICAgKiBCdXQgbGV0J3MganVzdCBrZWVwIGl0IHRvIGF2b2lkIGV2ZW50IHRyaWdnZXIgbG9naWMgY2hhbmdlLlxuICAgICAqL1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVEcmFnRW50ZXJcIiwgZnVuY3Rpb24gKGV2ZW50LCBub2RlKSB7XG4gICAgICB2YXIgX3RoaXMkc3RhdGUyID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIGV4cGFuZGVkS2V5cyA9IF90aGlzJHN0YXRlMi5leHBhbmRlZEtleXMsXG4gICAgICAgIGtleUVudGl0aWVzID0gX3RoaXMkc3RhdGUyLmtleUVudGl0aWVzLFxuICAgICAgICBkcmFnQ2hpbGRyZW5LZXlzID0gX3RoaXMkc3RhdGUyLmRyYWdDaGlsZHJlbktleXMsXG4gICAgICAgIGZsYXR0ZW5Ob2RlcyA9IF90aGlzJHN0YXRlMi5mbGF0dGVuTm9kZXMsXG4gICAgICAgIGluZGVudCA9IF90aGlzJHN0YXRlMi5pbmRlbnQ7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgb25EcmFnRW50ZXIgPSBfdGhpcyRwcm9wcy5vbkRyYWdFbnRlcixcbiAgICAgICAgb25FeHBhbmQgPSBfdGhpcyRwcm9wcy5vbkV4cGFuZCxcbiAgICAgICAgYWxsb3dEcm9wID0gX3RoaXMkcHJvcHMuYWxsb3dEcm9wLFxuICAgICAgICBkaXJlY3Rpb24gPSBfdGhpcyRwcm9wcy5kaXJlY3Rpb247XG4gICAgICB2YXIgX25vZGUkcHJvcHMgPSBub2RlLnByb3BzLFxuICAgICAgICBwb3MgPSBfbm9kZSRwcm9wcy5wb3MsXG4gICAgICAgIGV2ZW50S2V5ID0gX25vZGUkcHJvcHMuZXZlbnRLZXk7XG4gICAgICB2YXIgX2Fzc2VydFRoaXNJbml0aWFsaXplID0gX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksXG4gICAgICAgIGRyYWdOb2RlID0gX2Fzc2VydFRoaXNJbml0aWFsaXplLmRyYWdOb2RlO1xuXG4gICAgICAvLyByZWNvcmQgdGhlIGtleSBvZiBub2RlIHdoaWNoIGlzIGxhdGVzdCBlbnRlcmVkLCB1c2VkIGluIGRyYWdsZWF2ZSBldmVudC5cbiAgICAgIGlmIChfdGhpcy5jdXJyZW50TW91c2VPdmVyRHJvcHBhYmxlTm9kZUtleSAhPT0gZXZlbnRLZXkpIHtcbiAgICAgICAgX3RoaXMuY3VycmVudE1vdXNlT3ZlckRyb3BwYWJsZU5vZGVLZXkgPSBldmVudEtleTtcbiAgICAgIH1cbiAgICAgIGlmICghZHJhZ05vZGUpIHtcbiAgICAgICAgX3RoaXMucmVzZXREcmFnU3RhdGUoKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdmFyIF9jYWxjRHJvcFBvc2l0aW9uID0gY2FsY0Ryb3BQb3NpdGlvbihldmVudCwgZHJhZ05vZGUsIG5vZGUsIGluZGVudCwgX3RoaXMuZHJhZ1N0YXJ0TW91c2VQb3NpdGlvbiwgYWxsb3dEcm9wLCBmbGF0dGVuTm9kZXMsIGtleUVudGl0aWVzLCBleHBhbmRlZEtleXMsIGRpcmVjdGlvbiksXG4gICAgICAgIGRyb3BQb3NpdGlvbiA9IF9jYWxjRHJvcFBvc2l0aW9uLmRyb3BQb3NpdGlvbixcbiAgICAgICAgZHJvcExldmVsT2Zmc2V0ID0gX2NhbGNEcm9wUG9zaXRpb24uZHJvcExldmVsT2Zmc2V0LFxuICAgICAgICBkcm9wVGFyZ2V0S2V5ID0gX2NhbGNEcm9wUG9zaXRpb24uZHJvcFRhcmdldEtleSxcbiAgICAgICAgZHJvcENvbnRhaW5lcktleSA9IF9jYWxjRHJvcFBvc2l0aW9uLmRyb3BDb250YWluZXJLZXksXG4gICAgICAgIGRyb3BUYXJnZXRQb3MgPSBfY2FsY0Ryb3BQb3NpdGlvbi5kcm9wVGFyZ2V0UG9zLFxuICAgICAgICBkcm9wQWxsb3dlZCA9IF9jYWxjRHJvcFBvc2l0aW9uLmRyb3BBbGxvd2VkLFxuICAgICAgICBkcmFnT3Zlck5vZGVLZXkgPSBfY2FsY0Ryb3BQb3NpdGlvbi5kcmFnT3Zlck5vZGVLZXk7XG4gICAgICBpZiAoXG4gICAgICAvLyBkb24ndCBhbGxvdyBkcm9wIGluc2lkZSBpdHMgY2hpbGRyZW5cbiAgICAgIGRyYWdDaGlsZHJlbktleXMuaW5kZXhPZihkcm9wVGFyZ2V0S2V5KSAhPT0gLTEgfHxcbiAgICAgIC8vIGRvbid0IGFsbG93IGRyb3Agd2hlbiBkcm9wIGlzIG5vdCBhbGxvd2VkIGNhY3VsYXRlZCBieSBjYWxjRHJvcFBvc2l0aW9uXG4gICAgICAhZHJvcEFsbG93ZWQpIHtcbiAgICAgICAgX3RoaXMucmVzZXREcmFnU3RhdGUoKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBTaWRlIGVmZmVjdCBmb3IgZGVsYXkgZHJhZ1xuICAgICAgaWYgKCFfdGhpcy5kZWxheWVkRHJhZ0VudGVyTG9naWMpIHtcbiAgICAgICAgX3RoaXMuZGVsYXllZERyYWdFbnRlckxvZ2ljID0ge307XG4gICAgICB9XG4gICAgICBPYmplY3Qua2V5cyhfdGhpcy5kZWxheWVkRHJhZ0VudGVyTG9naWMpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgICAgICBjbGVhclRpbWVvdXQoX3RoaXMuZGVsYXllZERyYWdFbnRlckxvZ2ljW2tleV0pO1xuICAgICAgfSk7XG4gICAgICBpZiAoZHJhZ05vZGUucHJvcHMuZXZlbnRLZXkgIT09IG5vZGUucHJvcHMuZXZlbnRLZXkpIHtcbiAgICAgICAgLy8gaG9pc3QgZXhwYW5kIGxvZ2ljIGhlcmVcbiAgICAgICAgLy8gc2luY2UgaWYgbG9naWMgaXMgb24gdGhlIGJvdHRvbVxuICAgICAgICAvLyBpdCB3aWxsIGJlIGJsb2NrZWQgYnkgYWJzdHJhY3QgZHJhZ292ZXIgbm9kZSBjaGVja1xuICAgICAgICAvLyAgID0+IGlmIHlvdSBkcmFnZW50ZXIgZnJvbSB0b3AsIHlvdSBtb3VzZSB3aWxsIHN0aWxsIGJlIGNvbnNpZGVyIGFzIGluIHRoZSB0b3Agbm9kZVxuICAgICAgICBldmVudC5wZXJzaXN0KCk7XG4gICAgICAgIF90aGlzLmRlbGF5ZWREcmFnRW50ZXJMb2dpY1twb3NdID0gd2luZG93LnNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgICAgIGlmIChfdGhpcy5zdGF0ZS5kcmFnZ2luZ05vZGVLZXkgPT09IG51bGwpIHJldHVybjtcbiAgICAgICAgICB2YXIgbmV3RXhwYW5kZWRLZXlzID0gX3RvQ29uc3VtYWJsZUFycmF5KGV4cGFuZGVkS2V5cyk7XG4gICAgICAgICAgdmFyIGVudGl0eSA9IGdldEVudGl0eShrZXlFbnRpdGllcywgbm9kZS5wcm9wcy5ldmVudEtleSk7XG4gICAgICAgICAgaWYgKGVudGl0eSAmJiAoZW50aXR5LmNoaWxkcmVuIHx8IFtdKS5sZW5ndGgpIHtcbiAgICAgICAgICAgIG5ld0V4cGFuZGVkS2V5cyA9IGFyckFkZChleHBhbmRlZEtleXMsIG5vZGUucHJvcHMuZXZlbnRLZXkpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoIV90aGlzLnByb3BzLmhhc093blByb3BlcnR5KCdleHBhbmRlZEtleXMnKSkge1xuICAgICAgICAgICAgX3RoaXMuc2V0RXhwYW5kZWRLZXlzKG5ld0V4cGFuZGVkS2V5cyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIG9uRXhwYW5kID09PSBudWxsIHx8IG9uRXhwYW5kID09PSB2b2lkIDAgfHwgb25FeHBhbmQobmV3RXhwYW5kZWRLZXlzLCB7XG4gICAgICAgICAgICBub2RlOiBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEobm9kZS5wcm9wcyksXG4gICAgICAgICAgICBleHBhbmRlZDogdHJ1ZSxcbiAgICAgICAgICAgIG5hdGl2ZUV2ZW50OiBldmVudC5uYXRpdmVFdmVudFxuICAgICAgICAgIH0pO1xuICAgICAgICB9LCA4MDApO1xuICAgICAgfVxuXG4gICAgICAvLyBTa2lwIGlmIGRyYWcgbm9kZSBpcyBzZWxmXG4gICAgICBpZiAoZHJhZ05vZGUucHJvcHMuZXZlbnRLZXkgPT09IGRyb3BUYXJnZXRLZXkgJiYgZHJvcExldmVsT2Zmc2V0ID09PSAwKSB7XG4gICAgICAgIF90aGlzLnJlc2V0RHJhZ1N0YXRlKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIGRyYWcgb3ZlciBub2RlIGFuZCBkcmFnIHN0YXRlXG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGRyYWdPdmVyTm9kZUtleTogZHJhZ092ZXJOb2RlS2V5LFxuICAgICAgICBkcm9wUG9zaXRpb246IGRyb3BQb3NpdGlvbixcbiAgICAgICAgZHJvcExldmVsT2Zmc2V0OiBkcm9wTGV2ZWxPZmZzZXQsXG4gICAgICAgIGRyb3BUYXJnZXRLZXk6IGRyb3BUYXJnZXRLZXksXG4gICAgICAgIGRyb3BDb250YWluZXJLZXk6IGRyb3BDb250YWluZXJLZXksXG4gICAgICAgIGRyb3BUYXJnZXRQb3M6IGRyb3BUYXJnZXRQb3MsXG4gICAgICAgIGRyb3BBbGxvd2VkOiBkcm9wQWxsb3dlZFxuICAgICAgfSk7XG4gICAgICBvbkRyYWdFbnRlciA9PT0gbnVsbCB8fCBvbkRyYWdFbnRlciA9PT0gdm9pZCAwIHx8IG9uRHJhZ0VudGVyKHtcbiAgICAgICAgZXZlbnQ6IGV2ZW50LFxuICAgICAgICBub2RlOiBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEobm9kZS5wcm9wcyksXG4gICAgICAgIGV4cGFuZGVkS2V5czogZXhwYW5kZWRLZXlzXG4gICAgICB9KTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25Ob2RlRHJhZ092ZXJcIiwgZnVuY3Rpb24gKGV2ZW50LCBub2RlKSB7XG4gICAgICB2YXIgX3RoaXMkc3RhdGUzID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIGRyYWdDaGlsZHJlbktleXMgPSBfdGhpcyRzdGF0ZTMuZHJhZ0NoaWxkcmVuS2V5cyxcbiAgICAgICAgZmxhdHRlbk5vZGVzID0gX3RoaXMkc3RhdGUzLmZsYXR0ZW5Ob2RlcyxcbiAgICAgICAga2V5RW50aXRpZXMgPSBfdGhpcyRzdGF0ZTMua2V5RW50aXRpZXMsXG4gICAgICAgIGV4cGFuZGVkS2V5cyA9IF90aGlzJHN0YXRlMy5leHBhbmRlZEtleXMsXG4gICAgICAgIGluZGVudCA9IF90aGlzJHN0YXRlMy5pbmRlbnQ7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMyID0gX3RoaXMucHJvcHMsXG4gICAgICAgIG9uRHJhZ092ZXIgPSBfdGhpcyRwcm9wczIub25EcmFnT3ZlcixcbiAgICAgICAgYWxsb3dEcm9wID0gX3RoaXMkcHJvcHMyLmFsbG93RHJvcCxcbiAgICAgICAgZGlyZWN0aW9uID0gX3RoaXMkcHJvcHMyLmRpcmVjdGlvbjtcbiAgICAgIHZhciBfYXNzZXJ0VGhpc0luaXRpYWxpemUyID0gX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksXG4gICAgICAgIGRyYWdOb2RlID0gX2Fzc2VydFRoaXNJbml0aWFsaXplMi5kcmFnTm9kZTtcbiAgICAgIGlmICghZHJhZ05vZGUpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdmFyIF9jYWxjRHJvcFBvc2l0aW9uMiA9IGNhbGNEcm9wUG9zaXRpb24oZXZlbnQsIGRyYWdOb2RlLCBub2RlLCBpbmRlbnQsIF90aGlzLmRyYWdTdGFydE1vdXNlUG9zaXRpb24sIGFsbG93RHJvcCwgZmxhdHRlbk5vZGVzLCBrZXlFbnRpdGllcywgZXhwYW5kZWRLZXlzLCBkaXJlY3Rpb24pLFxuICAgICAgICBkcm9wUG9zaXRpb24gPSBfY2FsY0Ryb3BQb3NpdGlvbjIuZHJvcFBvc2l0aW9uLFxuICAgICAgICBkcm9wTGV2ZWxPZmZzZXQgPSBfY2FsY0Ryb3BQb3NpdGlvbjIuZHJvcExldmVsT2Zmc2V0LFxuICAgICAgICBkcm9wVGFyZ2V0S2V5ID0gX2NhbGNEcm9wUG9zaXRpb24yLmRyb3BUYXJnZXRLZXksXG4gICAgICAgIGRyb3BDb250YWluZXJLZXkgPSBfY2FsY0Ryb3BQb3NpdGlvbjIuZHJvcENvbnRhaW5lcktleSxcbiAgICAgICAgZHJvcEFsbG93ZWQgPSBfY2FsY0Ryb3BQb3NpdGlvbjIuZHJvcEFsbG93ZWQsXG4gICAgICAgIGRyb3BUYXJnZXRQb3MgPSBfY2FsY0Ryb3BQb3NpdGlvbjIuZHJvcFRhcmdldFBvcyxcbiAgICAgICAgZHJhZ092ZXJOb2RlS2V5ID0gX2NhbGNEcm9wUG9zaXRpb24yLmRyYWdPdmVyTm9kZUtleTtcbiAgICAgIGlmIChkcmFnQ2hpbGRyZW5LZXlzLmluZGV4T2YoZHJvcFRhcmdldEtleSkgIT09IC0xIHx8ICFkcm9wQWxsb3dlZCkge1xuICAgICAgICAvLyBkb24ndCBhbGxvdyBkcm9wIGluc2lkZSBpdHMgY2hpbGRyZW5cbiAgICAgICAgLy8gZG9uJ3QgYWxsb3cgZHJvcCB3aGVuIGRyb3AgaXMgbm90IGFsbG93ZWQgY2FsY3VsYXRlZCBieSBjYWxjRHJvcFBvc2l0aW9uXG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIGRyYWcgcG9zaXRpb25cblxuICAgICAgaWYgKGRyYWdOb2RlLnByb3BzLmV2ZW50S2V5ID09PSBkcm9wVGFyZ2V0S2V5ICYmIGRyb3BMZXZlbE9mZnNldCA9PT0gMCkge1xuICAgICAgICBpZiAoIShfdGhpcy5zdGF0ZS5kcm9wUG9zaXRpb24gPT09IG51bGwgJiYgX3RoaXMuc3RhdGUuZHJvcExldmVsT2Zmc2V0ID09PSBudWxsICYmIF90aGlzLnN0YXRlLmRyb3BUYXJnZXRLZXkgPT09IG51bGwgJiYgX3RoaXMuc3RhdGUuZHJvcENvbnRhaW5lcktleSA9PT0gbnVsbCAmJiBfdGhpcy5zdGF0ZS5kcm9wVGFyZ2V0UG9zID09PSBudWxsICYmIF90aGlzLnN0YXRlLmRyb3BBbGxvd2VkID09PSBmYWxzZSAmJiBfdGhpcy5zdGF0ZS5kcmFnT3Zlck5vZGVLZXkgPT09IG51bGwpKSB7XG4gICAgICAgICAgX3RoaXMucmVzZXREcmFnU3RhdGUoKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICghKGRyb3BQb3NpdGlvbiA9PT0gX3RoaXMuc3RhdGUuZHJvcFBvc2l0aW9uICYmIGRyb3BMZXZlbE9mZnNldCA9PT0gX3RoaXMuc3RhdGUuZHJvcExldmVsT2Zmc2V0ICYmIGRyb3BUYXJnZXRLZXkgPT09IF90aGlzLnN0YXRlLmRyb3BUYXJnZXRLZXkgJiYgZHJvcENvbnRhaW5lcktleSA9PT0gX3RoaXMuc3RhdGUuZHJvcENvbnRhaW5lcktleSAmJiBkcm9wVGFyZ2V0UG9zID09PSBfdGhpcy5zdGF0ZS5kcm9wVGFyZ2V0UG9zICYmIGRyb3BBbGxvd2VkID09PSBfdGhpcy5zdGF0ZS5kcm9wQWxsb3dlZCAmJiBkcmFnT3Zlck5vZGVLZXkgPT09IF90aGlzLnN0YXRlLmRyYWdPdmVyTm9kZUtleSkpIHtcbiAgICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICAgIGRyb3BQb3NpdGlvbjogZHJvcFBvc2l0aW9uLFxuICAgICAgICAgIGRyb3BMZXZlbE9mZnNldDogZHJvcExldmVsT2Zmc2V0LFxuICAgICAgICAgIGRyb3BUYXJnZXRLZXk6IGRyb3BUYXJnZXRLZXksXG4gICAgICAgICAgZHJvcENvbnRhaW5lcktleTogZHJvcENvbnRhaW5lcktleSxcbiAgICAgICAgICBkcm9wVGFyZ2V0UG9zOiBkcm9wVGFyZ2V0UG9zLFxuICAgICAgICAgIGRyb3BBbGxvd2VkOiBkcm9wQWxsb3dlZCxcbiAgICAgICAgICBkcmFnT3Zlck5vZGVLZXk6IGRyYWdPdmVyTm9kZUtleVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIG9uRHJhZ092ZXIgPT09IG51bGwgfHwgb25EcmFnT3ZlciA9PT0gdm9pZCAwIHx8IG9uRHJhZ092ZXIoe1xuICAgICAgICBldmVudDogZXZlbnQsXG4gICAgICAgIG5vZGU6IGNvbnZlcnROb2RlUHJvcHNUb0V2ZW50RGF0YShub2RlLnByb3BzKVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uTm9kZURyYWdMZWF2ZVwiLCBmdW5jdGlvbiAoZXZlbnQsIG5vZGUpIHtcbiAgICAgIC8vIGlmIGl0IGlzIG91dHNpZGUgdGhlIGRyb3BwYWJsZSBhcmVhXG4gICAgICAvLyBjdXJyZW50TW91c2VPdmVyRHJvcHBhYmxlTm9kZUtleSB3aWxsIGJlIHVwZGF0ZWQgaW4gZHJhZ2VudGVyIGV2ZW50IHdoZW4gaW50byBhbm90aGVyIGRyb3BwYWJsZSByZWNlaXZlci5cbiAgICAgIGlmIChfdGhpcy5jdXJyZW50TW91c2VPdmVyRHJvcHBhYmxlTm9kZUtleSA9PT0gbm9kZS5wcm9wcy5ldmVudEtleSAmJiAhZXZlbnQuY3VycmVudFRhcmdldC5jb250YWlucyhldmVudC5yZWxhdGVkVGFyZ2V0KSkge1xuICAgICAgICBfdGhpcy5yZXNldERyYWdTdGF0ZSgpO1xuICAgICAgICBfdGhpcy5jdXJyZW50TW91c2VPdmVyRHJvcHBhYmxlTm9kZUtleSA9IG51bGw7XG4gICAgICB9XG4gICAgICB2YXIgb25EcmFnTGVhdmUgPSBfdGhpcy5wcm9wcy5vbkRyYWdMZWF2ZTtcbiAgICAgIG9uRHJhZ0xlYXZlID09PSBudWxsIHx8IG9uRHJhZ0xlYXZlID09PSB2b2lkIDAgfHwgb25EcmFnTGVhdmUoe1xuICAgICAgICBldmVudDogZXZlbnQsXG4gICAgICAgIG5vZGU6IGNvbnZlcnROb2RlUHJvcHNUb0V2ZW50RGF0YShub2RlLnByb3BzKVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgLy8gc2luY2Ugc3RvcFByb3BhZ2F0aW9uKCkgaXMgY2FsbGVkIGluIHRyZWVOb2RlXG4gICAgLy8gaWYgb25XaW5kb3dEcmFnIGlzIGNhbGxlZCwgd2hpY2UgbWVhbnMgc3RhdGUgaXMga2VlcGVkLCBkcmFnIHN0YXRlIHNob3VsZCBiZSBjbGVhcmVkXG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uV2luZG93RHJhZ0VuZFwiLCBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIF90aGlzLm9uTm9kZURyYWdFbmQoZXZlbnQsIG51bGwsIHRydWUpO1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdlbmQnLCBfdGhpcy5vbldpbmRvd0RyYWdFbmQpO1xuICAgIH0pO1xuICAgIC8vIGlmIG9uTm9kZURyYWdFbmQgaXMgY2FsbGVkLCBvbldpbmRvd0RyYWdFbmQgd29uJ3QgYmUgY2FsbGVkIHNpbmNlIHN0b3BQcm9wYWdhdGlvbigpIGlzIGNhbGxlZFxuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVEcmFnRW5kXCIsIGZ1bmN0aW9uIChldmVudCwgbm9kZSkge1xuICAgICAgdmFyIG9uRHJhZ0VuZCA9IF90aGlzLnByb3BzLm9uRHJhZ0VuZDtcbiAgICAgIF90aGlzLnNldFN0YXRlKHtcbiAgICAgICAgZHJhZ092ZXJOb2RlS2V5OiBudWxsXG4gICAgICB9KTtcbiAgICAgIF90aGlzLmNsZWFuRHJhZ1N0YXRlKCk7XG4gICAgICBvbkRyYWdFbmQgPT09IG51bGwgfHwgb25EcmFnRW5kID09PSB2b2lkIDAgfHwgb25EcmFnRW5kKHtcbiAgICAgICAgZXZlbnQ6IGV2ZW50LFxuICAgICAgICBub2RlOiBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEobm9kZS5wcm9wcylcbiAgICAgIH0pO1xuICAgICAgX3RoaXMuZHJhZ05vZGUgPSBudWxsO1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdlbmQnLCBfdGhpcy5vbldpbmRvd0RyYWdFbmQpO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVEcm9wXCIsIGZ1bmN0aW9uIChldmVudCwgbm9kZSkge1xuICAgICAgdmFyIF90aGlzJGdldEFjdGl2ZUl0ZW07XG4gICAgICB2YXIgb3V0c2lkZVRyZWUgPSBhcmd1bWVudHMubGVuZ3RoID4gMiAmJiBhcmd1bWVudHNbMl0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1syXSA6IGZhbHNlO1xuICAgICAgdmFyIF90aGlzJHN0YXRlNCA9IF90aGlzLnN0YXRlLFxuICAgICAgICBkcmFnQ2hpbGRyZW5LZXlzID0gX3RoaXMkc3RhdGU0LmRyYWdDaGlsZHJlbktleXMsXG4gICAgICAgIGRyb3BQb3NpdGlvbiA9IF90aGlzJHN0YXRlNC5kcm9wUG9zaXRpb24sXG4gICAgICAgIGRyb3BUYXJnZXRLZXkgPSBfdGhpcyRzdGF0ZTQuZHJvcFRhcmdldEtleSxcbiAgICAgICAgZHJvcFRhcmdldFBvcyA9IF90aGlzJHN0YXRlNC5kcm9wVGFyZ2V0UG9zLFxuICAgICAgICBkcm9wQWxsb3dlZCA9IF90aGlzJHN0YXRlNC5kcm9wQWxsb3dlZDtcbiAgICAgIGlmICghZHJvcEFsbG93ZWQpIHJldHVybjtcbiAgICAgIHZhciBvbkRyb3AgPSBfdGhpcy5wcm9wcy5vbkRyb3A7XG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGRyYWdPdmVyTm9kZUtleTogbnVsbFxuICAgICAgfSk7XG4gICAgICBfdGhpcy5jbGVhbkRyYWdTdGF0ZSgpO1xuICAgICAgaWYgKGRyb3BUYXJnZXRLZXkgPT09IG51bGwpIHJldHVybjtcbiAgICAgIHZhciBhYnN0cmFjdERyb3BOb2RlUHJvcHMgPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGdldFRyZWVOb2RlUHJvcHMoZHJvcFRhcmdldEtleSwgX3RoaXMuZ2V0VHJlZU5vZGVSZXF1aXJlZFByb3BzKCkpKSwge30sIHtcbiAgICAgICAgYWN0aXZlOiAoKF90aGlzJGdldEFjdGl2ZUl0ZW0gPSBfdGhpcy5nZXRBY3RpdmVJdGVtKCkpID09PSBudWxsIHx8IF90aGlzJGdldEFjdGl2ZUl0ZW0gPT09IHZvaWQgMCA/IHZvaWQgMCA6IF90aGlzJGdldEFjdGl2ZUl0ZW0ua2V5KSA9PT0gZHJvcFRhcmdldEtleSxcbiAgICAgICAgZGF0YTogZ2V0RW50aXR5KF90aGlzLnN0YXRlLmtleUVudGl0aWVzLCBkcm9wVGFyZ2V0S2V5KS5ub2RlXG4gICAgICB9KTtcbiAgICAgIHZhciBkcm9wVG9DaGlsZCA9IGRyYWdDaGlsZHJlbktleXMuaW5kZXhPZihkcm9wVGFyZ2V0S2V5KSAhPT0gLTE7XG4gICAgICB3YXJuaW5nKCFkcm9wVG9DaGlsZCwgXCJDYW4gbm90IGRyb3AgdG8gZHJhZ05vZGUncyBjaGlsZHJlbiBub2RlLiBUaGlzIGlzIGEgYnVnIG9mIHJjLXRyZWUuIFBsZWFzZSByZXBvcnQgYW4gaXNzdWUuXCIpO1xuICAgICAgdmFyIHBvc0FyciA9IHBvc1RvQXJyKGRyb3BUYXJnZXRQb3MpO1xuICAgICAgdmFyIGRyb3BSZXN1bHQgPSB7XG4gICAgICAgIGV2ZW50OiBldmVudCxcbiAgICAgICAgbm9kZTogY29udmVydE5vZGVQcm9wc1RvRXZlbnREYXRhKGFic3RyYWN0RHJvcE5vZGVQcm9wcyksXG4gICAgICAgIGRyYWdOb2RlOiBfdGhpcy5kcmFnTm9kZSA/IGNvbnZlcnROb2RlUHJvcHNUb0V2ZW50RGF0YShfdGhpcy5kcmFnTm9kZS5wcm9wcykgOiBudWxsLFxuICAgICAgICBkcmFnTm9kZXNLZXlzOiBbX3RoaXMuZHJhZ05vZGUucHJvcHMuZXZlbnRLZXldLmNvbmNhdChkcmFnQ2hpbGRyZW5LZXlzKSxcbiAgICAgICAgZHJvcFRvR2FwOiBkcm9wUG9zaXRpb24gIT09IDAsXG4gICAgICAgIGRyb3BQb3NpdGlvbjogZHJvcFBvc2l0aW9uICsgTnVtYmVyKHBvc0Fycltwb3NBcnIubGVuZ3RoIC0gMV0pXG4gICAgICB9O1xuICAgICAgaWYgKCFvdXRzaWRlVHJlZSkge1xuICAgICAgICBvbkRyb3AgPT09IG51bGwgfHwgb25Ecm9wID09PSB2b2lkIDAgfHwgb25Ecm9wKGRyb3BSZXN1bHQpO1xuICAgICAgfVxuICAgICAgX3RoaXMuZHJhZ05vZGUgPSBudWxsO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJjbGVhbkRyYWdTdGF0ZVwiLCBmdW5jdGlvbiAoKSB7XG4gICAgICB2YXIgZHJhZ2dpbmdOb2RlS2V5ID0gX3RoaXMuc3RhdGUuZHJhZ2dpbmdOb2RlS2V5O1xuICAgICAgaWYgKGRyYWdnaW5nTm9kZUtleSAhPT0gbnVsbCkge1xuICAgICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgICAgZHJhZ2dpbmdOb2RlS2V5OiBudWxsLFxuICAgICAgICAgIGRyb3BQb3NpdGlvbjogbnVsbCxcbiAgICAgICAgICBkcm9wQ29udGFpbmVyS2V5OiBudWxsLFxuICAgICAgICAgIGRyb3BUYXJnZXRLZXk6IG51bGwsXG4gICAgICAgICAgZHJvcExldmVsT2Zmc2V0OiBudWxsLFxuICAgICAgICAgIGRyb3BBbGxvd2VkOiB0cnVlLFxuICAgICAgICAgIGRyYWdPdmVyTm9kZUtleTogbnVsbFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIF90aGlzLmRyYWdTdGFydE1vdXNlUG9zaXRpb24gPSBudWxsO1xuICAgICAgX3RoaXMuY3VycmVudE1vdXNlT3ZlckRyb3BwYWJsZU5vZGVLZXkgPSBudWxsO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJ0cmlnZ2VyRXhwYW5kQWN0aW9uRXhwYW5kXCIsIGZ1bmN0aW9uIChlLCB0cmVlTm9kZSkge1xuICAgICAgdmFyIF90aGlzJHN0YXRlNSA9IF90aGlzLnN0YXRlLFxuICAgICAgICBleHBhbmRlZEtleXMgPSBfdGhpcyRzdGF0ZTUuZXhwYW5kZWRLZXlzLFxuICAgICAgICBmbGF0dGVuTm9kZXMgPSBfdGhpcyRzdGF0ZTUuZmxhdHRlbk5vZGVzO1xuICAgICAgdmFyIGV4cGFuZGVkID0gdHJlZU5vZGUuZXhwYW5kZWQsXG4gICAgICAgIGtleSA9IHRyZWVOb2RlLmtleSxcbiAgICAgICAgaXNMZWFmID0gdHJlZU5vZGUuaXNMZWFmO1xuICAgICAgaWYgKGlzTGVhZiB8fCBlLnNoaWZ0S2V5IHx8IGUubWV0YUtleSB8fCBlLmN0cmxLZXkpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdmFyIG5vZGUgPSBmbGF0dGVuTm9kZXMuZmlsdGVyKGZ1bmN0aW9uIChub2RlSXRlbSkge1xuICAgICAgICByZXR1cm4gbm9kZUl0ZW0ua2V5ID09PSBrZXk7XG4gICAgICB9KVswXTtcbiAgICAgIHZhciBldmVudE5vZGUgPSBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBnZXRUcmVlTm9kZVByb3BzKGtleSwgX3RoaXMuZ2V0VHJlZU5vZGVSZXF1aXJlZFByb3BzKCkpKSwge30sIHtcbiAgICAgICAgZGF0YTogbm9kZS5kYXRhXG4gICAgICB9KSk7XG4gICAgICBfdGhpcy5zZXRFeHBhbmRlZEtleXMoZXhwYW5kZWQgPyBhcnJEZWwoZXhwYW5kZWRLZXlzLCBrZXkpIDogYXJyQWRkKGV4cGFuZGVkS2V5cywga2V5KSk7XG4gICAgICBfdGhpcy5vbk5vZGVFeHBhbmQoZSwgZXZlbnROb2RlKTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25Ob2RlQ2xpY2tcIiwgZnVuY3Rpb24gKGUsIHRyZWVOb2RlKSB7XG4gICAgICB2YXIgX3RoaXMkcHJvcHMzID0gX3RoaXMucHJvcHMsXG4gICAgICAgIG9uQ2xpY2sgPSBfdGhpcyRwcm9wczMub25DbGljayxcbiAgICAgICAgZXhwYW5kQWN0aW9uID0gX3RoaXMkcHJvcHMzLmV4cGFuZEFjdGlvbjtcbiAgICAgIGlmIChleHBhbmRBY3Rpb24gPT09ICdjbGljaycpIHtcbiAgICAgICAgX3RoaXMudHJpZ2dlckV4cGFuZEFjdGlvbkV4cGFuZChlLCB0cmVlTm9kZSk7XG4gICAgICB9XG4gICAgICBvbkNsaWNrID09PSBudWxsIHx8IG9uQ2xpY2sgPT09IHZvaWQgMCB8fCBvbkNsaWNrKGUsIHRyZWVOb2RlKTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25Ob2RlRG91YmxlQ2xpY2tcIiwgZnVuY3Rpb24gKGUsIHRyZWVOb2RlKSB7XG4gICAgICB2YXIgX3RoaXMkcHJvcHM0ID0gX3RoaXMucHJvcHMsXG4gICAgICAgIG9uRG91YmxlQ2xpY2sgPSBfdGhpcyRwcm9wczQub25Eb3VibGVDbGljayxcbiAgICAgICAgZXhwYW5kQWN0aW9uID0gX3RoaXMkcHJvcHM0LmV4cGFuZEFjdGlvbjtcbiAgICAgIGlmIChleHBhbmRBY3Rpb24gPT09ICdkb3VibGVDbGljaycpIHtcbiAgICAgICAgX3RoaXMudHJpZ2dlckV4cGFuZEFjdGlvbkV4cGFuZChlLCB0cmVlTm9kZSk7XG4gICAgICB9XG4gICAgICBvbkRvdWJsZUNsaWNrID09PSBudWxsIHx8IG9uRG91YmxlQ2xpY2sgPT09IHZvaWQgMCB8fCBvbkRvdWJsZUNsaWNrKGUsIHRyZWVOb2RlKTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25Ob2RlU2VsZWN0XCIsIGZ1bmN0aW9uIChlLCB0cmVlTm9kZSkge1xuICAgICAgdmFyIHNlbGVjdGVkS2V5cyA9IF90aGlzLnN0YXRlLnNlbGVjdGVkS2V5cztcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTYgPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAga2V5RW50aXRpZXMgPSBfdGhpcyRzdGF0ZTYua2V5RW50aXRpZXMsXG4gICAgICAgIGZpZWxkTmFtZXMgPSBfdGhpcyRzdGF0ZTYuZmllbGROYW1lcztcbiAgICAgIHZhciBfdGhpcyRwcm9wczUgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgb25TZWxlY3QgPSBfdGhpcyRwcm9wczUub25TZWxlY3QsXG4gICAgICAgIG11bHRpcGxlID0gX3RoaXMkcHJvcHM1Lm11bHRpcGxlO1xuICAgICAgdmFyIHNlbGVjdGVkID0gdHJlZU5vZGUuc2VsZWN0ZWQ7XG4gICAgICB2YXIga2V5ID0gdHJlZU5vZGVbZmllbGROYW1lcy5rZXldO1xuICAgICAgdmFyIHRhcmdldFNlbGVjdGVkID0gIXNlbGVjdGVkO1xuXG4gICAgICAvLyBVcGRhdGUgc2VsZWN0ZWQga2V5c1xuICAgICAgaWYgKCF0YXJnZXRTZWxlY3RlZCkge1xuICAgICAgICBzZWxlY3RlZEtleXMgPSBhcnJEZWwoc2VsZWN0ZWRLZXlzLCBrZXkpO1xuICAgICAgfSBlbHNlIGlmICghbXVsdGlwbGUpIHtcbiAgICAgICAgc2VsZWN0ZWRLZXlzID0gW2tleV07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZWxlY3RlZEtleXMgPSBhcnJBZGQoc2VsZWN0ZWRLZXlzLCBrZXkpO1xuICAgICAgfVxuXG4gICAgICAvLyBbTGVnYWN5XSBOb3QgZm91bmQgcmVsYXRlZCB1c2FnZSBpbiBkb2Mgb3IgdXBwZXIgbGlic1xuICAgICAgdmFyIHNlbGVjdGVkTm9kZXMgPSBzZWxlY3RlZEtleXMubWFwKGZ1bmN0aW9uIChzZWxlY3RlZEtleSkge1xuICAgICAgICB2YXIgZW50aXR5ID0gZ2V0RW50aXR5KGtleUVudGl0aWVzLCBzZWxlY3RlZEtleSk7XG4gICAgICAgIGlmICghZW50aXR5KSByZXR1cm4gbnVsbDtcbiAgICAgICAgcmV0dXJuIGVudGl0eS5ub2RlO1xuICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uIChub2RlKSB7XG4gICAgICAgIHJldHVybiBub2RlO1xuICAgICAgfSk7XG4gICAgICBfdGhpcy5zZXRVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gICAgICAgIHNlbGVjdGVkS2V5czogc2VsZWN0ZWRLZXlzXG4gICAgICB9KTtcbiAgICAgIG9uU2VsZWN0ID09PSBudWxsIHx8IG9uU2VsZWN0ID09PSB2b2lkIDAgfHwgb25TZWxlY3Qoc2VsZWN0ZWRLZXlzLCB7XG4gICAgICAgIGV2ZW50OiAnc2VsZWN0JyxcbiAgICAgICAgc2VsZWN0ZWQ6IHRhcmdldFNlbGVjdGVkLFxuICAgICAgICBub2RlOiB0cmVlTm9kZSxcbiAgICAgICAgc2VsZWN0ZWROb2Rlczogc2VsZWN0ZWROb2RlcyxcbiAgICAgICAgbmF0aXZlRXZlbnQ6IGUubmF0aXZlRXZlbnRcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVDaGVja1wiLCBmdW5jdGlvbiAoZSwgdHJlZU5vZGUsIGNoZWNrZWQpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTcgPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAga2V5RW50aXRpZXMgPSBfdGhpcyRzdGF0ZTcua2V5RW50aXRpZXMsXG4gICAgICAgIG9yaUNoZWNrZWRLZXlzID0gX3RoaXMkc3RhdGU3LmNoZWNrZWRLZXlzLFxuICAgICAgICBvcmlIYWxmQ2hlY2tlZEtleXMgPSBfdGhpcyRzdGF0ZTcuaGFsZkNoZWNrZWRLZXlzO1xuICAgICAgdmFyIF90aGlzJHByb3BzNiA9IF90aGlzLnByb3BzLFxuICAgICAgICBjaGVja1N0cmljdGx5ID0gX3RoaXMkcHJvcHM2LmNoZWNrU3RyaWN0bHksXG4gICAgICAgIG9uQ2hlY2sgPSBfdGhpcyRwcm9wczYub25DaGVjaztcbiAgICAgIHZhciBrZXkgPSB0cmVlTm9kZS5rZXk7XG5cbiAgICAgIC8vIFByZXBhcmUgdHJpZ2dlciBhcmd1bWVudHNcbiAgICAgIHZhciBjaGVja2VkT2JqO1xuICAgICAgdmFyIGV2ZW50T2JqID0ge1xuICAgICAgICBldmVudDogJ2NoZWNrJyxcbiAgICAgICAgbm9kZTogdHJlZU5vZGUsXG4gICAgICAgIGNoZWNrZWQ6IGNoZWNrZWQsXG4gICAgICAgIG5hdGl2ZUV2ZW50OiBlLm5hdGl2ZUV2ZW50XG4gICAgICB9O1xuICAgICAgaWYgKGNoZWNrU3RyaWN0bHkpIHtcbiAgICAgICAgdmFyIGNoZWNrZWRLZXlzID0gY2hlY2tlZCA/IGFyckFkZChvcmlDaGVja2VkS2V5cywga2V5KSA6IGFyckRlbChvcmlDaGVja2VkS2V5cywga2V5KTtcbiAgICAgICAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IGFyckRlbChvcmlIYWxmQ2hlY2tlZEtleXMsIGtleSk7XG4gICAgICAgIGNoZWNrZWRPYmogPSB7XG4gICAgICAgICAgY2hlY2tlZDogY2hlY2tlZEtleXMsXG4gICAgICAgICAgaGFsZkNoZWNrZWQ6IGhhbGZDaGVja2VkS2V5c1xuICAgICAgICB9O1xuICAgICAgICBldmVudE9iai5jaGVja2VkTm9kZXMgPSBjaGVja2VkS2V5cy5tYXAoZnVuY3Rpb24gKGNoZWNrZWRLZXkpIHtcbiAgICAgICAgICByZXR1cm4gZ2V0RW50aXR5KGtleUVudGl0aWVzLCBjaGVja2VkS2V5KTtcbiAgICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uIChlbnRpdHkpIHtcbiAgICAgICAgICByZXR1cm4gZW50aXR5O1xuICAgICAgICB9KS5tYXAoZnVuY3Rpb24gKGVudGl0eSkge1xuICAgICAgICAgIHJldHVybiBlbnRpdHkubm9kZTtcbiAgICAgICAgfSk7XG4gICAgICAgIF90aGlzLnNldFVuY29udHJvbGxlZFN0YXRlKHtcbiAgICAgICAgICBjaGVja2VkS2V5czogY2hlY2tlZEtleXNcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBBbHdheXMgZmlsbCBmaXJzdFxuICAgICAgICB2YXIgX2NvbmR1Y3RDaGVjayA9IGNvbmR1Y3RDaGVjayhbXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KG9yaUNoZWNrZWRLZXlzKSwgW2tleV0pLCB0cnVlLCBrZXlFbnRpdGllcyksXG4gICAgICAgICAgX2NoZWNrZWRLZXlzID0gX2NvbmR1Y3RDaGVjay5jaGVja2VkS2V5cyxcbiAgICAgICAgICBfaGFsZkNoZWNrZWRLZXlzID0gX2NvbmR1Y3RDaGVjay5oYWxmQ2hlY2tlZEtleXM7XG5cbiAgICAgICAgLy8gSWYgcmVtb3ZlLCB3ZSBkbyBpdCBhZ2FpbiB0byBjb3JyZWN0aW9uXG4gICAgICAgIGlmICghY2hlY2tlZCkge1xuICAgICAgICAgIHZhciBrZXlTZXQgPSBuZXcgU2V0KF9jaGVja2VkS2V5cyk7XG4gICAgICAgICAga2V5U2V0LmRlbGV0ZShrZXkpO1xuICAgICAgICAgIHZhciBfY29uZHVjdENoZWNrMiA9IGNvbmR1Y3RDaGVjayhBcnJheS5mcm9tKGtleVNldCksIHtcbiAgICAgICAgICAgIGNoZWNrZWQ6IGZhbHNlLFxuICAgICAgICAgICAgaGFsZkNoZWNrZWRLZXlzOiBfaGFsZkNoZWNrZWRLZXlzXG4gICAgICAgICAgfSwga2V5RW50aXRpZXMpO1xuICAgICAgICAgIF9jaGVja2VkS2V5cyA9IF9jb25kdWN0Q2hlY2syLmNoZWNrZWRLZXlzO1xuICAgICAgICAgIF9oYWxmQ2hlY2tlZEtleXMgPSBfY29uZHVjdENoZWNrMi5oYWxmQ2hlY2tlZEtleXM7XG4gICAgICAgIH1cbiAgICAgICAgY2hlY2tlZE9iaiA9IF9jaGVja2VkS2V5cztcblxuICAgICAgICAvLyBbTGVnYWN5XSBUaGlzIGlzIHVzZWQgZm9yIGByYy10cmVlLXNlbGVjdGBcbiAgICAgICAgZXZlbnRPYmouY2hlY2tlZE5vZGVzID0gW107XG4gICAgICAgIGV2ZW50T2JqLmNoZWNrZWROb2Rlc1Bvc2l0aW9ucyA9IFtdO1xuICAgICAgICBldmVudE9iai5oYWxmQ2hlY2tlZEtleXMgPSBfaGFsZkNoZWNrZWRLZXlzO1xuICAgICAgICBfY2hlY2tlZEtleXMuZm9yRWFjaChmdW5jdGlvbiAoY2hlY2tlZEtleSkge1xuICAgICAgICAgIHZhciBlbnRpdHkgPSBnZXRFbnRpdHkoa2V5RW50aXRpZXMsIGNoZWNrZWRLZXkpO1xuICAgICAgICAgIGlmICghZW50aXR5KSByZXR1cm47XG4gICAgICAgICAgdmFyIG5vZGUgPSBlbnRpdHkubm9kZSxcbiAgICAgICAgICAgIHBvcyA9IGVudGl0eS5wb3M7XG4gICAgICAgICAgZXZlbnRPYmouY2hlY2tlZE5vZGVzLnB1c2gobm9kZSk7XG4gICAgICAgICAgZXZlbnRPYmouY2hlY2tlZE5vZGVzUG9zaXRpb25zLnB1c2goe1xuICAgICAgICAgICAgbm9kZTogbm9kZSxcbiAgICAgICAgICAgIHBvczogcG9zXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgICBfdGhpcy5zZXRVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gICAgICAgICAgY2hlY2tlZEtleXM6IF9jaGVja2VkS2V5c1xuICAgICAgICB9LCBmYWxzZSwge1xuICAgICAgICAgIGhhbGZDaGVja2VkS2V5czogX2hhbGZDaGVja2VkS2V5c1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIG9uQ2hlY2sgPT09IG51bGwgfHwgb25DaGVjayA9PT0gdm9pZCAwIHx8IG9uQ2hlY2soY2hlY2tlZE9iaiwgZXZlbnRPYmopO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVMb2FkXCIsIGZ1bmN0aW9uICh0cmVlTm9kZSkge1xuICAgICAgdmFyIF9lbnRpdHkkY2hpbGRyZW47XG4gICAgICB2YXIga2V5ID0gdHJlZU5vZGUua2V5O1xuICAgICAgdmFyIGtleUVudGl0aWVzID0gX3RoaXMuc3RhdGUua2V5RW50aXRpZXM7XG5cbiAgICAgIC8vIFNraXAgaWYgaGFzIGNoaWxkcmVuIGFscmVhZHlcbiAgICAgIHZhciBlbnRpdHkgPSBnZXRFbnRpdHkoa2V5RW50aXRpZXMsIGtleSk7XG4gICAgICBpZiAoZW50aXR5ICE9PSBudWxsICYmIGVudGl0eSAhPT0gdm9pZCAwICYmIChfZW50aXR5JGNoaWxkcmVuID0gZW50aXR5LmNoaWxkcmVuKSAhPT0gbnVsbCAmJiBfZW50aXR5JGNoaWxkcmVuICE9PSB2b2lkIDAgJiYgX2VudGl0eSRjaGlsZHJlbi5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgdmFyIGxvYWRQcm9taXNlID0gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICAvLyBXZSBuZWVkIHRvIGdldCB0aGUgbGF0ZXN0IHN0YXRlIG9mIGxvYWRpbmcvbG9hZGVkIGtleXNcbiAgICAgICAgX3RoaXMuc2V0U3RhdGUoZnVuY3Rpb24gKF9yZWYpIHtcbiAgICAgICAgICB2YXIgX3JlZiRsb2FkZWRLZXlzID0gX3JlZi5sb2FkZWRLZXlzLFxuICAgICAgICAgICAgbG9hZGVkS2V5cyA9IF9yZWYkbG9hZGVkS2V5cyA9PT0gdm9pZCAwID8gW10gOiBfcmVmJGxvYWRlZEtleXMsXG4gICAgICAgICAgICBfcmVmJGxvYWRpbmdLZXlzID0gX3JlZi5sb2FkaW5nS2V5cyxcbiAgICAgICAgICAgIGxvYWRpbmdLZXlzID0gX3JlZiRsb2FkaW5nS2V5cyA9PT0gdm9pZCAwID8gW10gOiBfcmVmJGxvYWRpbmdLZXlzO1xuICAgICAgICAgIHZhciBfdGhpcyRwcm9wczcgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgICAgIGxvYWREYXRhID0gX3RoaXMkcHJvcHM3LmxvYWREYXRhLFxuICAgICAgICAgICAgb25Mb2FkID0gX3RoaXMkcHJvcHM3Lm9uTG9hZDtcbiAgICAgICAgICBpZiAoIWxvYWREYXRhIHx8IGxvYWRlZEtleXMuaW5kZXhPZihrZXkpICE9PSAtMSB8fCBsb2FkaW5nS2V5cy5pbmRleE9mKGtleSkgIT09IC0xKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBQcm9jZXNzIGxvYWQgZGF0YVxuICAgICAgICAgIHZhciBwcm9taXNlID0gbG9hZERhdGEodHJlZU5vZGUpO1xuICAgICAgICAgIHByb21pc2UudGhlbihmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICB2YXIgY3VycmVudExvYWRlZEtleXMgPSBfdGhpcy5zdGF0ZS5sb2FkZWRLZXlzO1xuICAgICAgICAgICAgdmFyIG5ld0xvYWRlZEtleXMgPSBhcnJBZGQoY3VycmVudExvYWRlZEtleXMsIGtleSk7XG5cbiAgICAgICAgICAgIC8vIG9uTG9hZCBzaG91bGQgdHJpZ2dlciBiZWZvcmUgaW50ZXJuYWwgc2V0U3RhdGUgdG8gYXZvaWQgYGxvYWREYXRhYCB0cmlnZ2VyIHR3aWNlLlxuICAgICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FudC1kZXNpZ24vYW50LWRlc2lnbi9pc3N1ZXMvMTI0NjRcbiAgICAgICAgICAgIG9uTG9hZCA9PT0gbnVsbCB8fCBvbkxvYWQgPT09IHZvaWQgMCB8fCBvbkxvYWQobmV3TG9hZGVkS2V5cywge1xuICAgICAgICAgICAgICBldmVudDogJ2xvYWQnLFxuICAgICAgICAgICAgICBub2RlOiB0cmVlTm9kZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBfdGhpcy5zZXRVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gICAgICAgICAgICAgIGxvYWRlZEtleXM6IG5ld0xvYWRlZEtleXNcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgX3RoaXMuc2V0U3RhdGUoZnVuY3Rpb24gKHByZXZTdGF0ZSkge1xuICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGxvYWRpbmdLZXlzOiBhcnJEZWwocHJldlN0YXRlLmxvYWRpbmdLZXlzLCBrZXkpXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZSkge1xuICAgICAgICAgICAgX3RoaXMuc2V0U3RhdGUoZnVuY3Rpb24gKHByZXZTdGF0ZSkge1xuICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGxvYWRpbmdLZXlzOiBhcnJEZWwocHJldlN0YXRlLmxvYWRpbmdLZXlzLCBrZXkpXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8gSWYgZXhjZWVkIG1heCByZXRyeSB0aW1lcywgd2UgZ2l2ZSB1cCByZXRyeVxuICAgICAgICAgICAgX3RoaXMubG9hZGluZ1JldHJ5VGltZXNba2V5XSA9IChfdGhpcy5sb2FkaW5nUmV0cnlUaW1lc1trZXldIHx8IDApICsgMTtcbiAgICAgICAgICAgIGlmIChfdGhpcy5sb2FkaW5nUmV0cnlUaW1lc1trZXldID49IE1BWF9SRVRSWV9USU1FUykge1xuICAgICAgICAgICAgICB2YXIgY3VycmVudExvYWRlZEtleXMgPSBfdGhpcy5zdGF0ZS5sb2FkZWRLZXlzO1xuICAgICAgICAgICAgICB3YXJuaW5nKGZhbHNlLCAnUmV0cnkgZm9yIGBsb2FkRGF0YWAgbWFueSB0aW1lcyBidXQgc3RpbGwgZmFpbGVkLiBObyBtb3JlIHJldHJ5LicpO1xuICAgICAgICAgICAgICBfdGhpcy5zZXRVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gICAgICAgICAgICAgICAgbG9hZGVkS2V5czogYXJyQWRkKGN1cnJlbnRMb2FkZWRLZXlzLCBrZXkpXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZWplY3QoZSk7XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGxvYWRpbmdLZXlzOiBhcnJBZGQobG9hZGluZ0tleXMsIGtleSlcbiAgICAgICAgICB9O1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBOb3QgY2FyZSB3YXJuaW5nIGlmIHdlIGlnbm9yZSB0aGlzXG4gICAgICBsb2FkUHJvbWlzZS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7XG4gICAgICByZXR1cm4gbG9hZFByb21pc2U7XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uTm9kZU1vdXNlRW50ZXJcIiwgZnVuY3Rpb24gKGV2ZW50LCBub2RlKSB7XG4gICAgICB2YXIgb25Nb3VzZUVudGVyID0gX3RoaXMucHJvcHMub25Nb3VzZUVudGVyO1xuICAgICAgb25Nb3VzZUVudGVyID09PSBudWxsIHx8IG9uTW91c2VFbnRlciA9PT0gdm9pZCAwIHx8IG9uTW91c2VFbnRlcih7XG4gICAgICAgIGV2ZW50OiBldmVudCxcbiAgICAgICAgbm9kZTogbm9kZVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uTm9kZU1vdXNlTGVhdmVcIiwgZnVuY3Rpb24gKGV2ZW50LCBub2RlKSB7XG4gICAgICB2YXIgb25Nb3VzZUxlYXZlID0gX3RoaXMucHJvcHMub25Nb3VzZUxlYXZlO1xuICAgICAgb25Nb3VzZUxlYXZlID09PSBudWxsIHx8IG9uTW91c2VMZWF2ZSA9PT0gdm9pZCAwIHx8IG9uTW91c2VMZWF2ZSh7XG4gICAgICAgIGV2ZW50OiBldmVudCxcbiAgICAgICAgbm9kZTogbm9kZVxuICAgICAgfSk7XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uTm9kZUNvbnRleHRNZW51XCIsIGZ1bmN0aW9uIChldmVudCwgbm9kZSkge1xuICAgICAgdmFyIG9uUmlnaHRDbGljayA9IF90aGlzLnByb3BzLm9uUmlnaHRDbGljaztcbiAgICAgIGlmIChvblJpZ2h0Q2xpY2spIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgb25SaWdodENsaWNrKHtcbiAgICAgICAgICBldmVudDogZXZlbnQsXG4gICAgICAgICAgbm9kZTogbm9kZVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25Gb2N1c1wiLCBmdW5jdGlvbiAoKSB7XG4gICAgICB2YXIgb25Gb2N1cyA9IF90aGlzLnByb3BzLm9uRm9jdXM7XG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGZvY3VzZWQ6IHRydWVcbiAgICAgIH0pO1xuICAgICAgZm9yICh2YXIgX2xlbjIgPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4yKSwgX2tleTIgPSAwOyBfa2V5MiA8IF9sZW4yOyBfa2V5MisrKSB7XG4gICAgICAgIGFyZ3NbX2tleTJdID0gYXJndW1lbnRzW19rZXkyXTtcbiAgICAgIH1cbiAgICAgIG9uRm9jdXMgPT09IG51bGwgfHwgb25Gb2N1cyA9PT0gdm9pZCAwIHx8IG9uRm9jdXMuYXBwbHkodm9pZCAwLCBhcmdzKTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25CbHVyXCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgIHZhciBvbkJsdXIgPSBfdGhpcy5wcm9wcy5vbkJsdXI7XG4gICAgICBfdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgIGZvY3VzZWQ6IGZhbHNlXG4gICAgICB9KTtcbiAgICAgIF90aGlzLm9uQWN0aXZlQ2hhbmdlKG51bGwpO1xuICAgICAgZm9yICh2YXIgX2xlbjMgPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4zKSwgX2tleTMgPSAwOyBfa2V5MyA8IF9sZW4zOyBfa2V5MysrKSB7XG4gICAgICAgIGFyZ3NbX2tleTNdID0gYXJndW1lbnRzW19rZXkzXTtcbiAgICAgIH1cbiAgICAgIG9uQmx1ciA9PT0gbnVsbCB8fCBvbkJsdXIgPT09IHZvaWQgMCB8fCBvbkJsdXIuYXBwbHkodm9pZCAwLCBhcmdzKTtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwiZ2V0VHJlZU5vZGVSZXF1aXJlZFByb3BzXCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTggPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAgZXhwYW5kZWRLZXlzID0gX3RoaXMkc3RhdGU4LmV4cGFuZGVkS2V5cyxcbiAgICAgICAgc2VsZWN0ZWRLZXlzID0gX3RoaXMkc3RhdGU4LnNlbGVjdGVkS2V5cyxcbiAgICAgICAgbG9hZGVkS2V5cyA9IF90aGlzJHN0YXRlOC5sb2FkZWRLZXlzLFxuICAgICAgICBsb2FkaW5nS2V5cyA9IF90aGlzJHN0YXRlOC5sb2FkaW5nS2V5cyxcbiAgICAgICAgY2hlY2tlZEtleXMgPSBfdGhpcyRzdGF0ZTguY2hlY2tlZEtleXMsXG4gICAgICAgIGhhbGZDaGVja2VkS2V5cyA9IF90aGlzJHN0YXRlOC5oYWxmQ2hlY2tlZEtleXMsXG4gICAgICAgIGRyYWdPdmVyTm9kZUtleSA9IF90aGlzJHN0YXRlOC5kcmFnT3Zlck5vZGVLZXksXG4gICAgICAgIGRyb3BQb3NpdGlvbiA9IF90aGlzJHN0YXRlOC5kcm9wUG9zaXRpb24sXG4gICAgICAgIGtleUVudGl0aWVzID0gX3RoaXMkc3RhdGU4LmtleUVudGl0aWVzO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgZXhwYW5kZWRLZXlzOiBleHBhbmRlZEtleXMgfHwgW10sXG4gICAgICAgIHNlbGVjdGVkS2V5czogc2VsZWN0ZWRLZXlzIHx8IFtdLFxuICAgICAgICBsb2FkZWRLZXlzOiBsb2FkZWRLZXlzIHx8IFtdLFxuICAgICAgICBsb2FkaW5nS2V5czogbG9hZGluZ0tleXMgfHwgW10sXG4gICAgICAgIGNoZWNrZWRLZXlzOiBjaGVja2VkS2V5cyB8fCBbXSxcbiAgICAgICAgaGFsZkNoZWNrZWRLZXlzOiBoYWxmQ2hlY2tlZEtleXMgfHwgW10sXG4gICAgICAgIGRyYWdPdmVyTm9kZUtleTogZHJhZ092ZXJOb2RlS2V5LFxuICAgICAgICBkcm9wUG9zaXRpb246IGRyb3BQb3NpdGlvbixcbiAgICAgICAga2V5RW50aXRpZXM6IGtleUVudGl0aWVzXG4gICAgICB9O1xuICAgIH0pO1xuICAgIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PSBFeHBhbmRlZCA9PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgICAvKiogU2V0IHVuY29udHJvbGxlZCBgZXhwYW5kZWRLZXlzYC4gVGhpcyB3aWxsIGFsc28gYXV0byB1cGRhdGUgYGZsYXR0ZW5Ob2Rlc2AuICovXG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcInNldEV4cGFuZGVkS2V5c1wiLCBmdW5jdGlvbiAoZXhwYW5kZWRLZXlzKSB7XG4gICAgICB2YXIgX3RoaXMkc3RhdGU5ID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIHRyZWVEYXRhID0gX3RoaXMkc3RhdGU5LnRyZWVEYXRhLFxuICAgICAgICBmaWVsZE5hbWVzID0gX3RoaXMkc3RhdGU5LmZpZWxkTmFtZXM7XG4gICAgICB2YXIgZmxhdHRlbk5vZGVzID0gZmxhdHRlblRyZWVEYXRhKHRyZWVEYXRhLCBleHBhbmRlZEtleXMsIGZpZWxkTmFtZXMpO1xuICAgICAgX3RoaXMuc2V0VW5jb250cm9sbGVkU3RhdGUoe1xuICAgICAgICBleHBhbmRlZEtleXM6IGV4cGFuZGVkS2V5cyxcbiAgICAgICAgZmxhdHRlbk5vZGVzOiBmbGF0dGVuTm9kZXNcbiAgICAgIH0sIHRydWUpO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbk5vZGVFeHBhbmRcIiwgZnVuY3Rpb24gKGUsIHRyZWVOb2RlKSB7XG4gICAgICB2YXIgZXhwYW5kZWRLZXlzID0gX3RoaXMuc3RhdGUuZXhwYW5kZWRLZXlzO1xuICAgICAgdmFyIF90aGlzJHN0YXRlMTAgPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAgbGlzdENoYW5naW5nID0gX3RoaXMkc3RhdGUxMC5saXN0Q2hhbmdpbmcsXG4gICAgICAgIGZpZWxkTmFtZXMgPSBfdGhpcyRzdGF0ZTEwLmZpZWxkTmFtZXM7XG4gICAgICB2YXIgX3RoaXMkcHJvcHM4ID0gX3RoaXMucHJvcHMsXG4gICAgICAgIG9uRXhwYW5kID0gX3RoaXMkcHJvcHM4Lm9uRXhwYW5kLFxuICAgICAgICBsb2FkRGF0YSA9IF90aGlzJHByb3BzOC5sb2FkRGF0YTtcbiAgICAgIHZhciBleHBhbmRlZCA9IHRyZWVOb2RlLmV4cGFuZGVkO1xuICAgICAgdmFyIGtleSA9IHRyZWVOb2RlW2ZpZWxkTmFtZXMua2V5XTtcblxuICAgICAgLy8gRG8gbm90aGluZyB3aGVuIG1vdGlvbiBpcyBpbiBwcm9ncmVzc1xuICAgICAgaWYgKGxpc3RDaGFuZ2luZykge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIFVwZGF0ZSBzZWxlY3RlZCBrZXlzXG4gICAgICB2YXIgaW5kZXggPSBleHBhbmRlZEtleXMuaW5kZXhPZihrZXkpO1xuICAgICAgdmFyIHRhcmdldEV4cGFuZGVkID0gIWV4cGFuZGVkO1xuICAgICAgd2FybmluZyhleHBhbmRlZCAmJiBpbmRleCAhPT0gLTEgfHwgIWV4cGFuZGVkICYmIGluZGV4ID09PSAtMSwgJ0V4cGFuZCBzdGF0ZSBub3Qgc3luYyB3aXRoIGluZGV4IGNoZWNrJyk7XG4gICAgICBpZiAodGFyZ2V0RXhwYW5kZWQpIHtcbiAgICAgICAgZXhwYW5kZWRLZXlzID0gYXJyQWRkKGV4cGFuZGVkS2V5cywga2V5KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGV4cGFuZGVkS2V5cyA9IGFyckRlbChleHBhbmRlZEtleXMsIGtleSk7XG4gICAgICB9XG4gICAgICBfdGhpcy5zZXRFeHBhbmRlZEtleXMoZXhwYW5kZWRLZXlzKTtcbiAgICAgIG9uRXhwYW5kID09PSBudWxsIHx8IG9uRXhwYW5kID09PSB2b2lkIDAgfHwgb25FeHBhbmQoZXhwYW5kZWRLZXlzLCB7XG4gICAgICAgIG5vZGU6IHRyZWVOb2RlLFxuICAgICAgICBleHBhbmRlZDogdGFyZ2V0RXhwYW5kZWQsXG4gICAgICAgIG5hdGl2ZUV2ZW50OiBlLm5hdGl2ZUV2ZW50XG4gICAgICB9KTtcblxuICAgICAgLy8gQXN5bmMgTG9hZCBkYXRhXG4gICAgICBpZiAodGFyZ2V0RXhwYW5kZWQgJiYgbG9hZERhdGEpIHtcbiAgICAgICAgdmFyIGxvYWRQcm9taXNlID0gX3RoaXMub25Ob2RlTG9hZCh0cmVlTm9kZSk7XG4gICAgICAgIGlmIChsb2FkUHJvbWlzZSkge1xuICAgICAgICAgIGxvYWRQcm9taXNlLnRoZW4oZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgLy8gW0xlZ2FjeV0gUmVmcmVzaCBsb2dpY1xuICAgICAgICAgICAgdmFyIG5ld0ZsYXR0ZW5UcmVlRGF0YSA9IGZsYXR0ZW5UcmVlRGF0YShfdGhpcy5zdGF0ZS50cmVlRGF0YSwgZXhwYW5kZWRLZXlzLCBmaWVsZE5hbWVzKTtcbiAgICAgICAgICAgIF90aGlzLnNldFVuY29udHJvbGxlZFN0YXRlKHtcbiAgICAgICAgICAgICAgZmxhdHRlbk5vZGVzOiBuZXdGbGF0dGVuVHJlZURhdGFcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHZhciBjdXJyZW50RXhwYW5kZWRLZXlzID0gX3RoaXMuc3RhdGUuZXhwYW5kZWRLZXlzO1xuICAgICAgICAgICAgdmFyIGV4cGFuZGVkS2V5c1RvUmVzdG9yZSA9IGFyckRlbChjdXJyZW50RXhwYW5kZWRLZXlzLCBrZXkpO1xuICAgICAgICAgICAgX3RoaXMuc2V0RXhwYW5kZWRLZXlzKGV4cGFuZGVkS2V5c1RvUmVzdG9yZSk7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib25MaXN0Q2hhbmdlU3RhcnRcIiwgZnVuY3Rpb24gKCkge1xuICAgICAgX3RoaXMuc2V0VW5jb250cm9sbGVkU3RhdGUoe1xuICAgICAgICBsaXN0Q2hhbmdpbmc6IHRydWVcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbkxpc3RDaGFuZ2VFbmRcIiwgZnVuY3Rpb24gKCkge1xuICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICAgIF90aGlzLnNldFVuY29udHJvbGxlZFN0YXRlKHtcbiAgICAgICAgICBsaXN0Q2hhbmdpbmc6IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgfSk7XG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09IEtleWJvYXJkID09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJvbkFjdGl2ZUNoYW5nZVwiLCBmdW5jdGlvbiAobmV3QWN0aXZlS2V5KSB7XG4gICAgICB2YXIgYWN0aXZlS2V5ID0gX3RoaXMuc3RhdGUuYWN0aXZlS2V5O1xuICAgICAgdmFyIF90aGlzJHByb3BzOSA9IF90aGlzLnByb3BzLFxuICAgICAgICBvbkFjdGl2ZUNoYW5nZSA9IF90aGlzJHByb3BzOS5vbkFjdGl2ZUNoYW5nZSxcbiAgICAgICAgX3RoaXMkcHJvcHM5JGl0ZW1TY3JvID0gX3RoaXMkcHJvcHM5Lml0ZW1TY3JvbGxPZmZzZXQsXG4gICAgICAgIGl0ZW1TY3JvbGxPZmZzZXQgPSBfdGhpcyRwcm9wczkkaXRlbVNjcm8gPT09IHZvaWQgMCA/IDAgOiBfdGhpcyRwcm9wczkkaXRlbVNjcm87XG4gICAgICBpZiAoYWN0aXZlS2V5ID09PSBuZXdBY3RpdmVLZXkpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgX3RoaXMuc2V0U3RhdGUoe1xuICAgICAgICBhY3RpdmVLZXk6IG5ld0FjdGl2ZUtleVxuICAgICAgfSk7XG4gICAgICBpZiAobmV3QWN0aXZlS2V5ICE9PSBudWxsKSB7XG4gICAgICAgIF90aGlzLnNjcm9sbFRvKHtcbiAgICAgICAgICBrZXk6IG5ld0FjdGl2ZUtleSxcbiAgICAgICAgICBvZmZzZXQ6IGl0ZW1TY3JvbGxPZmZzZXRcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBvbkFjdGl2ZUNoYW5nZSA9PT0gbnVsbCB8fCBvbkFjdGl2ZUNoYW5nZSA9PT0gdm9pZCAwIHx8IG9uQWN0aXZlQ2hhbmdlKG5ld0FjdGl2ZUtleSk7XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcImdldEFjdGl2ZUl0ZW1cIiwgZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIF90aGlzJHN0YXRlMTEgPSBfdGhpcy5zdGF0ZSxcbiAgICAgICAgYWN0aXZlS2V5ID0gX3RoaXMkc3RhdGUxMS5hY3RpdmVLZXksXG4gICAgICAgIGZsYXR0ZW5Ob2RlcyA9IF90aGlzJHN0YXRlMTEuZmxhdHRlbk5vZGVzO1xuICAgICAgaWYgKGFjdGl2ZUtleSA9PT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBmbGF0dGVuTm9kZXMuZmluZChmdW5jdGlvbiAoX3JlZjIpIHtcbiAgICAgICAgdmFyIGtleSA9IF9yZWYyLmtleTtcbiAgICAgICAgcmV0dXJuIGtleSA9PT0gYWN0aXZlS2V5O1xuICAgICAgfSkgfHwgbnVsbDtcbiAgICB9KTtcbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwib2Zmc2V0QWN0aXZlS2V5XCIsIGZ1bmN0aW9uIChvZmZzZXQpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTEyID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIGZsYXR0ZW5Ob2RlcyA9IF90aGlzJHN0YXRlMTIuZmxhdHRlbk5vZGVzLFxuICAgICAgICBhY3RpdmVLZXkgPSBfdGhpcyRzdGF0ZTEyLmFjdGl2ZUtleTtcbiAgICAgIHZhciBpbmRleCA9IGZsYXR0ZW5Ob2Rlcy5maW5kSW5kZXgoZnVuY3Rpb24gKF9yZWYzKSB7XG4gICAgICAgIHZhciBrZXkgPSBfcmVmMy5rZXk7XG4gICAgICAgIHJldHVybiBrZXkgPT09IGFjdGl2ZUtleTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBBbGlnbiB3aXRoIGluZGV4XG4gICAgICBpZiAoaW5kZXggPT09IC0xICYmIG9mZnNldCA8IDApIHtcbiAgICAgICAgaW5kZXggPSBmbGF0dGVuTm9kZXMubGVuZ3RoO1xuICAgICAgfVxuICAgICAgaW5kZXggPSAoaW5kZXggKyBvZmZzZXQgKyBmbGF0dGVuTm9kZXMubGVuZ3RoKSAlIGZsYXR0ZW5Ob2Rlcy5sZW5ndGg7XG4gICAgICB2YXIgaXRlbSA9IGZsYXR0ZW5Ob2Rlc1tpbmRleF07XG4gICAgICBpZiAoaXRlbSkge1xuICAgICAgICB2YXIgX2tleTQgPSBpdGVtLmtleTtcbiAgICAgICAgX3RoaXMub25BY3RpdmVDaGFuZ2UoX2tleTQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgX3RoaXMub25BY3RpdmVDaGFuZ2UobnVsbCk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgX2RlZmluZVByb3BlcnR5KF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQoX3RoaXMpLCBcIm9uS2V5RG93blwiLCBmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTEzID0gX3RoaXMuc3RhdGUsXG4gICAgICAgIGFjdGl2ZUtleSA9IF90aGlzJHN0YXRlMTMuYWN0aXZlS2V5LFxuICAgICAgICBleHBhbmRlZEtleXMgPSBfdGhpcyRzdGF0ZTEzLmV4cGFuZGVkS2V5cyxcbiAgICAgICAgY2hlY2tlZEtleXMgPSBfdGhpcyRzdGF0ZTEzLmNoZWNrZWRLZXlzLFxuICAgICAgICBmaWVsZE5hbWVzID0gX3RoaXMkc3RhdGUxMy5maWVsZE5hbWVzO1xuICAgICAgdmFyIF90aGlzJHByb3BzMTAgPSBfdGhpcy5wcm9wcyxcbiAgICAgICAgb25LZXlEb3duID0gX3RoaXMkcHJvcHMxMC5vbktleURvd24sXG4gICAgICAgIGNoZWNrYWJsZSA9IF90aGlzJHByb3BzMTAuY2hlY2thYmxlLFxuICAgICAgICBzZWxlY3RhYmxlID0gX3RoaXMkcHJvcHMxMC5zZWxlY3RhYmxlO1xuXG4gICAgICAvLyA+Pj4+Pj4+Pj4+IERpcmVjdGlvblxuICAgICAgc3dpdGNoIChldmVudC53aGljaCkge1xuICAgICAgICBjYXNlIEtleUNvZGUuVVA6XG4gICAgICAgICAge1xuICAgICAgICAgICAgX3RoaXMub2Zmc2V0QWN0aXZlS2V5KC0xKTtcbiAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgIGNhc2UgS2V5Q29kZS5ET1dOOlxuICAgICAgICAgIHtcbiAgICAgICAgICAgIF90aGlzLm9mZnNldEFjdGl2ZUtleSgxKTtcbiAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vID4+Pj4+Pj4+Pj4gRXhwYW5kICYgU2VsZWN0aW9uXG4gICAgICB2YXIgYWN0aXZlSXRlbSA9IF90aGlzLmdldEFjdGl2ZUl0ZW0oKTtcbiAgICAgIGlmIChhY3RpdmVJdGVtICYmIGFjdGl2ZUl0ZW0uZGF0YSkge1xuICAgICAgICB2YXIgdHJlZU5vZGVSZXF1aXJlZFByb3BzID0gX3RoaXMuZ2V0VHJlZU5vZGVSZXF1aXJlZFByb3BzKCk7XG4gICAgICAgIHZhciBleHBhbmRhYmxlID0gYWN0aXZlSXRlbS5kYXRhLmlzTGVhZiA9PT0gZmFsc2UgfHwgISEoYWN0aXZlSXRlbS5kYXRhW2ZpZWxkTmFtZXMuY2hpbGRyZW5dIHx8IFtdKS5sZW5ndGg7XG4gICAgICAgIHZhciBldmVudE5vZGUgPSBjb252ZXJ0Tm9kZVByb3BzVG9FdmVudERhdGEoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBnZXRUcmVlTm9kZVByb3BzKGFjdGl2ZUtleSwgdHJlZU5vZGVSZXF1aXJlZFByb3BzKSksIHt9LCB7XG4gICAgICAgICAgZGF0YTogYWN0aXZlSXRlbS5kYXRhLFxuICAgICAgICAgIGFjdGl2ZTogdHJ1ZVxuICAgICAgICB9KSk7XG4gICAgICAgIHN3aXRjaCAoZXZlbnQud2hpY2gpIHtcbiAgICAgICAgICAvLyA+Pj4gRXhwYW5kXG4gICAgICAgICAgY2FzZSBLZXlDb2RlLkxFRlQ6XG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIC8vIENvbGxhcHNlIGlmIHBvc3NpYmxlXG4gICAgICAgICAgICAgIGlmIChleHBhbmRhYmxlICYmIGV4cGFuZGVkS2V5cy5pbmNsdWRlcyhhY3RpdmVLZXkpKSB7XG4gICAgICAgICAgICAgICAgX3RoaXMub25Ob2RlRXhwYW5kKHt9LCBldmVudE5vZGUpO1xuICAgICAgICAgICAgICB9IGVsc2UgaWYgKGFjdGl2ZUl0ZW0ucGFyZW50KSB7XG4gICAgICAgICAgICAgICAgX3RoaXMub25BY3RpdmVDaGFuZ2UoYWN0aXZlSXRlbS5wYXJlbnQua2V5KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICBjYXNlIEtleUNvZGUuUklHSFQ6XG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIC8vIEV4cGFuZCBpZiBwb3NzaWJsZVxuICAgICAgICAgICAgICBpZiAoZXhwYW5kYWJsZSAmJiAhZXhwYW5kZWRLZXlzLmluY2x1ZGVzKGFjdGl2ZUtleSkpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5vbk5vZGVFeHBhbmQoe30sIGV2ZW50Tm9kZSk7XG4gICAgICAgICAgICAgIH0gZWxzZSBpZiAoYWN0aXZlSXRlbS5jaGlsZHJlbiAmJiBhY3RpdmVJdGVtLmNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgIF90aGlzLm9uQWN0aXZlQ2hhbmdlKGFjdGl2ZUl0ZW0uY2hpbGRyZW5bMF0ua2V5KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIFNlbGVjdGlvblxuICAgICAgICAgIGNhc2UgS2V5Q29kZS5FTlRFUjpcbiAgICAgICAgICBjYXNlIEtleUNvZGUuU1BBQ0U6XG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGlmIChjaGVja2FibGUgJiYgIWV2ZW50Tm9kZS5kaXNhYmxlZCAmJiBldmVudE5vZGUuY2hlY2thYmxlICE9PSBmYWxzZSAmJiAhZXZlbnROb2RlLmRpc2FibGVDaGVja2JveCkge1xuICAgICAgICAgICAgICAgIF90aGlzLm9uTm9kZUNoZWNrKHt9LCBldmVudE5vZGUsICFjaGVja2VkS2V5cy5pbmNsdWRlcyhhY3RpdmVLZXkpKTtcbiAgICAgICAgICAgICAgfSBlbHNlIGlmICghY2hlY2thYmxlICYmIHNlbGVjdGFibGUgJiYgIWV2ZW50Tm9kZS5kaXNhYmxlZCAmJiBldmVudE5vZGUuc2VsZWN0YWJsZSAhPT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5vbk5vZGVTZWxlY3Qoe30sIGV2ZW50Tm9kZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIG9uS2V5RG93biA9PT0gbnVsbCB8fCBvbktleURvd24gPT09IHZvaWQgMCB8fCBvbktleURvd24oZXZlbnQpO1xuICAgIH0pO1xuICAgIC8qKlxuICAgICAqIE9ubHkgdXBkYXRlIHRoZSB2YWx1ZSB3aGljaCBpcyBub3QgaW4gcHJvcHNcbiAgICAgKi9cbiAgICBfZGVmaW5lUHJvcGVydHkoX2Fzc2VydFRoaXNJbml0aWFsaXplZChfdGhpcyksIFwic2V0VW5jb250cm9sbGVkU3RhdGVcIiwgZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICB2YXIgYXRvbWljID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiBmYWxzZTtcbiAgICAgIHZhciBmb3JjZVN0YXRlID0gYXJndW1lbnRzLmxlbmd0aCA+IDIgJiYgYXJndW1lbnRzWzJdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMl0gOiBudWxsO1xuICAgICAgaWYgKCFfdGhpcy5kZXN0cm95ZWQpIHtcbiAgICAgICAgdmFyIG5lZWRTeW5jID0gZmFsc2U7XG4gICAgICAgIHZhciBhbGxQYXNzZWQgPSB0cnVlO1xuICAgICAgICB2YXIgbmV3U3RhdGUgPSB7fTtcbiAgICAgICAgT2JqZWN0LmtleXMoc3RhdGUpLmZvckVhY2goZnVuY3Rpb24gKG5hbWUpIHtcbiAgICAgICAgICBpZiAoX3RoaXMucHJvcHMuaGFzT3duUHJvcGVydHkobmFtZSkpIHtcbiAgICAgICAgICAgIGFsbFBhc3NlZCA9IGZhbHNlO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgICBuZWVkU3luYyA9IHRydWU7XG4gICAgICAgICAgbmV3U3RhdGVbbmFtZV0gPSBzdGF0ZVtuYW1lXTtcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChuZWVkU3luYyAmJiAoIWF0b21pYyB8fCBhbGxQYXNzZWQpKSB7XG4gICAgICAgICAgX3RoaXMuc2V0U3RhdGUoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBuZXdTdGF0ZSksIGZvcmNlU3RhdGUpKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuICAgIF9kZWZpbmVQcm9wZXJ0eShfYXNzZXJ0VGhpc0luaXRpYWxpemVkKF90aGlzKSwgXCJzY3JvbGxUb1wiLCBmdW5jdGlvbiAoc2Nyb2xsKSB7XG4gICAgICBfdGhpcy5saXN0UmVmLmN1cnJlbnQuc2Nyb2xsVG8oc2Nyb2xsKTtcbiAgICB9KTtcbiAgICByZXR1cm4gX3RoaXM7XG4gIH1cbiAgX2NyZWF0ZUNsYXNzKFRyZWUsIFt7XG4gICAga2V5OiBcImNvbXBvbmVudERpZE1vdW50XCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGNvbXBvbmVudERpZE1vdW50KCkge1xuICAgICAgdGhpcy5kZXN0cm95ZWQgPSBmYWxzZTtcbiAgICAgIHRoaXMub25VcGRhdGVkKCk7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImNvbXBvbmVudERpZFVwZGF0ZVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBjb21wb25lbnREaWRVcGRhdGUoKSB7XG4gICAgICB0aGlzLm9uVXBkYXRlZCgpO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJvblVwZGF0ZWRcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gb25VcGRhdGVkKCkge1xuICAgICAgdmFyIF90aGlzJHByb3BzMTEgPSB0aGlzLnByb3BzLFxuICAgICAgICBhY3RpdmVLZXkgPSBfdGhpcyRwcm9wczExLmFjdGl2ZUtleSxcbiAgICAgICAgX3RoaXMkcHJvcHMxMSRpdGVtU2NyID0gX3RoaXMkcHJvcHMxMS5pdGVtU2Nyb2xsT2Zmc2V0LFxuICAgICAgICBpdGVtU2Nyb2xsT2Zmc2V0ID0gX3RoaXMkcHJvcHMxMSRpdGVtU2NyID09PSB2b2lkIDAgPyAwIDogX3RoaXMkcHJvcHMxMSRpdGVtU2NyO1xuICAgICAgaWYgKGFjdGl2ZUtleSAhPT0gdW5kZWZpbmVkICYmIGFjdGl2ZUtleSAhPT0gdGhpcy5zdGF0ZS5hY3RpdmVLZXkpIHtcbiAgICAgICAgdGhpcy5zZXRTdGF0ZSh7XG4gICAgICAgICAgYWN0aXZlS2V5OiBhY3RpdmVLZXlcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChhY3RpdmVLZXkgIT09IG51bGwpIHtcbiAgICAgICAgICB0aGlzLnNjcm9sbFRvKHtcbiAgICAgICAgICAgIGtleTogYWN0aXZlS2V5LFxuICAgICAgICAgICAgb2Zmc2V0OiBpdGVtU2Nyb2xsT2Zmc2V0XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiY29tcG9uZW50V2lsbFVubW91bnRcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gY29tcG9uZW50V2lsbFVubW91bnQoKSB7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJhZ2VuZCcsIHRoaXMub25XaW5kb3dEcmFnRW5kKTtcbiAgICAgIHRoaXMuZGVzdHJveWVkID0gdHJ1ZTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVzZXREcmFnU3RhdGVcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gcmVzZXREcmFnU3RhdGUoKSB7XG4gICAgICB0aGlzLnNldFN0YXRlKHtcbiAgICAgICAgZHJhZ092ZXJOb2RlS2V5OiBudWxsLFxuICAgICAgICBkcm9wUG9zaXRpb246IG51bGwsXG4gICAgICAgIGRyb3BMZXZlbE9mZnNldDogbnVsbCxcbiAgICAgICAgZHJvcFRhcmdldEtleTogbnVsbCxcbiAgICAgICAgZHJvcENvbnRhaW5lcktleTogbnVsbCxcbiAgICAgICAgZHJvcFRhcmdldFBvczogbnVsbCxcbiAgICAgICAgZHJvcEFsbG93ZWQ6IGZhbHNlXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwicmVuZGVyXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlbmRlcigpIHtcbiAgICAgIHZhciBfdGhpcyRzdGF0ZTE0ID0gdGhpcy5zdGF0ZSxcbiAgICAgICAgZm9jdXNlZCA9IF90aGlzJHN0YXRlMTQuZm9jdXNlZCxcbiAgICAgICAgZmxhdHRlbk5vZGVzID0gX3RoaXMkc3RhdGUxNC5mbGF0dGVuTm9kZXMsXG4gICAgICAgIGtleUVudGl0aWVzID0gX3RoaXMkc3RhdGUxNC5rZXlFbnRpdGllcyxcbiAgICAgICAgZHJhZ2dpbmdOb2RlS2V5ID0gX3RoaXMkc3RhdGUxNC5kcmFnZ2luZ05vZGVLZXksXG4gICAgICAgIGFjdGl2ZUtleSA9IF90aGlzJHN0YXRlMTQuYWN0aXZlS2V5LFxuICAgICAgICBkcm9wTGV2ZWxPZmZzZXQgPSBfdGhpcyRzdGF0ZTE0LmRyb3BMZXZlbE9mZnNldCxcbiAgICAgICAgZHJvcENvbnRhaW5lcktleSA9IF90aGlzJHN0YXRlMTQuZHJvcENvbnRhaW5lcktleSxcbiAgICAgICAgZHJvcFRhcmdldEtleSA9IF90aGlzJHN0YXRlMTQuZHJvcFRhcmdldEtleSxcbiAgICAgICAgZHJvcFBvc2l0aW9uID0gX3RoaXMkc3RhdGUxNC5kcm9wUG9zaXRpb24sXG4gICAgICAgIGRyYWdPdmVyTm9kZUtleSA9IF90aGlzJHN0YXRlMTQuZHJhZ092ZXJOb2RlS2V5LFxuICAgICAgICBpbmRlbnQgPSBfdGhpcyRzdGF0ZTE0LmluZGVudDtcbiAgICAgIHZhciBfdGhpcyRwcm9wczEyID0gdGhpcy5wcm9wcyxcbiAgICAgICAgcHJlZml4Q2xzID0gX3RoaXMkcHJvcHMxMi5wcmVmaXhDbHMsXG4gICAgICAgIGNsYXNzTmFtZSA9IF90aGlzJHByb3BzMTIuY2xhc3NOYW1lLFxuICAgICAgICBzdHlsZSA9IF90aGlzJHByb3BzMTIuc3R5bGUsXG4gICAgICAgIHNob3dMaW5lID0gX3RoaXMkcHJvcHMxMi5zaG93TGluZSxcbiAgICAgICAgZm9jdXNhYmxlID0gX3RoaXMkcHJvcHMxMi5mb2N1c2FibGUsXG4gICAgICAgIF90aGlzJHByb3BzMTIkdGFiSW5kZSA9IF90aGlzJHByb3BzMTIudGFiSW5kZXgsXG4gICAgICAgIHRhYkluZGV4ID0gX3RoaXMkcHJvcHMxMiR0YWJJbmRlID09PSB2b2lkIDAgPyAwIDogX3RoaXMkcHJvcHMxMiR0YWJJbmRlLFxuICAgICAgICBzZWxlY3RhYmxlID0gX3RoaXMkcHJvcHMxMi5zZWxlY3RhYmxlLFxuICAgICAgICBzaG93SWNvbiA9IF90aGlzJHByb3BzMTIuc2hvd0ljb24sXG4gICAgICAgIGljb24gPSBfdGhpcyRwcm9wczEyLmljb24sXG4gICAgICAgIHN3aXRjaGVySWNvbiA9IF90aGlzJHByb3BzMTIuc3dpdGNoZXJJY29uLFxuICAgICAgICBkcmFnZ2FibGUgPSBfdGhpcyRwcm9wczEyLmRyYWdnYWJsZSxcbiAgICAgICAgY2hlY2thYmxlID0gX3RoaXMkcHJvcHMxMi5jaGVja2FibGUsXG4gICAgICAgIGNoZWNrU3RyaWN0bHkgPSBfdGhpcyRwcm9wczEyLmNoZWNrU3RyaWN0bHksXG4gICAgICAgIGRpc2FibGVkID0gX3RoaXMkcHJvcHMxMi5kaXNhYmxlZCxcbiAgICAgICAgbW90aW9uID0gX3RoaXMkcHJvcHMxMi5tb3Rpb24sXG4gICAgICAgIGxvYWREYXRhID0gX3RoaXMkcHJvcHMxMi5sb2FkRGF0YSxcbiAgICAgICAgZmlsdGVyVHJlZU5vZGUgPSBfdGhpcyRwcm9wczEyLmZpbHRlclRyZWVOb2RlLFxuICAgICAgICBoZWlnaHQgPSBfdGhpcyRwcm9wczEyLmhlaWdodCxcbiAgICAgICAgaXRlbUhlaWdodCA9IF90aGlzJHByb3BzMTIuaXRlbUhlaWdodCxcbiAgICAgICAgdmlydHVhbCA9IF90aGlzJHByb3BzMTIudmlydHVhbCxcbiAgICAgICAgdGl0bGVSZW5kZXIgPSBfdGhpcyRwcm9wczEyLnRpdGxlUmVuZGVyLFxuICAgICAgICBkcm9wSW5kaWNhdG9yUmVuZGVyID0gX3RoaXMkcHJvcHMxMi5kcm9wSW5kaWNhdG9yUmVuZGVyLFxuICAgICAgICBvbkNvbnRleHRNZW51ID0gX3RoaXMkcHJvcHMxMi5vbkNvbnRleHRNZW51LFxuICAgICAgICBvblNjcm9sbCA9IF90aGlzJHByb3BzMTIub25TY3JvbGwsXG4gICAgICAgIGRpcmVjdGlvbiA9IF90aGlzJHByb3BzMTIuZGlyZWN0aW9uLFxuICAgICAgICByb290Q2xhc3NOYW1lID0gX3RoaXMkcHJvcHMxMi5yb290Q2xhc3NOYW1lLFxuICAgICAgICByb290U3R5bGUgPSBfdGhpcyRwcm9wczEyLnJvb3RTdHlsZTtcbiAgICAgIHZhciBkb21Qcm9wcyA9IHBpY2tBdHRycyh0aGlzLnByb3BzLCB7XG4gICAgICAgIGFyaWE6IHRydWUsXG4gICAgICAgIGRhdGE6IHRydWVcbiAgICAgIH0pO1xuXG4gICAgICAvLyBJdCdzIGJldHRlciBtb3ZlIHRvIGhvb2tzIGJ1dCB3ZSBqdXN0IHNpbXBseSBrZWVwIGhlcmVcbiAgICAgIHZhciBkcmFnZ2FibGVDb25maWc7XG4gICAgICBpZiAoZHJhZ2dhYmxlKSB7XG4gICAgICAgIGlmIChfdHlwZW9mKGRyYWdnYWJsZSkgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgZHJhZ2dhYmxlQ29uZmlnID0gZHJhZ2dhYmxlO1xuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiBkcmFnZ2FibGUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICBkcmFnZ2FibGVDb25maWcgPSB7XG4gICAgICAgICAgICBub2RlRHJhZ2dhYmxlOiBkcmFnZ2FibGVcbiAgICAgICAgICB9O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGRyYWdnYWJsZUNvbmZpZyA9IHt9O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoVHJlZUNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICAgICAgdmFsdWU6IHtcbiAgICAgICAgICBwcmVmaXhDbHM6IHByZWZpeENscyxcbiAgICAgICAgICBzZWxlY3RhYmxlOiBzZWxlY3RhYmxlLFxuICAgICAgICAgIHNob3dJY29uOiBzaG93SWNvbixcbiAgICAgICAgICBpY29uOiBpY29uLFxuICAgICAgICAgIHN3aXRjaGVySWNvbjogc3dpdGNoZXJJY29uLFxuICAgICAgICAgIGRyYWdnYWJsZTogZHJhZ2dhYmxlQ29uZmlnLFxuICAgICAgICAgIGRyYWdnaW5nTm9kZUtleTogZHJhZ2dpbmdOb2RlS2V5LFxuICAgICAgICAgIGNoZWNrYWJsZTogY2hlY2thYmxlLFxuICAgICAgICAgIGNoZWNrU3RyaWN0bHk6IGNoZWNrU3RyaWN0bHksXG4gICAgICAgICAgZGlzYWJsZWQ6IGRpc2FibGVkLFxuICAgICAgICAgIGtleUVudGl0aWVzOiBrZXlFbnRpdGllcyxcbiAgICAgICAgICBkcm9wTGV2ZWxPZmZzZXQ6IGRyb3BMZXZlbE9mZnNldCxcbiAgICAgICAgICBkcm9wQ29udGFpbmVyS2V5OiBkcm9wQ29udGFpbmVyS2V5LFxuICAgICAgICAgIGRyb3BUYXJnZXRLZXk6IGRyb3BUYXJnZXRLZXksXG4gICAgICAgICAgZHJvcFBvc2l0aW9uOiBkcm9wUG9zaXRpb24sXG4gICAgICAgICAgZHJhZ092ZXJOb2RlS2V5OiBkcmFnT3Zlck5vZGVLZXksXG4gICAgICAgICAgaW5kZW50OiBpbmRlbnQsXG4gICAgICAgICAgZGlyZWN0aW9uOiBkaXJlY3Rpb24sXG4gICAgICAgICAgZHJvcEluZGljYXRvclJlbmRlcjogZHJvcEluZGljYXRvclJlbmRlcixcbiAgICAgICAgICBsb2FkRGF0YTogbG9hZERhdGEsXG4gICAgICAgICAgZmlsdGVyVHJlZU5vZGU6IGZpbHRlclRyZWVOb2RlLFxuICAgICAgICAgIHRpdGxlUmVuZGVyOiB0aXRsZVJlbmRlcixcbiAgICAgICAgICBvbk5vZGVDbGljazogdGhpcy5vbk5vZGVDbGljayxcbiAgICAgICAgICBvbk5vZGVEb3VibGVDbGljazogdGhpcy5vbk5vZGVEb3VibGVDbGljayxcbiAgICAgICAgICBvbk5vZGVFeHBhbmQ6IHRoaXMub25Ob2RlRXhwYW5kLFxuICAgICAgICAgIG9uTm9kZVNlbGVjdDogdGhpcy5vbk5vZGVTZWxlY3QsXG4gICAgICAgICAgb25Ob2RlQ2hlY2s6IHRoaXMub25Ob2RlQ2hlY2ssXG4gICAgICAgICAgb25Ob2RlTG9hZDogdGhpcy5vbk5vZGVMb2FkLFxuICAgICAgICAgIG9uTm9kZU1vdXNlRW50ZXI6IHRoaXMub25Ob2RlTW91c2VFbnRlcixcbiAgICAgICAgICBvbk5vZGVNb3VzZUxlYXZlOiB0aGlzLm9uTm9kZU1vdXNlTGVhdmUsXG4gICAgICAgICAgb25Ob2RlQ29udGV4dE1lbnU6IHRoaXMub25Ob2RlQ29udGV4dE1lbnUsXG4gICAgICAgICAgb25Ob2RlRHJhZ1N0YXJ0OiB0aGlzLm9uTm9kZURyYWdTdGFydCxcbiAgICAgICAgICBvbk5vZGVEcmFnRW50ZXI6IHRoaXMub25Ob2RlRHJhZ0VudGVyLFxuICAgICAgICAgIG9uTm9kZURyYWdPdmVyOiB0aGlzLm9uTm9kZURyYWdPdmVyLFxuICAgICAgICAgIG9uTm9kZURyYWdMZWF2ZTogdGhpcy5vbk5vZGVEcmFnTGVhdmUsXG4gICAgICAgICAgb25Ob2RlRHJhZ0VuZDogdGhpcy5vbk5vZGVEcmFnRW5kLFxuICAgICAgICAgIG9uTm9kZURyb3A6IHRoaXMub25Ob2RlRHJvcFxuICAgICAgICB9XG4gICAgICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICAgIHJvbGU6IFwidHJlZVwiLFxuICAgICAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMocHJlZml4Q2xzLCBjbGFzc05hbWUsIHJvb3RDbGFzc05hbWUsIF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXNob3ctbGluZVwiKSwgc2hvd0xpbmUpLCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWZvY3VzZWRcIiksIGZvY3VzZWQpLCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWFjdGl2ZS1mb2N1c2VkXCIpLCBhY3RpdmVLZXkgIT09IG51bGwpKSxcbiAgICAgICAgc3R5bGU6IHJvb3RTdHlsZVxuICAgICAgfSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTm9kZUxpc3QsIF9leHRlbmRzKHtcbiAgICAgICAgcmVmOiB0aGlzLmxpc3RSZWYsXG4gICAgICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgICAgICBzdHlsZTogc3R5bGUsXG4gICAgICAgIGRhdGE6IGZsYXR0ZW5Ob2RlcyxcbiAgICAgICAgZGlzYWJsZWQ6IGRpc2FibGVkLFxuICAgICAgICBzZWxlY3RhYmxlOiBzZWxlY3RhYmxlLFxuICAgICAgICBjaGVja2FibGU6ICEhY2hlY2thYmxlLFxuICAgICAgICBtb3Rpb246IG1vdGlvbixcbiAgICAgICAgZHJhZ2dpbmc6IGRyYWdnaW5nTm9kZUtleSAhPT0gbnVsbCxcbiAgICAgICAgaGVpZ2h0OiBoZWlnaHQsXG4gICAgICAgIGl0ZW1IZWlnaHQ6IGl0ZW1IZWlnaHQsXG4gICAgICAgIHZpcnR1YWw6IHZpcnR1YWwsXG4gICAgICAgIGZvY3VzYWJsZTogZm9jdXNhYmxlLFxuICAgICAgICBmb2N1c2VkOiBmb2N1c2VkLFxuICAgICAgICB0YWJJbmRleDogdGFiSW5kZXgsXG4gICAgICAgIGFjdGl2ZUl0ZW06IHRoaXMuZ2V0QWN0aXZlSXRlbSgpLFxuICAgICAgICBvbkZvY3VzOiB0aGlzLm9uRm9jdXMsXG4gICAgICAgIG9uQmx1cjogdGhpcy5vbkJsdXIsXG4gICAgICAgIG9uS2V5RG93bjogdGhpcy5vbktleURvd24sXG4gICAgICAgIG9uQWN0aXZlQ2hhbmdlOiB0aGlzLm9uQWN0aXZlQ2hhbmdlLFxuICAgICAgICBvbkxpc3RDaGFuZ2VTdGFydDogdGhpcy5vbkxpc3RDaGFuZ2VTdGFydCxcbiAgICAgICAgb25MaXN0Q2hhbmdlRW5kOiB0aGlzLm9uTGlzdENoYW5nZUVuZCxcbiAgICAgICAgb25Db250ZXh0TWVudTogb25Db250ZXh0TWVudSxcbiAgICAgICAgb25TY3JvbGw6IG9uU2Nyb2xsXG4gICAgICB9LCB0aGlzLmdldFRyZWVOb2RlUmVxdWlyZWRQcm9wcygpLCBkb21Qcm9wcykpKSk7XG4gICAgfVxuICB9XSwgW3tcbiAgICBrZXk6IFwiZ2V0RGVyaXZlZFN0YXRlRnJvbVByb3BzXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGdldERlcml2ZWRTdGF0ZUZyb21Qcm9wcyhwcm9wcywgcHJldlN0YXRlKSB7XG4gICAgICB2YXIgcHJldlByb3BzID0gcHJldlN0YXRlLnByZXZQcm9wcztcbiAgICAgIHZhciBuZXdTdGF0ZSA9IHtcbiAgICAgICAgcHJldlByb3BzOiBwcm9wc1xuICAgICAgfTtcbiAgICAgIGZ1bmN0aW9uIG5lZWRTeW5jKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuICFwcmV2UHJvcHMgJiYgcHJvcHMuaGFzT3duUHJvcGVydHkobmFtZSkgfHwgcHJldlByb3BzICYmIHByZXZQcm9wc1tuYW1lXSAhPT0gcHJvcHNbbmFtZV07XG4gICAgICB9XG5cbiAgICAgIC8vID09PT09PT09PT09PT09PT09PSBUcmVlIE5vZGUgPT09PT09PT09PT09PT09PT09XG4gICAgICB2YXIgdHJlZURhdGE7XG5cbiAgICAgIC8vIGZpZWxkTmFtZXNcbiAgICAgIHZhciBmaWVsZE5hbWVzID0gcHJldlN0YXRlLmZpZWxkTmFtZXM7XG4gICAgICBpZiAobmVlZFN5bmMoJ2ZpZWxkTmFtZXMnKSkge1xuICAgICAgICBmaWVsZE5hbWVzID0gZmlsbEZpZWxkTmFtZXMocHJvcHMuZmllbGROYW1lcyk7XG4gICAgICAgIG5ld1N0YXRlLmZpZWxkTmFtZXMgPSBmaWVsZE5hbWVzO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiBgdHJlZURhdGFgIG9yIGBjaGlsZHJlbmAgY2hhbmdlZCBhbmQgc2F2ZSBpbnRvIHRoZSBzdGF0ZS5cbiAgICAgIGlmIChuZWVkU3luYygndHJlZURhdGEnKSkge1xuICAgICAgICB0cmVlRGF0YSA9IHByb3BzLnRyZWVEYXRhO1xuICAgICAgfSBlbHNlIGlmIChuZWVkU3luYygnY2hpbGRyZW4nKSkge1xuICAgICAgICB3YXJuaW5nKGZhbHNlLCAnYGNoaWxkcmVuYCBvZiBUcmVlIGlzIGRlcHJlY2F0ZWQuIFBsZWFzZSB1c2UgYHRyZWVEYXRhYCBpbnN0ZWFkLicpO1xuICAgICAgICB0cmVlRGF0YSA9IGNvbnZlcnRUcmVlVG9EYXRhKHByb3BzLmNoaWxkcmVuKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2F2ZSBmbGF0dGVuIG5vZGVzIGluZm8gYW5kIGNvbnZlcnQgYHRyZWVEYXRhYCBpbnRvIGtleUVudGl0aWVzXG4gICAgICBpZiAodHJlZURhdGEpIHtcbiAgICAgICAgbmV3U3RhdGUudHJlZURhdGEgPSB0cmVlRGF0YTtcbiAgICAgICAgdmFyIGVudGl0aWVzTWFwID0gY29udmVydERhdGFUb0VudGl0aWVzKHRyZWVEYXRhLCB7XG4gICAgICAgICAgZmllbGROYW1lczogZmllbGROYW1lc1xuICAgICAgICB9KTtcbiAgICAgICAgbmV3U3RhdGUua2V5RW50aXRpZXMgPSBfb2JqZWN0U3ByZWFkKF9kZWZpbmVQcm9wZXJ0eSh7fSwgTU9USU9OX0tFWSwgTW90aW9uRW50aXR5KSwgZW50aXRpZXNNYXAua2V5RW50aXRpZXMpO1xuXG4gICAgICAgIC8vIFdhcm5pbmcgaWYgdHJlZU5vZGUgbm90IHByb3ZpZGUga2V5XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgd2FybmluZ1dpdGhvdXRLZXkodHJlZURhdGEsIGZpZWxkTmFtZXMpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICB2YXIga2V5RW50aXRpZXMgPSBuZXdTdGF0ZS5rZXlFbnRpdGllcyB8fCBwcmV2U3RhdGUua2V5RW50aXRpZXM7XG5cbiAgICAgIC8vID09PT09PT09PT09PT09PT0gZXhwYW5kZWRLZXlzID09PT09PT09PT09PT09PT09XG4gICAgICBpZiAobmVlZFN5bmMoJ2V4cGFuZGVkS2V5cycpIHx8IHByZXZQcm9wcyAmJiBuZWVkU3luYygnYXV0b0V4cGFuZFBhcmVudCcpKSB7XG4gICAgICAgIG5ld1N0YXRlLmV4cGFuZGVkS2V5cyA9IHByb3BzLmF1dG9FeHBhbmRQYXJlbnQgfHwgIXByZXZQcm9wcyAmJiBwcm9wcy5kZWZhdWx0RXhwYW5kUGFyZW50ID8gY29uZHVjdEV4cGFuZFBhcmVudChwcm9wcy5leHBhbmRlZEtleXMsIGtleUVudGl0aWVzKSA6IHByb3BzLmV4cGFuZGVkS2V5cztcbiAgICAgIH0gZWxzZSBpZiAoIXByZXZQcm9wcyAmJiBwcm9wcy5kZWZhdWx0RXhwYW5kQWxsKSB7XG4gICAgICAgIHZhciBjbG9uZUtleUVudGl0aWVzID0gX29iamVjdFNwcmVhZCh7fSwga2V5RW50aXRpZXMpO1xuICAgICAgICBkZWxldGUgY2xvbmVLZXlFbnRpdGllc1tNT1RJT05fS0VZXTtcblxuICAgICAgICAvLyBPbmx5IHRha2UgdGhlIGtleSB3aG8gaGFzIHRoZSBjaGlsZHJlbiB0byBlbmhhbmNlIHRoZSBwZXJmb3JtYW5jZVxuICAgICAgICB2YXIgbmV4dEV4cGFuZGVkS2V5cyA9IFtdO1xuICAgICAgICBPYmplY3Qua2V5cyhjbG9uZUtleUVudGl0aWVzKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgICB2YXIgZW50aXR5ID0gY2xvbmVLZXlFbnRpdGllc1trZXldO1xuICAgICAgICAgIGlmIChlbnRpdHkuY2hpbGRyZW4gJiYgZW50aXR5LmNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgICAgICAgbmV4dEV4cGFuZGVkS2V5cy5wdXNoKGVudGl0eS5rZXkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIG5ld1N0YXRlLmV4cGFuZGVkS2V5cyA9IG5leHRFeHBhbmRlZEtleXM7XG4gICAgICB9IGVsc2UgaWYgKCFwcmV2UHJvcHMgJiYgcHJvcHMuZGVmYXVsdEV4cGFuZGVkS2V5cykge1xuICAgICAgICBuZXdTdGF0ZS5leHBhbmRlZEtleXMgPSBwcm9wcy5hdXRvRXhwYW5kUGFyZW50IHx8IHByb3BzLmRlZmF1bHRFeHBhbmRQYXJlbnQgPyBjb25kdWN0RXhwYW5kUGFyZW50KHByb3BzLmRlZmF1bHRFeHBhbmRlZEtleXMsIGtleUVudGl0aWVzKSA6IHByb3BzLmRlZmF1bHRFeHBhbmRlZEtleXM7XG4gICAgICB9XG4gICAgICBpZiAoIW5ld1N0YXRlLmV4cGFuZGVkS2V5cykge1xuICAgICAgICBkZWxldGUgbmV3U3RhdGUuZXhwYW5kZWRLZXlzO1xuICAgICAgfVxuXG4gICAgICAvLyA9PT09PT09PT09PT09PT09IGZsYXR0ZW5Ob2RlcyA9PT09PT09PT09PT09PT09PVxuICAgICAgaWYgKHRyZWVEYXRhIHx8IG5ld1N0YXRlLmV4cGFuZGVkS2V5cykge1xuICAgICAgICB2YXIgZmxhdHRlbk5vZGVzID0gZmxhdHRlblRyZWVEYXRhKHRyZWVEYXRhIHx8IHByZXZTdGF0ZS50cmVlRGF0YSwgbmV3U3RhdGUuZXhwYW5kZWRLZXlzIHx8IHByZXZTdGF0ZS5leHBhbmRlZEtleXMsIGZpZWxkTmFtZXMpO1xuICAgICAgICBuZXdTdGF0ZS5mbGF0dGVuTm9kZXMgPSBmbGF0dGVuTm9kZXM7XG4gICAgICB9XG5cbiAgICAgIC8vID09PT09PT09PT09PT09PT0gc2VsZWN0ZWRLZXlzID09PT09PT09PT09PT09PT09XG4gICAgICBpZiAocHJvcHMuc2VsZWN0YWJsZSkge1xuICAgICAgICBpZiAobmVlZFN5bmMoJ3NlbGVjdGVkS2V5cycpKSB7XG4gICAgICAgICAgbmV3U3RhdGUuc2VsZWN0ZWRLZXlzID0gY2FsY1NlbGVjdGVkS2V5cyhwcm9wcy5zZWxlY3RlZEtleXMsIHByb3BzKTtcbiAgICAgICAgfSBlbHNlIGlmICghcHJldlByb3BzICYmIHByb3BzLmRlZmF1bHRTZWxlY3RlZEtleXMpIHtcbiAgICAgICAgICBuZXdTdGF0ZS5zZWxlY3RlZEtleXMgPSBjYWxjU2VsZWN0ZWRLZXlzKHByb3BzLmRlZmF1bHRTZWxlY3RlZEtleXMsIHByb3BzKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyA9PT09PT09PT09PT09PT09PSBjaGVja2VkS2V5cyA9PT09PT09PT09PT09PT09PVxuICAgICAgaWYgKHByb3BzLmNoZWNrYWJsZSkge1xuICAgICAgICB2YXIgY2hlY2tlZEtleUVudGl0eTtcbiAgICAgICAgaWYgKG5lZWRTeW5jKCdjaGVja2VkS2V5cycpKSB7XG4gICAgICAgICAgY2hlY2tlZEtleUVudGl0eSA9IHBhcnNlQ2hlY2tlZEtleXMocHJvcHMuY2hlY2tlZEtleXMpIHx8IHt9O1xuICAgICAgICB9IGVsc2UgaWYgKCFwcmV2UHJvcHMgJiYgcHJvcHMuZGVmYXVsdENoZWNrZWRLZXlzKSB7XG4gICAgICAgICAgY2hlY2tlZEtleUVudGl0eSA9IHBhcnNlQ2hlY2tlZEtleXMocHJvcHMuZGVmYXVsdENoZWNrZWRLZXlzKSB8fCB7fTtcbiAgICAgICAgfSBlbHNlIGlmICh0cmVlRGF0YSkge1xuICAgICAgICAgIC8vIElmIGB0cmVlRGF0YWAgY2hhbmdlZCwgd2UgYWxzbyBuZWVkIGNoZWNrIGl0XG4gICAgICAgICAgY2hlY2tlZEtleUVudGl0eSA9IHBhcnNlQ2hlY2tlZEtleXMocHJvcHMuY2hlY2tlZEtleXMpIHx8IHtcbiAgICAgICAgICAgIGNoZWNrZWRLZXlzOiBwcmV2U3RhdGUuY2hlY2tlZEtleXMsXG4gICAgICAgICAgICBoYWxmQ2hlY2tlZEtleXM6IHByZXZTdGF0ZS5oYWxmQ2hlY2tlZEtleXNcbiAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChjaGVja2VkS2V5RW50aXR5KSB7XG4gICAgICAgICAgdmFyIF9jaGVja2VkS2V5RW50aXR5ID0gY2hlY2tlZEtleUVudGl0eSxcbiAgICAgICAgICAgIF9jaGVja2VkS2V5RW50aXR5JGNoZSA9IF9jaGVja2VkS2V5RW50aXR5LmNoZWNrZWRLZXlzLFxuICAgICAgICAgICAgY2hlY2tlZEtleXMgPSBfY2hlY2tlZEtleUVudGl0eSRjaGUgPT09IHZvaWQgMCA/IFtdIDogX2NoZWNrZWRLZXlFbnRpdHkkY2hlLFxuICAgICAgICAgICAgX2NoZWNrZWRLZXlFbnRpdHkkaGFsID0gX2NoZWNrZWRLZXlFbnRpdHkuaGFsZkNoZWNrZWRLZXlzLFxuICAgICAgICAgICAgaGFsZkNoZWNrZWRLZXlzID0gX2NoZWNrZWRLZXlFbnRpdHkkaGFsID09PSB2b2lkIDAgPyBbXSA6IF9jaGVja2VkS2V5RW50aXR5JGhhbDtcbiAgICAgICAgICBpZiAoIXByb3BzLmNoZWNrU3RyaWN0bHkpIHtcbiAgICAgICAgICAgIHZhciBjb25kdWN0S2V5cyA9IGNvbmR1Y3RDaGVjayhjaGVja2VkS2V5cywgdHJ1ZSwga2V5RW50aXRpZXMpO1xuICAgICAgICAgICAgY2hlY2tlZEtleXMgPSBjb25kdWN0S2V5cy5jaGVja2VkS2V5cztcbiAgICAgICAgICAgIGhhbGZDaGVja2VkS2V5cyA9IGNvbmR1Y3RLZXlzLmhhbGZDaGVja2VkS2V5cztcbiAgICAgICAgICB9XG4gICAgICAgICAgbmV3U3RhdGUuY2hlY2tlZEtleXMgPSBjaGVja2VkS2V5cztcbiAgICAgICAgICBuZXdTdGF0ZS5oYWxmQ2hlY2tlZEtleXMgPSBoYWxmQ2hlY2tlZEtleXM7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gPT09PT09PT09PT09PT09PT0gbG9hZGVkS2V5cyA9PT09PT09PT09PT09PT09PT1cbiAgICAgIGlmIChuZWVkU3luYygnbG9hZGVkS2V5cycpKSB7XG4gICAgICAgIG5ld1N0YXRlLmxvYWRlZEtleXMgPSBwcm9wcy5sb2FkZWRLZXlzO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG5ld1N0YXRlO1xuICAgIH1cbiAgfV0pO1xuICByZXR1cm4gVHJlZTtcbn0oUmVhY3QuQ29tcG9uZW50KTtcbl9kZWZpbmVQcm9wZXJ0eShUcmVlLCBcImRlZmF1bHRQcm9wc1wiLCB7XG4gIHByZWZpeENsczogJ3JjLXRyZWUnLFxuICBzaG93TGluZTogZmFsc2UsXG4gIHNob3dJY29uOiB0cnVlLFxuICBzZWxlY3RhYmxlOiB0cnVlLFxuICBtdWx0aXBsZTogZmFsc2UsXG4gIGNoZWNrYWJsZTogZmFsc2UsXG4gIGRpc2FibGVkOiBmYWxzZSxcbiAgY2hlY2tTdHJpY3RseTogZmFsc2UsXG4gIGRyYWdnYWJsZTogZmFsc2UsXG4gIGRlZmF1bHRFeHBhbmRQYXJlbnQ6IHRydWUsXG4gIGF1dG9FeHBhbmRQYXJlbnQ6IGZhbHNlLFxuICBkZWZhdWx0RXhwYW5kQWxsOiBmYWxzZSxcbiAgZGVmYXVsdEV4cGFuZGVkS2V5czogW10sXG4gIGRlZmF1bHRDaGVja2VkS2V5czogW10sXG4gIGRlZmF1bHRTZWxlY3RlZEtleXM6IFtdLFxuICBkcm9wSW5kaWNhdG9yUmVuZGVyOiBEcm9wSW5kaWNhdG9yLFxuICBhbGxvd0Ryb3A6IGZ1bmN0aW9uIGFsbG93RHJvcCgpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSxcbiAgZXhwYW5kQWN0aW9uOiBmYWxzZVxufSk7XG5fZGVmaW5lUHJvcGVydHkoVHJlZSwgXCJUcmVlTm9kZVwiLCBUcmVlTm9kZSk7XG5leHBvcnQgZGVmYXVsdCBUcmVlOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/Tree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/TreeNode.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-tree/es/TreeNode.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _contextTypes__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./contextTypes */ \"(ssr)/./node_modules/rc-tree/es/contextTypes.js\");\n/* harmony import */ var _Indent__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Indent */ \"(ssr)/./node_modules/rc-tree/es/Indent.js\");\n/* harmony import */ var _utils_keyUtil__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/keyUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\");\n/* harmony import */ var _utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/treeUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\");\n\n\n\n\n\n\n\n\n\nvar _excluded = [\"eventKey\", \"className\", \"style\", \"dragOver\", \"dragOverGapTop\", \"dragOverGapBottom\", \"isLeaf\", \"isStart\", \"isEnd\", \"expanded\", \"selected\", \"checked\", \"halfChecked\", \"loading\", \"domRef\", \"active\", \"data\", \"onMouseMove\", \"selectable\"];\n\n\n\n// @ts-ignore\n\n\n\n\nvar ICON_OPEN = 'open';\nvar ICON_CLOSE = 'close';\nvar defaultTitle = '---';\nvar InternalTreeNode = /*#__PURE__*/function (_React$Component) {\n  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(InternalTreeNode, _React$Component);\n  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(InternalTreeNode);\n  function InternalTreeNode() {\n    var _this;\n    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, InternalTreeNode);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"state\", {\n      dragNodeHighlight: false\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"selectHandle\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"cacheIndent\", void 0);\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onSelectorClick\", function (e) {\n      // Click trigger before select/check operation\n      var onNodeClick = _this.props.context.onNodeClick;\n      onNodeClick(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n      if (_this.isSelectable()) {\n        _this.onSelect(e);\n      } else {\n        _this.onCheck(e);\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onSelectorDoubleClick\", function (e) {\n      var onNodeDoubleClick = _this.props.context.onNodeDoubleClick;\n      onNodeDoubleClick(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onSelect\", function (e) {\n      if (_this.isDisabled()) return;\n      var onNodeSelect = _this.props.context.onNodeSelect;\n      onNodeSelect(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onCheck\", function (e) {\n      if (_this.isDisabled()) return;\n      var _this$props = _this.props,\n        disableCheckbox = _this$props.disableCheckbox,\n        checked = _this$props.checked;\n      var onNodeCheck = _this.props.context.onNodeCheck;\n      if (!_this.isCheckable() || disableCheckbox) return;\n      var targetChecked = !checked;\n      onNodeCheck(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props), targetChecked);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onMouseEnter\", function (e) {\n      var onNodeMouseEnter = _this.props.context.onNodeMouseEnter;\n      onNodeMouseEnter(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onMouseLeave\", function (e) {\n      var onNodeMouseLeave = _this.props.context.onNodeMouseLeave;\n      onNodeMouseLeave(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onContextMenu\", function (e) {\n      var onNodeContextMenu = _this.props.context.onNodeContextMenu;\n      onNodeContextMenu(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDragStart\", function (e) {\n      var onNodeDragStart = _this.props.context.onNodeDragStart;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: true\n      });\n      onNodeDragStart(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n      try {\n        // ie throw error\n        // firefox-need-it\n        e.dataTransfer.setData('text/plain', '');\n      } catch (error) {\n        // empty\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDragEnter\", function (e) {\n      var onNodeDragEnter = _this.props.context.onNodeDragEnter;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragEnter(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDragOver\", function (e) {\n      var onNodeDragOver = _this.props.context.onNodeDragOver;\n      e.preventDefault();\n      e.stopPropagation();\n      onNodeDragOver(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDragLeave\", function (e) {\n      var onNodeDragLeave = _this.props.context.onNodeDragLeave;\n      e.stopPropagation();\n      onNodeDragLeave(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDragEnd\", function (e) {\n      var onNodeDragEnd = _this.props.context.onNodeDragEnd;\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDragEnd(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onDrop\", function (e) {\n      var onNodeDrop = _this.props.context.onNodeDrop;\n      e.preventDefault();\n      e.stopPropagation();\n      _this.setState({\n        dragNodeHighlight: false\n      });\n      onNodeDrop(e, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this));\n    });\n    // Disabled item still can be switch\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"onExpand\", function (e) {\n      var _this$props2 = _this.props,\n        loading = _this$props2.loading,\n        onNodeExpand = _this$props2.context.onNodeExpand;\n      if (loading) return;\n      onNodeExpand(e, (0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n    });\n    // Drag usage\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"setSelectHandle\", function (node) {\n      _this.selectHandle = node;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"getNodeState\", function () {\n      var expanded = _this.props.expanded;\n      if (_this.isLeaf()) {\n        return null;\n      }\n      return expanded ? ICON_OPEN : ICON_CLOSE;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"hasChildren\", function () {\n      var eventKey = _this.props.eventKey;\n      var keyEntities = _this.props.context.keyEntities;\n      var _ref = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(keyEntities, eventKey) || {},\n        children = _ref.children;\n      return !!(children || []).length;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"isLeaf\", function () {\n      var _this$props3 = _this.props,\n        isLeaf = _this$props3.isLeaf,\n        loaded = _this$props3.loaded;\n      var loadData = _this.props.context.loadData;\n      var hasChildren = _this.hasChildren();\n      if (isLeaf === false) {\n        return false;\n      }\n      return isLeaf || !loadData && !hasChildren || loadData && loaded && !hasChildren;\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"isDisabled\", function () {\n      var disabled = _this.props.disabled;\n      var treeDisabled = _this.props.context.disabled;\n      return !!(treeDisabled || disabled);\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"isCheckable\", function () {\n      var checkable = _this.props.checkable;\n      var treeCheckable = _this.props.context.checkable;\n\n      // Return false if tree or treeNode is not checkable\n      if (!treeCheckable || checkable === false) return false;\n      return treeCheckable;\n    });\n    // Load data to avoid default expanded tree without data\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"syncLoadData\", function (props) {\n      var expanded = props.expanded,\n        loading = props.loading,\n        loaded = props.loaded;\n      var _this$props$context = _this.props.context,\n        loadData = _this$props$context.loadData,\n        onNodeLoad = _this$props$context.onNodeLoad;\n      if (loading) {\n        return;\n      }\n\n      // read from state to avoid loadData at same time\n      if (loadData && expanded && !_this.isLeaf() && !loaded) {\n        // We needn't reload data when has children in sync logic\n        // It's only needed in node expanded\n        onNodeLoad((0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(_this.props));\n      }\n    });\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"isDraggable\", function () {\n      var _this$props4 = _this.props,\n        data = _this$props4.data,\n        draggable = _this$props4.context.draggable;\n      return !!(draggable && (!draggable.nodeDraggable || draggable.nodeDraggable(data)));\n    });\n    // ==================== Render: Drag Handler ====================\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderDragHandler\", function () {\n      var _this$props$context2 = _this.props.context,\n        draggable = _this$props$context2.draggable,\n        prefixCls = _this$props$context2.prefixCls;\n      return draggable !== null && draggable !== void 0 && draggable.icon ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-draggable-icon\")\n      }, draggable.icon) : null;\n    });\n    // ====================== Render: Switcher ======================\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderSwitcherIconDom\", function (isLeaf) {\n      var switcherIconFromProps = _this.props.switcherIcon;\n      var switcherIconFromCtx = _this.props.context.switcherIcon;\n      var switcherIcon = switcherIconFromProps || switcherIconFromCtx;\n      // if switcherIconDom is null, no render switcher span\n      if (typeof switcherIcon === 'function') {\n        return switcherIcon((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, _this.props), {}, {\n          isLeaf: isLeaf\n        }));\n      }\n      return switcherIcon;\n    });\n    // Switcher\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderSwitcher\", function () {\n      var expanded = _this.props.expanded;\n      var prefixCls = _this.props.context.prefixCls;\n      if (_this.isLeaf()) {\n        // if switcherIconDom is null, no render switcher span\n        var _switcherIconDom = _this.renderSwitcherIconDom(true);\n        return _switcherIconDom !== false ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n          className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher-noop\"))\n        }, _switcherIconDom) : null;\n      }\n      var switcherCls = classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(prefixCls, \"-switcher\"), \"\".concat(prefixCls, \"-switcher_\").concat(expanded ? ICON_OPEN : ICON_CLOSE));\n      var switcherIconDom = _this.renderSwitcherIconDom(false);\n      return switcherIconDom !== false ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        onClick: _this.onExpand,\n        className: switcherCls\n      }, switcherIconDom) : null;\n    });\n    // ====================== Render: Checkbox ======================\n    // Checkbox\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderCheckbox\", function () {\n      var _this$props5 = _this.props,\n        checked = _this$props5.checked,\n        halfChecked = _this$props5.halfChecked,\n        disableCheckbox = _this$props5.disableCheckbox;\n      var prefixCls = _this.props.context.prefixCls;\n      var disabled = _this.isDisabled();\n      var checkable = _this.isCheckable();\n      if (!checkable) return null;\n\n      // [Legacy] Custom element should be separate with `checkable` in future\n      var $custom = typeof checkable !== 'boolean' ? checkable : null;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(prefixCls, \"-checkbox\"), checked && \"\".concat(prefixCls, \"-checkbox-checked\"), !checked && halfChecked && \"\".concat(prefixCls, \"-checkbox-indeterminate\"), (disabled || disableCheckbox) && \"\".concat(prefixCls, \"-checkbox-disabled\")),\n        onClick: _this.onCheck\n      }, $custom);\n    });\n    // ==================== Render: Title + Icon ====================\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderIcon\", function () {\n      var loading = _this.props.loading;\n      var prefixCls = _this.props.context.prefixCls;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__\").concat(_this.getNodeState() || 'docu'), loading && \"\".concat(prefixCls, \"-icon_loading\"))\n      });\n    });\n    // Icon + Title\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderSelector\", function () {\n      var dragNodeHighlight = _this.state.dragNodeHighlight;\n      var _this$props6 = _this.props,\n        _this$props6$title = _this$props6.title,\n        title = _this$props6$title === void 0 ? defaultTitle : _this$props6$title,\n        selected = _this$props6.selected,\n        icon = _this$props6.icon,\n        loading = _this$props6.loading,\n        data = _this$props6.data;\n      var _this$props$context3 = _this.props.context,\n        prefixCls = _this$props$context3.prefixCls,\n        showIcon = _this$props$context3.showIcon,\n        treeIcon = _this$props$context3.icon,\n        loadData = _this$props$context3.loadData,\n        titleRender = _this$props$context3.titleRender;\n      var disabled = _this.isDisabled();\n      var wrapClass = \"\".concat(prefixCls, \"-node-content-wrapper\");\n\n      // Icon - Still show loading icon when loading without showIcon\n      var $icon;\n      if (showIcon) {\n        var currentIcon = icon || treeIcon;\n        $icon = currentIcon ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n          className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(prefixCls, \"-iconEle\"), \"\".concat(prefixCls, \"-icon__customize\"))\n        }, typeof currentIcon === 'function' ? currentIcon(_this.props) : currentIcon) : _this.renderIcon();\n      } else if (loadData && loading) {\n        $icon = _this.renderIcon();\n      }\n\n      // Title\n      var titleNode;\n      if (typeof title === 'function') {\n        titleNode = title(data);\n      } else if (titleRender) {\n        titleNode = titleRender(data);\n      } else {\n        titleNode = title;\n      }\n      var $title = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-title\")\n      }, titleNode);\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"span\", {\n        ref: _this.setSelectHandle,\n        title: typeof title === 'string' ? title : '',\n        className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(\"\".concat(wrapClass), \"\".concat(wrapClass, \"-\").concat(_this.getNodeState() || 'normal'), !disabled && (selected || dragNodeHighlight) && \"\".concat(prefixCls, \"-node-selected\")),\n        onMouseEnter: _this.onMouseEnter,\n        onMouseLeave: _this.onMouseLeave,\n        onContextMenu: _this.onContextMenu,\n        onClick: _this.onSelectorClick,\n        onDoubleClick: _this.onSelectorDoubleClick\n      }, $icon, $title, _this.renderDropIndicator());\n    });\n    // =================== Render: Drop Indicator ===================\n    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_this), \"renderDropIndicator\", function () {\n      var _this$props7 = _this.props,\n        disabled = _this$props7.disabled,\n        eventKey = _this$props7.eventKey;\n      var _this$props$context4 = _this.props.context,\n        draggable = _this$props$context4.draggable,\n        dropLevelOffset = _this$props$context4.dropLevelOffset,\n        dropPosition = _this$props$context4.dropPosition,\n        prefixCls = _this$props$context4.prefixCls,\n        indent = _this$props$context4.indent,\n        dropIndicatorRender = _this$props$context4.dropIndicatorRender,\n        dragOverNodeKey = _this$props$context4.dragOverNodeKey,\n        direction = _this$props$context4.direction;\n      var rootDraggable = !!draggable;\n      // allowDrop is calculated in Tree.tsx, there is no need for calc it here\n      var showIndicator = !disabled && rootDraggable && dragOverNodeKey === eventKey;\n\n      // This is a hot fix which is already fixed in\n      // https://github.com/react-component/tree/pull/743/files\n      // But some case need break point so we hack on this\n      // ref https://github.com/ant-design/ant-design/issues/43493\n      var mergedIndent = indent !== null && indent !== void 0 ? indent : _this.cacheIndent;\n      _this.cacheIndent = indent;\n      return showIndicator ? dropIndicatorRender({\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        indent: mergedIndent,\n        prefixCls: prefixCls,\n        direction: direction\n      }) : null;\n    });\n    return _this;\n  }\n  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(InternalTreeNode, [{\n    key: \"componentDidMount\",\n    value:\n    // Isomorphic needn't load data in server side\n    function componentDidMount() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.syncLoadData(this.props);\n    }\n  }, {\n    key: \"isSelectable\",\n    value: function isSelectable() {\n      var selectable = this.props.selectable;\n      var treeSelectable = this.props.context.selectable;\n\n      // Ignore when selectable is undefined or null\n      if (typeof selectable === 'boolean') {\n        return selectable;\n      }\n      return treeSelectable;\n    }\n  }, {\n    key: \"render\",\n    value:\n    // =========================== Render ===========================\n    function render() {\n      var _classNames;\n      var _this$props8 = this.props,\n        eventKey = _this$props8.eventKey,\n        className = _this$props8.className,\n        style = _this$props8.style,\n        dragOver = _this$props8.dragOver,\n        dragOverGapTop = _this$props8.dragOverGapTop,\n        dragOverGapBottom = _this$props8.dragOverGapBottom,\n        isLeaf = _this$props8.isLeaf,\n        isStart = _this$props8.isStart,\n        isEnd = _this$props8.isEnd,\n        expanded = _this$props8.expanded,\n        selected = _this$props8.selected,\n        checked = _this$props8.checked,\n        halfChecked = _this$props8.halfChecked,\n        loading = _this$props8.loading,\n        domRef = _this$props8.domRef,\n        active = _this$props8.active,\n        data = _this$props8.data,\n        onMouseMove = _this$props8.onMouseMove,\n        selectable = _this$props8.selectable,\n        otherProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this$props8, _excluded);\n      var _this$props$context5 = this.props.context,\n        prefixCls = _this$props$context5.prefixCls,\n        filterTreeNode = _this$props$context5.filterTreeNode,\n        keyEntities = _this$props$context5.keyEntities,\n        dropContainerKey = _this$props$context5.dropContainerKey,\n        dropTargetKey = _this$props$context5.dropTargetKey,\n        draggingNodeKey = _this$props$context5.draggingNodeKey;\n      var disabled = this.isDisabled();\n      var dataOrAriaAttributeProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(otherProps, {\n        aria: true,\n        data: true\n      });\n      var _ref2 = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(keyEntities, eventKey) || {},\n        level = _ref2.level;\n      var isEndNode = isEnd[isEnd.length - 1];\n      var mergedDraggable = this.isDraggable();\n      var draggableWithoutDisabled = !disabled && mergedDraggable;\n      var dragging = draggingNodeKey === eventKey;\n      var ariaSelected = selectable !== undefined ? {\n        'aria-selected': !!selectable\n      } : undefined;\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: domRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_9___default()(className, \"\".concat(prefixCls, \"-treenode\"), (_classNames = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_classNames, \"\".concat(prefixCls, \"-treenode-disabled\"), disabled), \"\".concat(prefixCls, \"-treenode-switcher-\").concat(expanded ? 'open' : 'close'), !isLeaf), \"\".concat(prefixCls, \"-treenode-checkbox-checked\"), checked), \"\".concat(prefixCls, \"-treenode-checkbox-indeterminate\"), halfChecked), \"\".concat(prefixCls, \"-treenode-selected\"), selected), \"\".concat(prefixCls, \"-treenode-loading\"), loading), \"\".concat(prefixCls, \"-treenode-active\"), active), \"\".concat(prefixCls, \"-treenode-leaf-last\"), isEndNode), \"\".concat(prefixCls, \"-treenode-draggable\"), mergedDraggable), \"dragging\", dragging), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(_classNames, 'drop-target', dropTargetKey === eventKey), 'drop-container', dropContainerKey === eventKey), 'drag-over', !disabled && dragOver), 'drag-over-gap-top', !disabled && dragOverGapTop), 'drag-over-gap-bottom', !disabled && dragOverGapBottom), 'filter-node', filterTreeNode && filterTreeNode((0,_utils_treeUtil__WEBPACK_IMPORTED_MODULE_15__.convertNodePropsToEventData)(this.props))))),\n        style: style\n        // Draggable config\n        ,\n        draggable: draggableWithoutDisabled,\n        \"aria-grabbed\": dragging,\n        onDragStart: draggableWithoutDisabled ? this.onDragStart : undefined\n        // Drop config\n        ,\n        onDragEnter: mergedDraggable ? this.onDragEnter : undefined,\n        onDragOver: mergedDraggable ? this.onDragOver : undefined,\n        onDragLeave: mergedDraggable ? this.onDragLeave : undefined,\n        onDrop: mergedDraggable ? this.onDrop : undefined,\n        onDragEnd: mergedDraggable ? this.onDragEnd : undefined,\n        onMouseMove: onMouseMove\n      }, ariaSelected, dataOrAriaAttributeProps), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_Indent__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        prefixCls: prefixCls,\n        level: level,\n        isStart: isStart,\n        isEnd: isEnd\n      }), this.renderDragHandler(), this.renderSwitcher(), this.renderCheckbox(), this.renderSelector());\n    }\n  }]);\n  return InternalTreeNode;\n}(react__WEBPACK_IMPORTED_MODULE_11__.Component);\nvar ContextTreeNode = function ContextTreeNode(props) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(_contextTypes__WEBPACK_IMPORTED_MODULE_12__.TreeContext.Consumer, null, function (context) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_11__.createElement(InternalTreeNode, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n      context: context\n    }));\n  });\n};\nContextTreeNode.displayName = 'TreeNode';\nContextTreeNode.isTreeNode = 1;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContextTreeNode);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/TreeNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/contextTypes.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-tree/es/contextTypes.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeContext: () => (/* binding */ TreeContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Webpack has bug for import loop, which is not the same behavior as ES module.\n * When util.js imports the TreeNode for tree generate will cause treeContextTypes be empty.\n */\n\nvar TreeContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9jb250ZXh0VHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDK0I7QUFDeEIsK0JBQStCLGdEQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10cmVlL2VzL2NvbnRleHRUeXBlcy5qcz9hMDhmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogV2VicGFjayBoYXMgYnVnIGZvciBpbXBvcnQgbG9vcCwgd2hpY2ggaXMgbm90IHRoZSBzYW1lIGJlaGF2aW9yIGFzIEVTIG1vZHVsZS5cbiAqIFdoZW4gdXRpbC5qcyBpbXBvcnRzIHRoZSBUcmVlTm9kZSBmb3IgdHJlZSBnZW5lcmF0ZSB3aWxsIGNhdXNlIHRyZWVDb250ZXh0VHlwZXMgYmUgZW1wdHkuXG4gKi9cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgVHJlZUNvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dChudWxsKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/contextTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/index.js":
/*!******************************************!*\
  !*** ./node_modules/rc-tree/es/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeNode: () => (/* reexport safe */ _TreeNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tree */ \"(ssr)/./node_modules/rc-tree/es/Tree.js\");\n/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TreeNode */ \"(ssr)/./node_modules/rc-tree/es/TreeNode.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Tree__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTBCO0FBQ1E7QUFDZDtBQUNwQixpRUFBZSw2Q0FBSSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10cmVlL2VzL2luZGV4LmpzP2E4ODAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFRyZWUgZnJvbSBcIi4vVHJlZVwiO1xuaW1wb3J0IFRyZWVOb2RlIGZyb20gXCIuL1RyZWVOb2RlXCI7XG5leHBvcnQgeyBUcmVlTm9kZSB9O1xuZXhwb3J0IGRlZmF1bHQgVHJlZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/useUnmount.js":
/*!***********************************************!*\
  !*** ./node_modules/rc-tree/es/useUnmount.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useUnmount)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n\n\n\n\n/**\n * Trigger only when component unmount\n */\nfunction useUnmount(triggerStart, triggerEnd) {\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(false),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    firstMount = _React$useState2[0],\n    setFirstMount = _React$useState2[1];\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function () {\n    if (firstMount) {\n      triggerStart();\n      return function () {\n        triggerEnd();\n      };\n    }\n  }, [firstMount]);\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function () {\n    setFirstMount(true);\n    return function () {\n      setFirstMount(false);\n    };\n  }, []);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy91c2VVbm1vdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNFO0FBQ3ZDO0FBQ2dDOztBQUUvRDtBQUNBO0FBQ0E7QUFDZTtBQUNmLHdCQUF3QiwyQ0FBYztBQUN0Qyx1QkFBdUIsb0ZBQWM7QUFDckM7QUFDQTtBQUNBLEVBQUUsNEVBQWU7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsNEVBQWU7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy91c2VVbm1vdW50LmpzPzk5NTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdXNlTGF5b3V0RWZmZWN0IGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZUxheW91dEVmZmVjdFwiO1xuXG4vKipcbiAqIFRyaWdnZXIgb25seSB3aGVuIGNvbXBvbmVudCB1bm1vdW50XG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVVubW91bnQodHJpZ2dlclN0YXJ0LCB0cmlnZ2VyRW5kKSB7XG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZShmYWxzZSksXG4gICAgX1JlYWN0JHVzZVN0YXRlMiA9IF9zbGljZWRUb0FycmF5KF9SZWFjdCR1c2VTdGF0ZSwgMiksXG4gICAgZmlyc3RNb3VudCA9IF9SZWFjdCR1c2VTdGF0ZTJbMF0sXG4gICAgc2V0Rmlyc3RNb3VudCA9IF9SZWFjdCR1c2VTdGF0ZTJbMV07XG4gIHVzZUxheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKGZpcnN0TW91bnQpIHtcbiAgICAgIHRyaWdnZXJTdGFydCgpO1xuICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdHJpZ2dlckVuZCgpO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtmaXJzdE1vdW50XSk7XG4gIHVzZUxheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgc2V0Rmlyc3RNb3VudCh0cnVlKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgc2V0Rmlyc3RNb3VudChmYWxzZSk7XG4gICAgfTtcbiAgfSwgW10pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/useUnmount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/util.js":
/*!*****************************************!*\
  !*** ./node_modules/rc-tree/es/util.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrAdd: () => (/* binding */ arrAdd),\n/* harmony export */   arrDel: () => (/* binding */ arrDel),\n/* harmony export */   calcDropPosition: () => (/* binding */ calcDropPosition),\n/* harmony export */   calcSelectedKeys: () => (/* binding */ calcSelectedKeys),\n/* harmony export */   conductExpandParent: () => (/* binding */ conductExpandParent),\n/* harmony export */   convertDataToTree: () => (/* binding */ convertDataToTree),\n/* harmony export */   getDragChildrenKeys: () => (/* binding */ getDragChildrenKeys),\n/* harmony export */   getPosition: () => (/* reexport safe */ _utils_treeUtil__WEBPACK_IMPORTED_MODULE_8__.getPosition),\n/* harmony export */   isFirstChild: () => (/* binding */ isFirstChild),\n/* harmony export */   isLastChild: () => (/* binding */ isLastChild),\n/* harmony export */   isTreeNode: () => (/* reexport safe */ _utils_treeUtil__WEBPACK_IMPORTED_MODULE_8__.isTreeNode),\n/* harmony export */   parseCheckedKeys: () => (/* binding */ parseCheckedKeys),\n/* harmony export */   posToArr: () => (/* binding */ posToArr)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _TreeNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TreeNode */ \"(ssr)/./node_modules/rc-tree/es/TreeNode.js\");\n/* harmony import */ var _utils_keyUtil__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/keyUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\");\n/* harmony import */ var _utils_treeUtil__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/treeUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\");\n\n\n\n\nvar _excluded = [\"children\"];\n/* eslint-disable no-lonely-if */\n/**\n * Legacy code. Should avoid to use if you are new to import these code.\n */\n\n\n\n\n\n\nfunction arrDel(list, value) {\n  if (!list) return [];\n  var clone = list.slice();\n  var index = clone.indexOf(value);\n  if (index >= 0) {\n    clone.splice(index, 1);\n  }\n  return clone;\n}\nfunction arrAdd(list, value) {\n  var clone = (list || []).slice();\n  if (clone.indexOf(value) === -1) {\n    clone.push(value);\n  }\n  return clone;\n}\nfunction posToArr(pos) {\n  return pos.split('-');\n}\nfunction getDragChildrenKeys(dragNodeKey, keyEntities) {\n  // not contains self\n  // self for left or right drag\n  var dragChildrenKeys = [];\n  var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(keyEntities, dragNodeKey);\n  function dig() {\n    var list = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    list.forEach(function (_ref) {\n      var key = _ref.key,\n        children = _ref.children;\n      dragChildrenKeys.push(key);\n      dig(children);\n    });\n  }\n  dig(entity.children);\n  return dragChildrenKeys;\n}\nfunction isLastChild(treeNodeEntity) {\n  if (treeNodeEntity.parent) {\n    var posArr = posToArr(treeNodeEntity.pos);\n    return Number(posArr[posArr.length - 1]) === treeNodeEntity.parent.children.length - 1;\n  }\n  return false;\n}\nfunction isFirstChild(treeNodeEntity) {\n  var posArr = posToArr(treeNodeEntity.pos);\n  return Number(posArr[posArr.length - 1]) === 0;\n}\n\n// Only used when drag, not affect SSR.\nfunction calcDropPosition(event, dragNode, targetNode, indent, startMousePosition, allowDrop, flattenedNodes, keyEntities, expandKeys, direction) {\n  var _abstractDropNodeEnti;\n  var clientX = event.clientX,\n    clientY = event.clientY;\n  var _getBoundingClientRec = event.target.getBoundingClientRect(),\n    top = _getBoundingClientRec.top,\n    height = _getBoundingClientRec.height;\n  // optional chain for testing\n  var horizontalMouseOffset = (direction === 'rtl' ? -1 : 1) * (((startMousePosition === null || startMousePosition === void 0 ? void 0 : startMousePosition.x) || 0) - clientX);\n  var rawDropLevelOffset = (horizontalMouseOffset - 12) / indent;\n\n  // Filter the expanded keys to exclude the node that not has children currently (like async nodes).\n  var filteredExpandKeys = expandKeys.filter(function (key) {\n    var _keyEntities$key;\n    return (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 || (_keyEntities$key = _keyEntities$key.children) === null || _keyEntities$key === void 0 ? void 0 : _keyEntities$key.length;\n  });\n\n  // find abstract drop node by horizontal offset\n  var abstractDropNodeEntity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(keyEntities, targetNode.props.eventKey);\n  if (clientY < top + height / 2) {\n    // first half, set abstract drop node to previous node\n    var nodeIndex = flattenedNodes.findIndex(function (flattenedNode) {\n      return flattenedNode.key === abstractDropNodeEntity.key;\n    });\n    var prevNodeIndex = nodeIndex <= 0 ? 0 : nodeIndex - 1;\n    var prevNodeKey = flattenedNodes[prevNodeIndex].key;\n    abstractDropNodeEntity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(keyEntities, prevNodeKey);\n  }\n  var initialAbstractDropNodeKey = abstractDropNodeEntity.key;\n  var abstractDragOverEntity = abstractDropNodeEntity;\n  var dragOverNodeKey = abstractDropNodeEntity.key;\n  var dropPosition = 0;\n  var dropLevelOffset = 0;\n\n  // Only allow cross level drop when dragging on a non-expanded node\n  if (!filteredExpandKeys.includes(initialAbstractDropNodeKey)) {\n    for (var i = 0; i < rawDropLevelOffset; i += 1) {\n      if (isLastChild(abstractDropNodeEntity)) {\n        abstractDropNodeEntity = abstractDropNodeEntity.parent;\n        dropLevelOffset += 1;\n      } else {\n        break;\n      }\n    }\n  }\n  var abstractDragDataNode = dragNode.props.data;\n  var abstractDropDataNode = abstractDropNodeEntity.node;\n  var dropAllowed = true;\n  if (isFirstChild(abstractDropNodeEntity) && abstractDropNodeEntity.level === 0 && clientY < top + height / 2 && allowDrop({\n    dragNode: abstractDragDataNode,\n    dropNode: abstractDropDataNode,\n    dropPosition: -1\n  }) && abstractDropNodeEntity.key === targetNode.props.eventKey) {\n    // first half of first node in first level\n    dropPosition = -1;\n  } else if ((abstractDragOverEntity.children || []).length && filteredExpandKeys.includes(dragOverNodeKey)) {\n    // drop on expanded node\n    // only allow drop inside\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 0\n    })) {\n      dropPosition = 0;\n    } else {\n      dropAllowed = false;\n    }\n  } else if (dropLevelOffset === 0) {\n    if (rawDropLevelOffset > -1.5) {\n      // | Node     | <- abstractDropNode\n      // | -^-===== | <- mousePosition\n      // 1. try drop after\n      // 2. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    } else {\n      // | Node     | <- abstractDropNode\n      // | ---==^== | <- mousePosition\n      // whether it has children or doesn't has children\n      // always\n      // 1. try drop inside\n      // 2. try drop after\n      // 3. do not allow drop\n      if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 0\n      })) {\n        dropPosition = 0;\n      } else if (allowDrop({\n        dragNode: abstractDragDataNode,\n        dropNode: abstractDropDataNode,\n        dropPosition: 1\n      })) {\n        dropPosition = 1;\n      } else {\n        dropAllowed = false;\n      }\n    }\n  } else {\n    // | Node1 | <- abstractDropNode\n    //      |  Node2  |\n    // --^--|----=====| <- mousePosition\n    // 1. try insert after Node1\n    // 2. do not allow drop\n    if (allowDrop({\n      dragNode: abstractDragDataNode,\n      dropNode: abstractDropDataNode,\n      dropPosition: 1\n    })) {\n      dropPosition = 1;\n    } else {\n      dropAllowed = false;\n    }\n  }\n  return {\n    dropPosition: dropPosition,\n    dropLevelOffset: dropLevelOffset,\n    dropTargetKey: abstractDropNodeEntity.key,\n    dropTargetPos: abstractDropNodeEntity.pos,\n    dragOverNodeKey: dragOverNodeKey,\n    dropContainerKey: dropPosition === 0 ? null : ((_abstractDropNodeEnti = abstractDropNodeEntity.parent) === null || _abstractDropNodeEnti === void 0 ? void 0 : _abstractDropNodeEnti.key) || null,\n    dropAllowed: dropAllowed\n  };\n}\n\n/**\n * Return selectedKeys according with multiple prop\n * @param selectedKeys\n * @param props\n * @returns [string]\n */\nfunction calcSelectedKeys(selectedKeys, props) {\n  if (!selectedKeys) return undefined;\n  var multiple = props.multiple;\n  if (multiple) {\n    return selectedKeys.slice();\n  }\n  if (selectedKeys.length) {\n    return [selectedKeys[0]];\n  }\n  return selectedKeys;\n}\nvar internalProcessProps = function internalProcessProps(props) {\n  return props;\n};\nfunction convertDataToTree(treeData, processor) {\n  if (!treeData) return [];\n  var _ref2 = processor || {},\n    _ref2$processProps = _ref2.processProps,\n    processProps = _ref2$processProps === void 0 ? internalProcessProps : _ref2$processProps;\n  var list = Array.isArray(treeData) ? treeData : [treeData];\n  return list.map(function (_ref3) {\n    var children = _ref3.children,\n      props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref3, _excluded);\n    var childrenNodes = convertDataToTree(children, processor);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5___default().createElement(_TreeNode__WEBPACK_IMPORTED_MODULE_6__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n      key: props.key\n    }, processProps(props)), childrenNodes);\n  });\n}\n\n/**\n * Parse `checkedKeys` to { checkedKeys, halfCheckedKeys } style\n */\nfunction parseCheckedKeys(keys) {\n  if (!keys) {\n    return null;\n  }\n\n  // Convert keys to object format\n  var keyProps;\n  if (Array.isArray(keys)) {\n    // [Legacy] Follow the api doc\n    keyProps = {\n      checkedKeys: keys,\n      halfCheckedKeys: undefined\n    };\n  } else if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(keys) === 'object') {\n    keyProps = {\n      checkedKeys: keys.checked || undefined,\n      halfCheckedKeys: keys.halfChecked || undefined\n    };\n  } else {\n    (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, '`checkedKeys` is not an array or an object');\n    return null;\n  }\n  return keyProps;\n}\n\n/**\n * If user use `autoExpandParent` we should get the list of parent node\n * @param keyList\n * @param keyEntities\n */\nfunction conductExpandParent(keyList, keyEntities) {\n  var expandedKeys = new Set();\n  function conductUp(key) {\n    if (expandedKeys.has(key)) return;\n    var entity = (0,_utils_keyUtil__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(keyEntities, key);\n    if (!entity) return;\n    expandedKeys.add(key);\n    var parent = entity.parent,\n      node = entity.node;\n    if (node.disabled) return;\n    if (parent) {\n      conductUp(parent.key);\n    }\n  }\n  (keyList || []).forEach(function (key) {\n    conductUp(key);\n  });\n  return (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(expandedKeys);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/utils/conductUtil.js":
/*!******************************************************!*\
  !*** ./node_modules/rc-tree/es/utils/conductUtil.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conductCheck: () => (/* binding */ conductCheck),\n/* harmony export */   isCheckDisabled: () => (/* binding */ isCheckDisabled)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var _keyUtil__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./keyUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\");\n\n\nfunction removeFromCheckedKeys(halfCheckedKeys, checkedKeys) {\n  var filteredKeys = new Set();\n  halfCheckedKeys.forEach(function (key) {\n    if (!checkedKeys.has(key)) {\n      filteredKeys.add(key);\n    }\n  });\n  return filteredKeys;\n}\nfunction isCheckDisabled(node) {\n  var _ref = node || {},\n    disabled = _ref.disabled,\n    disableCheckbox = _ref.disableCheckbox,\n    checkable = _ref.checkable;\n  return !!(disabled || disableCheckbox) || checkable === false;\n}\n\n// Fill miss keys\nfunction fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set();\n\n  // Add checked keys top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children = entity.children,\n        children = _entity$children === void 0 ? [] : _entity$children;\n      if (checkedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.add(childEntity.key);\n        });\n      }\n    });\n  }\n\n  // Add checked keys from bottom to top\n  var visitedKeys = new Set();\n  for (var _level = maxLevel; _level >= 0; _level -= 1) {\n    var _entities = levelEntities.get(_level) || new Set();\n    _entities.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref2) {\n        var key = _ref2.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (allChecked) {\n        checkedKeys.add(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n\n// Remove useless key\nfunction cleanConductCheck(keys, halfKeys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set(halfKeys);\n\n  // Remove checked keys from top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children2 = entity.children,\n        children = _entity$children2 === void 0 ? [] : _entity$children2;\n      if (!checkedKeys.has(key) && !halfCheckedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.delete(childEntity.key);\n        });\n      }\n    });\n  }\n\n  // Remove checked keys form bottom to top\n  halfCheckedKeys = new Set();\n  var visitedKeys = new Set();\n  for (var _level2 = maxLevel; _level2 >= 0; _level2 -= 1) {\n    var _entities2 = levelEntities.get(_level2) || new Set();\n    _entities2.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref3) {\n        var key = _ref3.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (!allChecked) {\n        checkedKeys.delete(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n\n/**\n * Conduct with keys.\n * @param keyList current key list\n * @param keyEntities key - dataEntity map\n * @param mode `fill` to fill missing key, `clean` to remove useless key\n */\nfunction conductCheck(keyList, checked, keyEntities, getCheckDisabled) {\n  var warningMissKeys = [];\n  var syntheticGetCheckDisabled;\n  if (getCheckDisabled) {\n    syntheticGetCheckDisabled = getCheckDisabled;\n  } else {\n    syntheticGetCheckDisabled = isCheckDisabled;\n  }\n\n  // We only handle exist keys\n  var keys = new Set(keyList.filter(function (key) {\n    var hasEntity = !!(0,_keyUtil__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(keyEntities, key);\n    if (!hasEntity) {\n      warningMissKeys.push(key);\n    }\n    return hasEntity;\n  }));\n  var levelEntities = new Map();\n  var maxLevel = 0;\n\n  // Convert entities by level for calculation\n  Object.keys(keyEntities).forEach(function (key) {\n    var entity = keyEntities[key];\n    var level = entity.level;\n    var levelSet = levelEntities.get(level);\n    if (!levelSet) {\n      levelSet = new Set();\n      levelEntities.set(level, levelSet);\n    }\n    levelSet.add(entity);\n    maxLevel = Math.max(maxLevel, level);\n  });\n  (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(!warningMissKeys.length, \"Tree missing follow keys: \".concat(warningMissKeys.slice(0, 100).map(function (key) {\n    return \"'\".concat(key, \"'\");\n  }).join(', ')));\n  var result;\n  if (checked === true) {\n    result = fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  } else {\n    result = cleanConductCheck(keys, checked.halfCheckedKeys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  }\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy91dGlscy9jb25kdWN0VXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlDO0FBQ1A7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDTztBQUNQLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esc0JBQXNCLG1CQUFtQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBLDhCQUE4QixhQUFhO0FBQzNDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHNCQUFzQixtQkFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixjQUFjO0FBQzdDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHNCQUFzQixvREFBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSw4REFBTztBQUNUO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9yYy10cmVlL2VzL3V0aWxzL2NvbmR1Y3RVdGlsLmpzPzRmYjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHdhcm5pbmcgZnJvbSBcInJjLXV0aWwvZXMvd2FybmluZ1wiO1xuaW1wb3J0IGdldEVudGl0eSBmcm9tIFwiLi9rZXlVdGlsXCI7XG5mdW5jdGlvbiByZW1vdmVGcm9tQ2hlY2tlZEtleXMoaGFsZkNoZWNrZWRLZXlzLCBjaGVja2VkS2V5cykge1xuICB2YXIgZmlsdGVyZWRLZXlzID0gbmV3IFNldCgpO1xuICBoYWxmQ2hlY2tlZEtleXMuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgaWYgKCFjaGVja2VkS2V5cy5oYXMoa2V5KSkge1xuICAgICAgZmlsdGVyZWRLZXlzLmFkZChrZXkpO1xuICAgIH1cbiAgfSk7XG4gIHJldHVybiBmaWx0ZXJlZEtleXM7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNDaGVja0Rpc2FibGVkKG5vZGUpIHtcbiAgdmFyIF9yZWYgPSBub2RlIHx8IHt9LFxuICAgIGRpc2FibGVkID0gX3JlZi5kaXNhYmxlZCxcbiAgICBkaXNhYmxlQ2hlY2tib3ggPSBfcmVmLmRpc2FibGVDaGVja2JveCxcbiAgICBjaGVja2FibGUgPSBfcmVmLmNoZWNrYWJsZTtcbiAgcmV0dXJuICEhKGRpc2FibGVkIHx8IGRpc2FibGVDaGVja2JveCkgfHwgY2hlY2thYmxlID09PSBmYWxzZTtcbn1cblxuLy8gRmlsbCBtaXNzIGtleXNcbmZ1bmN0aW9uIGZpbGxDb25kdWN0Q2hlY2soa2V5cywgbGV2ZWxFbnRpdGllcywgbWF4TGV2ZWwsIHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQpIHtcbiAgdmFyIGNoZWNrZWRLZXlzID0gbmV3IFNldChrZXlzKTtcbiAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IG5ldyBTZXQoKTtcblxuICAvLyBBZGQgY2hlY2tlZCBrZXlzIHRvcCB0byBib3R0b21cbiAgZm9yICh2YXIgbGV2ZWwgPSAwOyBsZXZlbCA8PSBtYXhMZXZlbDsgbGV2ZWwgKz0gMSkge1xuICAgIHZhciBlbnRpdGllcyA9IGxldmVsRW50aXRpZXMuZ2V0KGxldmVsKSB8fCBuZXcgU2V0KCk7XG4gICAgZW50aXRpZXMuZm9yRWFjaChmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICB2YXIga2V5ID0gZW50aXR5LmtleSxcbiAgICAgICAgbm9kZSA9IGVudGl0eS5ub2RlLFxuICAgICAgICBfZW50aXR5JGNoaWxkcmVuID0gZW50aXR5LmNoaWxkcmVuLFxuICAgICAgICBjaGlsZHJlbiA9IF9lbnRpdHkkY2hpbGRyZW4gPT09IHZvaWQgMCA/IFtdIDogX2VudGl0eSRjaGlsZHJlbjtcbiAgICAgIGlmIChjaGVja2VkS2V5cy5oYXMoa2V5KSAmJiAhc3ludGhldGljR2V0Q2hlY2tEaXNhYmxlZChub2RlKSkge1xuICAgICAgICBjaGlsZHJlbi5maWx0ZXIoZnVuY3Rpb24gKGNoaWxkRW50aXR5KSB7XG4gICAgICAgICAgcmV0dXJuICFzeW50aGV0aWNHZXRDaGVja0Rpc2FibGVkKGNoaWxkRW50aXR5Lm5vZGUpO1xuICAgICAgICB9KS5mb3JFYWNoKGZ1bmN0aW9uIChjaGlsZEVudGl0eSkge1xuICAgICAgICAgIGNoZWNrZWRLZXlzLmFkZChjaGlsZEVudGl0eS5rZXkpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIC8vIEFkZCBjaGVja2VkIGtleXMgZnJvbSBib3R0b20gdG8gdG9wXG4gIHZhciB2aXNpdGVkS2V5cyA9IG5ldyBTZXQoKTtcbiAgZm9yICh2YXIgX2xldmVsID0gbWF4TGV2ZWw7IF9sZXZlbCA+PSAwOyBfbGV2ZWwgLT0gMSkge1xuICAgIHZhciBfZW50aXRpZXMgPSBsZXZlbEVudGl0aWVzLmdldChfbGV2ZWwpIHx8IG5ldyBTZXQoKTtcbiAgICBfZW50aXRpZXMuZm9yRWFjaChmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICB2YXIgcGFyZW50ID0gZW50aXR5LnBhcmVudCxcbiAgICAgICAgbm9kZSA9IGVudGl0eS5ub2RlO1xuXG4gICAgICAvLyBTa2lwIGlmIG5vIG5lZWQgdG8gY2hlY2tcbiAgICAgIGlmIChzeW50aGV0aWNHZXRDaGVja0Rpc2FibGVkKG5vZGUpIHx8ICFlbnRpdHkucGFyZW50IHx8IHZpc2l0ZWRLZXlzLmhhcyhlbnRpdHkucGFyZW50LmtleSkpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBTa2lwIGlmIHBhcmVudCBpcyBkaXNhYmxlZFxuICAgICAgaWYgKHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQoZW50aXR5LnBhcmVudC5ub2RlKSkge1xuICAgICAgICB2aXNpdGVkS2V5cy5hZGQocGFyZW50LmtleSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHZhciBhbGxDaGVja2VkID0gdHJ1ZTtcbiAgICAgIHZhciBwYXJ0aWFsQ2hlY2tlZCA9IGZhbHNlO1xuICAgICAgKHBhcmVudC5jaGlsZHJlbiB8fCBbXSkuZmlsdGVyKGZ1bmN0aW9uIChjaGlsZEVudGl0eSkge1xuICAgICAgICByZXR1cm4gIXN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQoY2hpbGRFbnRpdHkubm9kZSk7XG4gICAgICB9KS5mb3JFYWNoKGZ1bmN0aW9uIChfcmVmMikge1xuICAgICAgICB2YXIga2V5ID0gX3JlZjIua2V5O1xuICAgICAgICB2YXIgY2hlY2tlZCA9IGNoZWNrZWRLZXlzLmhhcyhrZXkpO1xuICAgICAgICBpZiAoYWxsQ2hlY2tlZCAmJiAhY2hlY2tlZCkge1xuICAgICAgICAgIGFsbENoZWNrZWQgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXBhcnRpYWxDaGVja2VkICYmIChjaGVja2VkIHx8IGhhbGZDaGVja2VkS2V5cy5oYXMoa2V5KSkpIHtcbiAgICAgICAgICBwYXJ0aWFsQ2hlY2tlZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgaWYgKGFsbENoZWNrZWQpIHtcbiAgICAgICAgY2hlY2tlZEtleXMuYWRkKHBhcmVudC5rZXkpO1xuICAgICAgfVxuICAgICAgaWYgKHBhcnRpYWxDaGVja2VkKSB7XG4gICAgICAgIGhhbGZDaGVja2VkS2V5cy5hZGQocGFyZW50LmtleSk7XG4gICAgICB9XG4gICAgICB2aXNpdGVkS2V5cy5hZGQocGFyZW50LmtleSk7XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBjaGVja2VkS2V5czogQXJyYXkuZnJvbShjaGVja2VkS2V5cyksXG4gICAgaGFsZkNoZWNrZWRLZXlzOiBBcnJheS5mcm9tKHJlbW92ZUZyb21DaGVja2VkS2V5cyhoYWxmQ2hlY2tlZEtleXMsIGNoZWNrZWRLZXlzKSlcbiAgfTtcbn1cblxuLy8gUmVtb3ZlIHVzZWxlc3Mga2V5XG5mdW5jdGlvbiBjbGVhbkNvbmR1Y3RDaGVjayhrZXlzLCBoYWxmS2V5cywgbGV2ZWxFbnRpdGllcywgbWF4TGV2ZWwsIHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQpIHtcbiAgdmFyIGNoZWNrZWRLZXlzID0gbmV3IFNldChrZXlzKTtcbiAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IG5ldyBTZXQoaGFsZktleXMpO1xuXG4gIC8vIFJlbW92ZSBjaGVja2VkIGtleXMgZnJvbSB0b3AgdG8gYm90dG9tXG4gIGZvciAodmFyIGxldmVsID0gMDsgbGV2ZWwgPD0gbWF4TGV2ZWw7IGxldmVsICs9IDEpIHtcbiAgICB2YXIgZW50aXRpZXMgPSBsZXZlbEVudGl0aWVzLmdldChsZXZlbCkgfHwgbmV3IFNldCgpO1xuICAgIGVudGl0aWVzLmZvckVhY2goZnVuY3Rpb24gKGVudGl0eSkge1xuICAgICAgdmFyIGtleSA9IGVudGl0eS5rZXksXG4gICAgICAgIG5vZGUgPSBlbnRpdHkubm9kZSxcbiAgICAgICAgX2VudGl0eSRjaGlsZHJlbjIgPSBlbnRpdHkuY2hpbGRyZW4sXG4gICAgICAgIGNoaWxkcmVuID0gX2VudGl0eSRjaGlsZHJlbjIgPT09IHZvaWQgMCA/IFtdIDogX2VudGl0eSRjaGlsZHJlbjI7XG4gICAgICBpZiAoIWNoZWNrZWRLZXlzLmhhcyhrZXkpICYmICFoYWxmQ2hlY2tlZEtleXMuaGFzKGtleSkgJiYgIXN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQobm9kZSkpIHtcbiAgICAgICAgY2hpbGRyZW4uZmlsdGVyKGZ1bmN0aW9uIChjaGlsZEVudGl0eSkge1xuICAgICAgICAgIHJldHVybiAhc3ludGhldGljR2V0Q2hlY2tEaXNhYmxlZChjaGlsZEVudGl0eS5ub2RlKTtcbiAgICAgICAgfSkuZm9yRWFjaChmdW5jdGlvbiAoY2hpbGRFbnRpdHkpIHtcbiAgICAgICAgICBjaGVja2VkS2V5cy5kZWxldGUoY2hpbGRFbnRpdHkua2V5KTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxuICAvLyBSZW1vdmUgY2hlY2tlZCBrZXlzIGZvcm0gYm90dG9tIHRvIHRvcFxuICBoYWxmQ2hlY2tlZEtleXMgPSBuZXcgU2V0KCk7XG4gIHZhciB2aXNpdGVkS2V5cyA9IG5ldyBTZXQoKTtcbiAgZm9yICh2YXIgX2xldmVsMiA9IG1heExldmVsOyBfbGV2ZWwyID49IDA7IF9sZXZlbDIgLT0gMSkge1xuICAgIHZhciBfZW50aXRpZXMyID0gbGV2ZWxFbnRpdGllcy5nZXQoX2xldmVsMikgfHwgbmV3IFNldCgpO1xuICAgIF9lbnRpdGllczIuZm9yRWFjaChmdW5jdGlvbiAoZW50aXR5KSB7XG4gICAgICB2YXIgcGFyZW50ID0gZW50aXR5LnBhcmVudCxcbiAgICAgICAgbm9kZSA9IGVudGl0eS5ub2RlO1xuXG4gICAgICAvLyBTa2lwIGlmIG5vIG5lZWQgdG8gY2hlY2tcbiAgICAgIGlmIChzeW50aGV0aWNHZXRDaGVja0Rpc2FibGVkKG5vZGUpIHx8ICFlbnRpdHkucGFyZW50IHx8IHZpc2l0ZWRLZXlzLmhhcyhlbnRpdHkucGFyZW50LmtleSkpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBTa2lwIGlmIHBhcmVudCBpcyBkaXNhYmxlZFxuICAgICAgaWYgKHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQoZW50aXR5LnBhcmVudC5ub2RlKSkge1xuICAgICAgICB2aXNpdGVkS2V5cy5hZGQocGFyZW50LmtleSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHZhciBhbGxDaGVja2VkID0gdHJ1ZTtcbiAgICAgIHZhciBwYXJ0aWFsQ2hlY2tlZCA9IGZhbHNlO1xuICAgICAgKHBhcmVudC5jaGlsZHJlbiB8fCBbXSkuZmlsdGVyKGZ1bmN0aW9uIChjaGlsZEVudGl0eSkge1xuICAgICAgICByZXR1cm4gIXN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQoY2hpbGRFbnRpdHkubm9kZSk7XG4gICAgICB9KS5mb3JFYWNoKGZ1bmN0aW9uIChfcmVmMykge1xuICAgICAgICB2YXIga2V5ID0gX3JlZjMua2V5O1xuICAgICAgICB2YXIgY2hlY2tlZCA9IGNoZWNrZWRLZXlzLmhhcyhrZXkpO1xuICAgICAgICBpZiAoYWxsQ2hlY2tlZCAmJiAhY2hlY2tlZCkge1xuICAgICAgICAgIGFsbENoZWNrZWQgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXBhcnRpYWxDaGVja2VkICYmIChjaGVja2VkIHx8IGhhbGZDaGVja2VkS2V5cy5oYXMoa2V5KSkpIHtcbiAgICAgICAgICBwYXJ0aWFsQ2hlY2tlZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgaWYgKCFhbGxDaGVja2VkKSB7XG4gICAgICAgIGNoZWNrZWRLZXlzLmRlbGV0ZShwYXJlbnQua2V5KTtcbiAgICAgIH1cbiAgICAgIGlmIChwYXJ0aWFsQ2hlY2tlZCkge1xuICAgICAgICBoYWxmQ2hlY2tlZEtleXMuYWRkKHBhcmVudC5rZXkpO1xuICAgICAgfVxuICAgICAgdmlzaXRlZEtleXMuYWRkKHBhcmVudC5rZXkpO1xuICAgIH0pO1xuICB9XG4gIHJldHVybiB7XG4gICAgY2hlY2tlZEtleXM6IEFycmF5LmZyb20oY2hlY2tlZEtleXMpLFxuICAgIGhhbGZDaGVja2VkS2V5czogQXJyYXkuZnJvbShyZW1vdmVGcm9tQ2hlY2tlZEtleXMoaGFsZkNoZWNrZWRLZXlzLCBjaGVja2VkS2V5cykpXG4gIH07XG59XG5cbi8qKlxuICogQ29uZHVjdCB3aXRoIGtleXMuXG4gKiBAcGFyYW0ga2V5TGlzdCBjdXJyZW50IGtleSBsaXN0XG4gKiBAcGFyYW0ga2V5RW50aXRpZXMga2V5IC0gZGF0YUVudGl0eSBtYXBcbiAqIEBwYXJhbSBtb2RlIGBmaWxsYCB0byBmaWxsIG1pc3Npbmcga2V5LCBgY2xlYW5gIHRvIHJlbW92ZSB1c2VsZXNzIGtleVxuICovXG5leHBvcnQgZnVuY3Rpb24gY29uZHVjdENoZWNrKGtleUxpc3QsIGNoZWNrZWQsIGtleUVudGl0aWVzLCBnZXRDaGVja0Rpc2FibGVkKSB7XG4gIHZhciB3YXJuaW5nTWlzc0tleXMgPSBbXTtcbiAgdmFyIHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQ7XG4gIGlmIChnZXRDaGVja0Rpc2FibGVkKSB7XG4gICAgc3ludGhldGljR2V0Q2hlY2tEaXNhYmxlZCA9IGdldENoZWNrRGlzYWJsZWQ7XG4gIH0gZWxzZSB7XG4gICAgc3ludGhldGljR2V0Q2hlY2tEaXNhYmxlZCA9IGlzQ2hlY2tEaXNhYmxlZDtcbiAgfVxuXG4gIC8vIFdlIG9ubHkgaGFuZGxlIGV4aXN0IGtleXNcbiAgdmFyIGtleXMgPSBuZXcgU2V0KGtleUxpc3QuZmlsdGVyKGZ1bmN0aW9uIChrZXkpIHtcbiAgICB2YXIgaGFzRW50aXR5ID0gISFnZXRFbnRpdHkoa2V5RW50aXRpZXMsIGtleSk7XG4gICAgaWYgKCFoYXNFbnRpdHkpIHtcbiAgICAgIHdhcm5pbmdNaXNzS2V5cy5wdXNoKGtleSk7XG4gICAgfVxuICAgIHJldHVybiBoYXNFbnRpdHk7XG4gIH0pKTtcbiAgdmFyIGxldmVsRW50aXRpZXMgPSBuZXcgTWFwKCk7XG4gIHZhciBtYXhMZXZlbCA9IDA7XG5cbiAgLy8gQ29udmVydCBlbnRpdGllcyBieSBsZXZlbCBmb3IgY2FsY3VsYXRpb25cbiAgT2JqZWN0LmtleXMoa2V5RW50aXRpZXMpLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgIHZhciBlbnRpdHkgPSBrZXlFbnRpdGllc1trZXldO1xuICAgIHZhciBsZXZlbCA9IGVudGl0eS5sZXZlbDtcbiAgICB2YXIgbGV2ZWxTZXQgPSBsZXZlbEVudGl0aWVzLmdldChsZXZlbCk7XG4gICAgaWYgKCFsZXZlbFNldCkge1xuICAgICAgbGV2ZWxTZXQgPSBuZXcgU2V0KCk7XG4gICAgICBsZXZlbEVudGl0aWVzLnNldChsZXZlbCwgbGV2ZWxTZXQpO1xuICAgIH1cbiAgICBsZXZlbFNldC5hZGQoZW50aXR5KTtcbiAgICBtYXhMZXZlbCA9IE1hdGgubWF4KG1heExldmVsLCBsZXZlbCk7XG4gIH0pO1xuICB3YXJuaW5nKCF3YXJuaW5nTWlzc0tleXMubGVuZ3RoLCBcIlRyZWUgbWlzc2luZyBmb2xsb3cga2V5czogXCIuY29uY2F0KHdhcm5pbmdNaXNzS2V5cy5zbGljZSgwLCAxMDApLm1hcChmdW5jdGlvbiAoa2V5KSB7XG4gICAgcmV0dXJuIFwiJ1wiLmNvbmNhdChrZXksIFwiJ1wiKTtcbiAgfSkuam9pbignLCAnKSkpO1xuICB2YXIgcmVzdWx0O1xuICBpZiAoY2hlY2tlZCA9PT0gdHJ1ZSkge1xuICAgIHJlc3VsdCA9IGZpbGxDb25kdWN0Q2hlY2soa2V5cywgbGV2ZWxFbnRpdGllcywgbWF4TGV2ZWwsIHN5bnRoZXRpY0dldENoZWNrRGlzYWJsZWQpO1xuICB9IGVsc2Uge1xuICAgIHJlc3VsdCA9IGNsZWFuQ29uZHVjdENoZWNrKGtleXMsIGNoZWNrZWQuaGFsZkNoZWNrZWRLZXlzLCBsZXZlbEVudGl0aWVzLCBtYXhMZXZlbCwgc3ludGhldGljR2V0Q2hlY2tEaXNhYmxlZCk7XG4gIH1cbiAgcmV0dXJuIHJlc3VsdDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/utils/conductUtil.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/utils/diffUtil.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-tree/es/utils/diffUtil.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findExpandedKeys: () => (/* binding */ findExpandedKeys),\n/* harmony export */   getExpandRange: () => (/* binding */ getExpandRange)\n/* harmony export */ });\nfunction findExpandedKeys() {\n  var prev = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var next = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var prevLen = prev.length;\n  var nextLen = next.length;\n  if (Math.abs(prevLen - nextLen) !== 1) {\n    return {\n      add: false,\n      key: null\n    };\n  }\n  function find(shorter, longer) {\n    var cache = new Map();\n    shorter.forEach(function (key) {\n      cache.set(key, true);\n    });\n    var keys = longer.filter(function (key) {\n      return !cache.has(key);\n    });\n    return keys.length === 1 ? keys[0] : null;\n  }\n  if (prevLen < nextLen) {\n    return {\n      add: true,\n      key: find(prev, next)\n    };\n  }\n  return {\n    add: false,\n    key: find(next, prev)\n  };\n}\nfunction getExpandRange(shorter, longer, key) {\n  var shorterStartIndex = shorter.findIndex(function (data) {\n    return data.key === key;\n  });\n  var shorterEndNode = shorter[shorterStartIndex + 1];\n  var longerStartIndex = longer.findIndex(function (data) {\n    return data.key === key;\n  });\n  if (shorterEndNode) {\n    var longerEndIndex = longer.findIndex(function (data) {\n      return data.key === shorterEndNode.key;\n    });\n    return longer.slice(longerStartIndex + 1, longerEndIndex);\n  }\n  return longer.slice(longerStartIndex + 1);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/utils/diffUtil.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-tree/es/utils/keyUtil.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getEntity)\n/* harmony export */ });\nfunction getEntity(keyEntities, key) {\n  return keyEntities[key];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy91dGlscy9rZXlVdGlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb2dpY2xlYXB3ZWIvLi9ub2RlX21vZHVsZXMvcmMtdHJlZS9lcy91dGlscy9rZXlVdGlsLmpzPzQ4MWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0RW50aXR5KGtleUVudGl0aWVzLCBrZXkpIHtcbiAgcmV0dXJuIGtleUVudGl0aWVzW2tleV07XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-tree/es/utils/treeUtil.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertDataToEntities: () => (/* binding */ convertDataToEntities),\n/* harmony export */   convertNodePropsToEventData: () => (/* binding */ convertNodePropsToEventData),\n/* harmony export */   convertTreeToData: () => (/* binding */ convertTreeToData),\n/* harmony export */   fillFieldNames: () => (/* binding */ fillFieldNames),\n/* harmony export */   flattenTreeData: () => (/* binding */ flattenTreeData),\n/* harmony export */   getKey: () => (/* binding */ getKey),\n/* harmony export */   getPosition: () => (/* binding */ getPosition),\n/* harmony export */   getTreeNodeProps: () => (/* binding */ getTreeNodeProps),\n/* harmony export */   isTreeNode: () => (/* binding */ isTreeNode),\n/* harmony export */   traverseDataNodes: () => (/* binding */ traverseDataNodes),\n/* harmony export */   warningWithoutKey: () => (/* binding */ warningWithoutKey)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/Children/toArray */ \"(ssr)/./node_modules/rc-util/es/Children/toArray.js\");\n/* harmony import */ var rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/omit */ \"(ssr)/./node_modules/rc-util/es/omit.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var _keyUtil__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./keyUtil */ \"(ssr)/./node_modules/rc-tree/es/utils/keyUtil.js\");\n\n\n\n\nvar _excluded = [\"children\"];\n\n\n\n\nfunction getPosition(level, index) {\n  return \"\".concat(level, \"-\").concat(index);\n}\nfunction isTreeNode(node) {\n  return node && node.type && node.type.isTreeNode;\n}\nfunction getKey(key, pos) {\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  return pos;\n}\nfunction fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    title = _ref.title,\n    _title = _ref._title,\n    key = _ref.key,\n    children = _ref.children;\n  var mergedTitle = title || 'title';\n  return {\n    title: mergedTitle,\n    _title: _title || [mergedTitle],\n    key: key || 'key',\n    children: children || 'children'\n  };\n}\n\n/**\n * Warning if TreeNode do not provides key\n */\nfunction warningWithoutKey(treeData, fieldNames) {\n  var keys = new Map();\n  function dig(list) {\n    var path = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    (list || []).forEach(function (treeNode) {\n      var key = treeNode[fieldNames.key];\n      var children = treeNode[fieldNames.children];\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(key !== null && key !== undefined, \"Tree node must have a certain key: [\".concat(path).concat(key, \"]\"));\n      var recordKey = String(key);\n      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(!keys.has(recordKey) || key === null || key === undefined, \"Same 'key' exist in the Tree: \".concat(recordKey));\n      keys.set(recordKey, true);\n      dig(children, \"\".concat(path).concat(recordKey, \" > \"));\n    });\n  }\n  dig(treeData);\n}\n\n/**\n * Convert `children` of Tree into `treeData` structure.\n */\nfunction convertTreeToData(rootNodes) {\n  function dig(node) {\n    var treeNodes = (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(node);\n    return treeNodes.map(function (treeNode) {\n      // Filter invalidate node\n      if (!isTreeNode(treeNode)) {\n        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(!treeNode, 'Tree/TreeNode can only accept TreeNode as children.');\n        return null;\n      }\n      var key = treeNode.key;\n      var _treeNode$props = treeNode.props,\n        children = _treeNode$props.children,\n        rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_treeNode$props, _excluded);\n      var dataNode = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        key: key\n      }, rest);\n      var parsedChildren = dig(children);\n      if (parsedChildren.length) {\n        dataNode.children = parsedChildren;\n      }\n      return dataNode;\n    }).filter(function (dataNode) {\n      return dataNode;\n    });\n  }\n  return dig(rootNodes);\n}\n\n/**\n * Flat nest tree data into flatten list. This is used for virtual list render.\n * @param treeNodeList Origin data node list\n * @param expandedKeys\n * need expanded keys, provides `true` means all expanded (used in `rc-tree-select`).\n */\nfunction flattenTreeData(treeNodeList, expandedKeys, fieldNames) {\n  var _fillFieldNames = fillFieldNames(fieldNames),\n    fieldTitles = _fillFieldNames._title,\n    fieldKey = _fillFieldNames.key,\n    fieldChildren = _fillFieldNames.children;\n  var expandedKeySet = new Set(expandedKeys === true ? [] : expandedKeys);\n  var flattenList = [];\n  function dig(list) {\n    var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    return list.map(function (treeNode, index) {\n      var pos = getPosition(parent ? parent.pos : '0', index);\n      var mergedKey = getKey(treeNode[fieldKey], pos);\n\n      // Pick matched title in field title list\n      var mergedTitle;\n      for (var i = 0; i < fieldTitles.length; i += 1) {\n        var fieldTitle = fieldTitles[i];\n        if (treeNode[fieldTitle] !== undefined) {\n          mergedTitle = treeNode[fieldTitle];\n          break;\n        }\n      }\n\n      // Add FlattenDataNode into list\n      // We use `Object.assign` here to save perf since babel's `objectSpread` has perf issue\n      var flattenNode = Object.assign((0,rc_util_es_omit__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(treeNode, [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(fieldTitles), [fieldKey, fieldChildren])), {\n        title: mergedTitle,\n        key: mergedKey,\n        parent: parent,\n        pos: pos,\n        children: null,\n        data: treeNode,\n        isStart: [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(parent ? parent.isStart : []), [index === 0]),\n        isEnd: [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(parent ? parent.isEnd : []), [index === list.length - 1])\n      });\n      flattenList.push(flattenNode);\n\n      // Loop treeNode children\n      if (expandedKeys === true || expandedKeySet.has(mergedKey)) {\n        flattenNode.children = dig(treeNode[fieldChildren] || [], flattenNode);\n      } else {\n        flattenNode.children = [];\n      }\n      return flattenNode;\n    });\n  }\n  dig(treeNodeList);\n  return flattenList;\n}\n/**\n * Traverse all the data by `treeData`.\n * Please not use it out of the `rc-tree` since we may refactor this code.\n */\nfunction traverseDataNodes(dataNodes, callback,\n// To avoid too many params, let use config instead of origin param\nconfig) {\n  var mergedConfig = {};\n  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(config) === 'object') {\n    mergedConfig = config;\n  } else {\n    mergedConfig = {\n      externalGetKey: config\n    };\n  }\n  mergedConfig = mergedConfig || {};\n\n  // Init config\n  var _mergedConfig = mergedConfig,\n    childrenPropName = _mergedConfig.childrenPropName,\n    externalGetKey = _mergedConfig.externalGetKey,\n    fieldNames = _mergedConfig.fieldNames;\n  var _fillFieldNames2 = fillFieldNames(fieldNames),\n    fieldKey = _fillFieldNames2.key,\n    fieldChildren = _fillFieldNames2.children;\n  var mergeChildrenPropName = childrenPropName || fieldChildren;\n\n  // Get keys\n  var syntheticGetKey;\n  if (externalGetKey) {\n    if (typeof externalGetKey === 'string') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return node[externalGetKey];\n      };\n    } else if (typeof externalGetKey === 'function') {\n      syntheticGetKey = function syntheticGetKey(node) {\n        return externalGetKey(node);\n      };\n    }\n  } else {\n    syntheticGetKey = function syntheticGetKey(node, pos) {\n      return getKey(node[fieldKey], pos);\n    };\n  }\n\n  // Process\n  function processNode(node, index, parent, pathNodes) {\n    var children = node ? node[mergeChildrenPropName] : dataNodes;\n    var pos = node ? getPosition(parent.pos, index) : '0';\n    var connectNodes = node ? [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(pathNodes), [node]) : [];\n\n    // Process node if is not root\n    if (node) {\n      var key = syntheticGetKey(node, pos);\n      var _data = {\n        node: node,\n        index: index,\n        pos: pos,\n        key: key,\n        parentPos: parent.node ? parent.pos : null,\n        level: parent.level + 1,\n        nodes: connectNodes\n      };\n      callback(_data);\n    }\n\n    // Process children node\n    if (children) {\n      children.forEach(function (subNode, subIndex) {\n        processNode(subNode, subIndex, {\n          node: node,\n          pos: pos,\n          level: parent ? parent.level + 1 : -1\n        }, connectNodes);\n      });\n    }\n  }\n  processNode(null);\n}\n/**\n * Convert `treeData` into entity records.\n */\nfunction convertDataToEntities(dataNodes) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    initWrapper = _ref2.initWrapper,\n    processEntity = _ref2.processEntity,\n    onProcessFinished = _ref2.onProcessFinished,\n    externalGetKey = _ref2.externalGetKey,\n    childrenPropName = _ref2.childrenPropName,\n    fieldNames = _ref2.fieldNames;\n  var /** @deprecated Use `config.externalGetKey` instead */\n  legacyExternalGetKey = arguments.length > 2 ? arguments[2] : undefined;\n  // Init config\n  var mergedExternalGetKey = externalGetKey || legacyExternalGetKey;\n  var posEntities = {};\n  var keyEntities = {};\n  var wrapper = {\n    posEntities: posEntities,\n    keyEntities: keyEntities\n  };\n  if (initWrapper) {\n    wrapper = initWrapper(wrapper) || wrapper;\n  }\n  traverseDataNodes(dataNodes, function (item) {\n    var node = item.node,\n      index = item.index,\n      pos = item.pos,\n      key = item.key,\n      parentPos = item.parentPos,\n      level = item.level,\n      nodes = item.nodes;\n    var entity = {\n      node: node,\n      nodes: nodes,\n      index: index,\n      key: key,\n      pos: pos,\n      level: level\n    };\n    var mergedKey = getKey(key, pos);\n    posEntities[pos] = entity;\n    keyEntities[mergedKey] = entity;\n\n    // Fill children\n    entity.parent = posEntities[parentPos];\n    if (entity.parent) {\n      entity.parent.children = entity.parent.children || [];\n      entity.parent.children.push(entity);\n    }\n    if (processEntity) {\n      processEntity(entity, wrapper);\n    }\n  }, {\n    externalGetKey: mergedExternalGetKey,\n    childrenPropName: childrenPropName,\n    fieldNames: fieldNames\n  });\n  if (onProcessFinished) {\n    onProcessFinished(wrapper);\n  }\n  return wrapper;\n}\n/**\n * Get TreeNode props with Tree props.\n */\nfunction getTreeNodeProps(key, _ref3) {\n  var expandedKeys = _ref3.expandedKeys,\n    selectedKeys = _ref3.selectedKeys,\n    loadedKeys = _ref3.loadedKeys,\n    loadingKeys = _ref3.loadingKeys,\n    checkedKeys = _ref3.checkedKeys,\n    halfCheckedKeys = _ref3.halfCheckedKeys,\n    dragOverNodeKey = _ref3.dragOverNodeKey,\n    dropPosition = _ref3.dropPosition,\n    keyEntities = _ref3.keyEntities;\n  var entity = (0,_keyUtil__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(keyEntities, key);\n  var treeNodeProps = {\n    eventKey: key,\n    expanded: expandedKeys.indexOf(key) !== -1,\n    selected: selectedKeys.indexOf(key) !== -1,\n    loaded: loadedKeys.indexOf(key) !== -1,\n    loading: loadingKeys.indexOf(key) !== -1,\n    checked: checkedKeys.indexOf(key) !== -1,\n    halfChecked: halfCheckedKeys.indexOf(key) !== -1,\n    pos: String(entity ? entity.pos : ''),\n    // [Legacy] Drag props\n    // Since the interaction of drag is changed, the semantic of the props are\n    // not accuracy, I think it should be finally removed\n    dragOver: dragOverNodeKey === key && dropPosition === 0,\n    dragOverGapTop: dragOverNodeKey === key && dropPosition === -1,\n    dragOverGapBottom: dragOverNodeKey === key && dropPosition === 1\n  };\n  return treeNodeProps;\n}\nfunction convertNodePropsToEventData(props) {\n  var data = props.data,\n    expanded = props.expanded,\n    selected = props.selected,\n    checked = props.checked,\n    loaded = props.loaded,\n    loading = props.loading,\n    halfChecked = props.halfChecked,\n    dragOver = props.dragOver,\n    dragOverGapTop = props.dragOverGapTop,\n    dragOverGapBottom = props.dragOverGapBottom,\n    pos = props.pos,\n    active = props.active,\n    eventKey = props.eventKey;\n  var eventData = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, data), {}, {\n    expanded: expanded,\n    selected: selected,\n    checked: checked,\n    loaded: loaded,\n    loading: loading,\n    halfChecked: halfChecked,\n    dragOver: dragOver,\n    dragOverGapTop: dragOverGapTop,\n    dragOverGapBottom: dragOverGapBottom,\n    pos: pos,\n    active: active,\n    key: eventKey\n  });\n  if (!('props' in eventData)) {\n    Object.defineProperty(eventData, 'props', {\n      get: function get() {\n        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, 'Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`.');\n        return props;\n      }\n    });\n  }\n  return eventData;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-tree/es/utils/treeUtil.js\n");

/***/ })

};
;