window.addEventListener("error",(function(e){try{if(e.message.includes("ResizeObserver"))return e.stopImmediatePropagation(),!1}catch(e){console.log(e)}}),!0),function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define([],r):"object"==typeof exports?exports.GUI=r():e.GUI=r()}(self,(()=>(()=>{var e,r,n,t,o,i={32284:(e,r,n)=>{"use strict";n.r(r);var t=n(46942),o=n.n(t),i=n(5556),a=n.n(i),d=n(96540),c=n(40961),s=n(61934),l=n(77108),u=n(19489),f=n(73125),p=n(41580),h=n(11640),v=n(71270),y=n(50367);const m=e=>{let{isPlayerOnly:r,onSeeInside:n,projectId:t}=e;return d.createElement(u.default,{className:o()(r?y.default.stageOnly:y.default.editor)},r&&d.createElement("button",{onClick:n},"See inside"),d.createElement(f.default,{canEditTitle:!0,enableCommunity:!0,isPlayerOnly:r,projectId:t}))};m.propTypes={isPlayerOnly:a().bool,onSeeInside:a().func,projectId:a().string};const b=(0,s.connect)((e=>({isPlayerOnly:e.scratchGui.mode.isPlayerOnly})),(e=>({onSeeInside:()=>e((0,v.setPlayer)(!1))})))(m),g=(0,l.compose)(h.default,p.default)(b),_=document.createElement("div");document.body.appendChild(_),c.render(d.createElement(g,{isPlayerOnly:!0}),_)},34368:(e,r,n)=>{var t=n(54991),o=n(76314)(t);o.push([e.id,".player_stage-only_21SVa {\n    width: calc(480px + 1rem);\n}\n\n.player_editor_K1xPI {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n}\n\n.player_stage-only_21SVa * {\n    box-sizing: border-box;\n}\n","",{version:3,sources:["webpack://./src/playground/player.css"],names:[],mappings:"AAAA;IACI,yBAAyB;AAC7B;;AAEA;IACI,kBAAkB;IAClB,MAAM;IACN,OAAO;IACP,YAAY;IACZ,WAAW;AACf;;AAEA;IACI,sBAAsB;AAC1B",sourcesContent:[".stage-only {\n    width: calc(480px + 1rem);\n}\n\n.editor {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n}\n\n.stage-only * {\n    box-sizing: border-box;\n}\n"],sourceRoot:""}]),o.locals={"stage-only":"player_stage-only_21SVa",stageOnly:"player_stage-only_21SVa",editor:"player_editor_K1xPI"},e.exports=o},50367:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>I});var t=n(85072),o=n.n(t),i=n(97825),a=n.n(i),d=n(77659),c=n.n(d),s=n(55056),l=n.n(s),u=n(10540),f=n.n(u),p=n(41113),h=n.n(p),v=n(34368),y=n.n(v),m={};for(const e in v)"default"!==e&&(m[e]=()=>v[e]);n.d(r,m);var b={};b.styleTagTransform=h(),b.setAttributes=l(),b.insert=c().bind(null,"head"),b.domAPI=a(),b.insertStyleElement=f();var g=o()(y(),b);if(!y().locals||e.hot.invalidate){var _=!y().locals,E=_?v:y().locals;e.hot.accept(34368,(r=>{v=n(34368),y=n.n(v),function(e,r,n){if(!e&&r||e&&!r)return!1;var t;for(t in e)if((!n||"default"!==t)&&e[t]!==r[t])return!1;for(t in r)if(!(n&&"default"===t||e[t]))return!1;return!0}(E,_?v:y().locals,_)?(E=_?v:y().locals,g(y())):e.hot.invalidate()}))}e.hot.dispose((function(){g()}));const I=y()&&y().locals?y().locals:void 0}},a={};function d(e){var r=a[e];if(void 0!==r)return r.exports;var n=a[e]={id:e,loaded:!1,exports:{}},t={id:e,module:n,factory:i[e],require:d};return d.i.forEach((function(e){e(t)})),n=t.module,t.factory.call(n.exports,n,n.exports,t.require),n.loaded=!0,n.exports}d.m=i,d.c=a,d.i=[],d.amdD=function(){throw new Error("define cannot be used indirect")},d.amdO={},e=[],d.O=(r,n,t,o)=>{if(!n){var i=1/0;for(l=0;l<e.length;l++){for(var[n,t,o]=e[l],a=!0,c=0;c<n.length;c++)(!1&o||i>=o)&&Object.keys(d.O).every((e=>d.O[e](n[c])))?n.splice(c--,1):(a=!1,o<i&&(i=o));if(a){e.splice(l--,1);var s=t();void 0!==s&&(r=s)}}return r}o=o||0;for(var l=e.length;l>0&&e[l-1][2]>o;l--)e[l]=e[l-1];e[l]=[n,t,o]},d.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return d.d(r,{a:r}),r},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,d.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var o=Object.create(null);d.r(o);var i={};r=r||[null,n({}),n([]),n(n)];for(var a=2&t&&e;"object"==typeof a&&!~r.indexOf(a);a=n(a))Object.getOwnPropertyNames(a).forEach((r=>i[r]=()=>e[r]));return i.default=()=>e,d.d(o,i),o},d.d=(e,r)=>{for(var n in r)d.o(r,n)&&!d.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},d.f={},d.e=e=>Promise.all(Object.keys(d.f).reduce(((r,n)=>(d.f[n](e,r),r)),[])),d.u=e=>"static/js/"+{29:"uk-steps",122:"ja-steps",149:"fr-steps",179:"tr-steps",365:"zh_TW-steps",367:"sw-steps",509:"es-steps",665:"zh_CN-steps",875:"am-steps",884:"ar-steps",904:"pt_BR-steps",950:"zu-steps"}[e]+"."+{29:"ad8cc0c1d099b36fb652",122:"c425b7c855b44887bd5a",149:"8dcb9cbc81509f82e9fb",179:"da27b180bbd46e0b39b0",365:"7a3b2aecb25816a500ef",367:"b52015c009cc4a3ad349",509:"e3a6b4f0b3a58302c68c",665:"2669dcf583e8f55a3632",875:"54abb2724ac1d567ad8a",884:"970da08a0bcd1f6a166f",904:"f7703429f8123efdb1b1",950:"38c123d1621f65d39f90"}[e]+".js",d.hu=e=>e+"."+d.h()+".hot-update.js",d.hmrF=()=>"player."+d.h()+".hot-update.json",d.h=()=>"619309e5f863873ae05b",d.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),d.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),d.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t={},o="GUI:",d.l=(e,r,n,i)=>{if(t[e])t[e].push(r);else{var a,c;if(void 0!==n)for(var s=document.getElementsByTagName("script"),l=0;l<s.length;l++){var u=s[l];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==o+n){a=u;break}}a||(c=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,d.nc&&a.setAttribute("nonce",d.nc),a.setAttribute("data-webpack",o+n),a.src=e),t[e]=[r];var f=(r,n)=>{a.onerror=a.onload=null,clearTimeout(p);var o=t[e];if(delete t[e],a.parentNode&&a.parentNode.removeChild(a),o&&o.forEach((e=>e(n))),r)return r(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=f.bind(null,a.onerror),a.onload=f.bind(null,a.onload),c&&document.head.appendChild(a)}},d.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},d.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e,r,n,t={},o=d.c,i=[],a=[],c="idle",s=0,l=[];function u(e){c=e;for(var r=[],n=0;n<a.length;n++)r[n]=a[n].call(null,e);return Promise.all(r).then((function(){}))}function f(){0==--s&&u("ready").then((function(){if(0===s){var e=l;l=[];for(var r=0;r<e.length;r++)e[r]()}}))}function p(e){if("idle"!==c)throw new Error("check() is only allowed in idle status");return u("check").then(d.hmrM).then((function(n){return n?u("prepare").then((function(){var t=[];return r=[],Promise.all(Object.keys(d.hmrC).reduce((function(e,o){return d.hmrC[o](n.c,n.r,n.m,e,r,t),e}),[])).then((function(){return r=function(){return e?v(e):u("ready").then((function(){return t}))},0===s?r():new Promise((function(e){l.push((function(){e(r())}))}));var r}))})):u(y()?"ready":"idle").then((function(){return null}))}))}function h(e){return"ready"!==c?Promise.resolve().then((function(){throw new Error("apply() is only allowed in ready status (state: "+c+")")})):v(e)}function v(e){e=e||{},y();var t=r.map((function(r){return r(e)}));r=void 0;var o=t.map((function(e){return e.error})).filter(Boolean);if(o.length>0)return u("abort").then((function(){throw o[0]}));var i=u("dispose");t.forEach((function(e){e.dispose&&e.dispose()}));var a,d=u("apply"),c=function(e){a||(a=e)},s=[];return t.forEach((function(e){if(e.apply){var r=e.apply(c);if(r)for(var n=0;n<r.length;n++)s.push(r[n])}})),Promise.all([i,d]).then((function(){return a?u("fail").then((function(){throw a})):n?v(e).then((function(e){return s.forEach((function(r){e.indexOf(r)<0&&e.push(r)})),e})):u("idle").then((function(){return s}))}))}function y(){if(n)return r||(r=[]),Object.keys(d.hmrI).forEach((function(e){n.forEach((function(n){d.hmrI[e](n,r)}))})),n=void 0,!0}d.hmrD=t,d.i.push((function(l){var v,y,m,b,g=l.module,_=function(r,n){var t=o[n];if(!t)return r;var a=function(a){if(t.hot.active){if(o[a]){var d=o[a].parents;-1===d.indexOf(n)&&d.push(n)}else i=[n],e=a;-1===t.children.indexOf(a)&&t.children.push(a)}else console.warn("[HMR] unexpected require("+a+") from disposed module "+n),i=[];return r(a)},d=function(e){return{configurable:!0,enumerable:!0,get:function(){return r[e]},set:function(n){r[e]=n}}};for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&"e"!==l&&Object.defineProperty(a,l,d(l));return a.e=function(e,n){return function(e){switch(c){case"ready":u("prepare");case"prepare":return s++,e.then(f,f),e;default:return e}}(r.e(e,n))},a}(l.require,l.id);g.hot=(v=l.id,y=g,b={_acceptedDependencies:{},_acceptedErrorHandlers:{},_declinedDependencies:{},_selfAccepted:!1,_selfDeclined:!1,_selfInvalidated:!1,_disposeHandlers:[],_main:m=e!==v,_requireSelf:function(){i=y.parents.slice(),e=m?void 0:v,d(v)},active:!0,accept:function(e,r,n){if(void 0===e)b._selfAccepted=!0;else if("function"==typeof e)b._selfAccepted=e;else if("object"==typeof e&&null!==e)for(var t=0;t<e.length;t++)b._acceptedDependencies[e[t]]=r||function(){},b._acceptedErrorHandlers[e[t]]=n;else b._acceptedDependencies[e]=r||function(){},b._acceptedErrorHandlers[e]=n},decline:function(e){if(void 0===e)b._selfDeclined=!0;else if("object"==typeof e&&null!==e)for(var r=0;r<e.length;r++)b._declinedDependencies[e[r]]=!0;else b._declinedDependencies[e]=!0},dispose:function(e){b._disposeHandlers.push(e)},addDisposeHandler:function(e){b._disposeHandlers.push(e)},removeDisposeHandler:function(e){var r=b._disposeHandlers.indexOf(e);r>=0&&b._disposeHandlers.splice(r,1)},invalidate:function(){switch(this._selfInvalidated=!0,c){case"idle":r=[],Object.keys(d.hmrI).forEach((function(e){d.hmrI[e](v,r)})),u("ready");break;case"ready":Object.keys(d.hmrI).forEach((function(e){d.hmrI[e](v,r)}));break;case"prepare":case"check":case"dispose":case"apply":(n=n||[]).push(v)}},check:p,apply:h,status:function(e){if(!e)return c;a.push(e)},addStatusHandler:function(e){a.push(e)},removeStatusHandler:function(e){var r=a.indexOf(e);r>=0&&a.splice(r,1)},data:t[v]},e=void 0,b),g.parents=i,g.children=[],i=[],l.require=_})),d.hmrC={},d.hmrI={}})(),d.p="/logicleap/",(()=>{var e,r=d.hmrS_jsonp=d.hmrS_jsonp||{88:0};d.f.j=(e,n)=>{var t=d.o(r,e)?r[e]:void 0;if(0!==t)if(t)n.push(t[2]);else{var o=new Promise(((n,o)=>t=r[e]=[n,o]));n.push(t[2]=o);var i=d.p+d.u(e),a=new Error;d.l(i,(n=>{if(d.o(r,e)&&(0!==(t=r[e])&&(r[e]=void 0),t)){var o=n&&("load"===n.type?"missing":n.type),i=n&&n.target&&n.target.src;a.message="Loading chunk "+e+" failed.\n("+o+": "+i+")",a.name="ChunkLoadError",a.type=o,a.request=i,t[1](a)}}),"chunk-"+e,e)}};var n,t,o,i,a={};function c(r,n){return e=n,new Promise(((e,n)=>{a[r]=e;var t=d.p+d.hu(r),o=new Error;d.l(t,(e=>{if(a[r]){a[r]=void 0;var t=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;o.message="Loading hot update chunk "+r+" failed.\n("+t+": "+i+")",o.name="ChunkLoadError",o.type=t,o.request=i,n(o)}}))}))}function s(e){function a(e){for(var r=[e],n={},t=r.map((function(e){return{chain:[e],id:e}}));t.length>0;){var o=t.pop(),i=o.id,a=o.chain,s=d.c[i];if(s&&(!s.hot._selfAccepted||s.hot._selfInvalidated)){if(s.hot._selfDeclined)return{type:"self-declined",chain:a,moduleId:i};if(s.hot._main)return{type:"unaccepted",chain:a,moduleId:i};for(var l=0;l<s.parents.length;l++){var u=s.parents[l],f=d.c[u];if(f){if(f.hot._declinedDependencies[i])return{type:"declined",chain:a.concat([u]),moduleId:i,parentId:u};-1===r.indexOf(u)&&(f.hot._acceptedDependencies[i]?(n[u]||(n[u]=[]),c(n[u],[i])):(delete n[u],r.push(u),t.push({chain:a.concat([u]),id:u})))}}}}return{type:"accepted",moduleId:e,outdatedModules:r,outdatedDependencies:n}}function c(e,r){for(var n=0;n<r.length;n++){var t=r[n];-1===e.indexOf(t)&&e.push(t)}}d.f&&delete d.f.jsonpHmr,n=void 0;var s={},l=[],u={},f=function(e){console.warn("[HMR] unexpected require("+e.id+") to disposed module")};for(var p in t)if(d.o(t,p)){var h=t[p],v=h?a(p):{type:"disposed",moduleId:p},y=!1,m=!1,b=!1,g="";switch(v.chain&&(g="\nUpdate propagation: "+v.chain.join(" -> ")),v.type){case"self-declined":e.onDeclined&&e.onDeclined(v),e.ignoreDeclined||(y=new Error("Aborted because of self decline: "+v.moduleId+g));break;case"declined":e.onDeclined&&e.onDeclined(v),e.ignoreDeclined||(y=new Error("Aborted because of declined dependency: "+v.moduleId+" in "+v.parentId+g));break;case"unaccepted":e.onUnaccepted&&e.onUnaccepted(v),e.ignoreUnaccepted||(y=new Error("Aborted because "+p+" is not accepted"+g));break;case"accepted":e.onAccepted&&e.onAccepted(v),m=!0;break;case"disposed":e.onDisposed&&e.onDisposed(v),b=!0;break;default:throw new Error("Unexception type "+v.type)}if(y)return{error:y};if(m)for(p in u[p]=h,c(l,v.outdatedModules),v.outdatedDependencies)d.o(v.outdatedDependencies,p)&&(s[p]||(s[p]=[]),c(s[p],v.outdatedDependencies[p]));b&&(c(l,[v.moduleId]),u[p]=f)}t=void 0;for(var _,E=[],I=0;I<l.length;I++){var A=l[I],O=d.c[A];O&&(O.hot._selfAccepted||O.hot._main)&&u[A]!==f&&!O.hot._selfInvalidated&&E.push({module:A,require:O.hot._requireSelf,errorHandler:O.hot._selfAccepted})}return{dispose:function(){var e;o.forEach((function(e){delete r[e]})),o=void 0;for(var n,t=l.slice();t.length>0;){var i=t.pop(),a=d.c[i];if(a){var c={},u=a.hot._disposeHandlers;for(I=0;I<u.length;I++)u[I].call(null,c);for(d.hmrD[i]=c,a.hot.active=!1,delete d.c[i],delete s[i],I=0;I<a.children.length;I++){var f=d.c[a.children[I]];f&&((e=f.parents.indexOf(i))>=0&&f.parents.splice(e,1))}}}for(var p in s)if(d.o(s,p)&&(a=d.c[p]))for(_=s[p],I=0;I<_.length;I++)n=_[I],(e=a.children.indexOf(n))>=0&&a.children.splice(e,1)},apply:function(r){for(var n in u)d.o(u,n)&&(d.m[n]=u[n]);for(var t=0;t<i.length;t++)i[t](d);for(var o in s)if(d.o(s,o)){var a=d.c[o];if(a){_=s[o];for(var c=[],f=[],p=[],h=0;h<_.length;h++){var v=_[h],y=a.hot._acceptedDependencies[v],m=a.hot._acceptedErrorHandlers[v];if(y){if(-1!==c.indexOf(y))continue;c.push(y),f.push(m),p.push(v)}}for(var b=0;b<c.length;b++)try{c[b].call(null,_)}catch(n){if("function"==typeof f[b])try{f[b](n,{moduleId:o,dependencyId:p[b]})}catch(t){e.onErrored&&e.onErrored({type:"accept-error-handler-errored",moduleId:o,dependencyId:p[b],error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"accept-errored",moduleId:o,dependencyId:p[b],error:n}),e.ignoreErrored||r(n)}}}for(var g=0;g<E.length;g++){var I=E[g],A=I.module;try{I.require(A)}catch(n){if("function"==typeof I.errorHandler)try{I.errorHandler(n,{moduleId:A,module:d.c[A]})}catch(t){e.onErrored&&e.onErrored({type:"self-accept-error-handler-errored",moduleId:A,error:t,originalError:n}),e.ignoreErrored||(r(t),r(n))}else e.onErrored&&e.onErrored({type:"self-accept-errored",moduleId:A,error:n}),e.ignoreErrored||r(n)}}return l}}}self.webpackHotUpdateGUI=(r,n,o)=>{for(var c in n)d.o(n,c)&&(t[c]=n[c],e&&e.push(c));o&&i.push(o),a[r]&&(a[r](),a[r]=void 0)},d.hmrI.jsonp=function(e,r){t||(t={},i=[],o=[],r.push(s)),d.o(t,e)||(t[e]=d.m[e])},d.hmrC.jsonp=function(e,a,l,u,f,p){f.push(s),n={},o=a,t=l.reduce((function(e,r){return e[r]=!1,e}),{}),i=[],e.forEach((function(e){d.o(r,e)&&void 0!==r[e]?(u.push(c(e,p)),n[e]=!0):n[e]=!1})),d.f&&(d.f.jsonpHmr=function(e,r){n&&d.o(n,e)&&!n[e]&&(r.push(c(e)),n[e]=!0)})},d.hmrM=()=>{if("undefined"==typeof fetch)throw new Error("No browser support: need fetch API");return fetch(d.p+d.hmrF()).then((e=>{if(404!==e.status){if(!e.ok)throw new Error("Failed to fetch update manifest "+e.statusText);return e.json()}}))},d.O.j=e=>0===r[e];var l=(e,n)=>{var t,o,[i,a,c]=n,s=0;if(i.some((e=>0!==r[e]))){for(t in a)d.o(a,t)&&(d.m[t]=a[t]);if(c)var l=c(d)}for(e&&e(n);s<i.length;s++)o=i[s],d.o(r,o)&&r[o]&&r[o][0](),r[o]=0;return d.O(l)},u=self.webpackChunkGUI=self.webpackChunkGUI||[];u.forEach(l.bind(null,0)),u.push=l.bind(null,u.push.bind(u))})(),d.nc=void 0,d.O(void 0,[340,644,96,223],(()=>d(42440))),d.O(void 0,[340,644,96,223],(()=>d(63943)));var c=d.O(void 0,[340,644,96,223],(()=>d(32284)));return c=d.O(c)})()));