{"version": 3, "file": "teaching-records.dto.js", "sourceRoot": "", "sources": ["../../../../src/course/teaching/dto/teaching-records.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAiF;AACjF,yDAAyC;AAKzC,MAAa,0BAA0B;IAWrC,IAAI,GAAY,CAAC,CAAC;IAalB,QAAQ,GAAY,EAAE,CAAC;IAYvB,SAAS,CAAU;IAYnB,QAAQ,CAAU;IAYlB,OAAO,CAAU;IAWjB,MAAM,CAAU;IAUhB,SAAS,CAAU;IAUnB,OAAO,CAAU;CAClB;AA5FD,gEA4FC;AAjFC;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC7B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC9B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;wDACD;AAalB;IAXC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;4DACI;AAYvB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,GAAG;QACZ,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;6DACA;AAYnB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;4DACD;AAYlB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAC/B,IAAA,qBAAG,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAChC,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;2DACF;AAWjB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,EAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC7B,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;;0DACH;AAUhB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,MAAM;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;6DACjB;AAUnB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,MAAM;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;;2DACnB;AAMnB,MAAa,qBAAqB;IAEhC,EAAE,CAAS;IAGX,QAAQ,CAAS;IAGjB,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,OAAO,CAAS;IAGhB,SAAS,CAAS;IAGlB,SAAS,CAAS;IAGlB,WAAW,CAAS;IAGpB,MAAM,CAAS;IAGf,WAAW,CAAS;IAGpB,eAAe,CAAS;IAGxB,YAAY,CAAS;IAGrB,eAAe,CAAS;IAGxB,eAAe,CAAS;IAGxB,kBAAkB,CAAS;IAG3B,YAAY,CAAS;IAGrB,SAAS,CAAS;IAGlB,SAAS,CAAS;CACnB;AAtDD,sDAsDC;AApDC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;iDACvC;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;uDACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;;yDAC9C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC;;yDAC5C;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;sDAClC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;;wDAC3C;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;wDACjC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;0DACjC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDAChD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;0DAChC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8DAC/B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2DAC/B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8DAC5B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DAC/B;AAGxB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iEAC9B;AAG3B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;;2DACrD;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;wDACpD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;wDACpD;AAMpB,MAAa,iBAAiB;IAE5B,IAAI,CAAS;IAGb,QAAQ,CAAS;IAGjB,KAAK,CAAS;IAGd,UAAU,CAAS;IAGnB,OAAO,CAAU;IAGjB,OAAO,CAAU;CAClB;AAlBD,8CAkBC;AAhBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CACpC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;mDACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDACnC;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDAC7B;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACtC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACtC;AAMnB,MAAa,sBAAsB;IAEjC,IAAI,CAA0B;IAG9B,UAAU,CAAoB;CAC/B;AAND,wDAMC;AAJC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,qBAAqB,CAAC,EAAE,CAAC;;oDACxC;AAG9B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;8BAClD,iBAAiB;0DAAC;AAMhC,MAAa,0BAA0B;IAErC,IAAI,CAAS;IAGb,OAAO,CAAS;IAGhB,IAAI,CAAyB;CAC9B;AATD,gEASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;wDACvC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;2DACzC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC;8BAC7D,sBAAsB;wDAAC"}