"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassTasks.tsx":
/*!*************************************************!*\
  !*** ./app/workbench/components/ClassTasks.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/delete.js\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ClassTasks.module.css */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.module.css\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.tsx\");\n/* harmony import */ var _teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../teacher-space/components/task-detail-modal */ \"(app-pages-browser)/./app/teacher-space/components/task-detail-modal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../teacher-space/components/modals/edit-task-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/edit-task-modal.tsx\");\n/* harmony import */ var _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/utils/task-event-manager */ \"(app-pages-browser)/./app/utils/task-event-manager.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* harmony import */ var _lib_utils_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/user */ \"(app-pages-browser)/./lib/utils/user.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ClassTasks = ()=>{\n    _s();\n    // 移除对模板上下文的依赖，只使用全局事件\n    // const { notifyTaskPublished } = useTemplate();\n    // 获取状态对应的CSS类名\n    const getStatusClassName = (status)=>{\n        switch(status){\n            case \"已完成\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().completed);\n            case \"进行中\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().inProgress);\n            case \"已结束\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().ended);\n            case \"未开始\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().notStarted);\n            default:\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default()[\"default\"]);\n        }\n    };\n    // 数据状态\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 发布任务多步骤弹窗状态\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishTaskModalOpen, setIsPublishTaskModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishModalData, setPublishModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"\",\n        energyAmount: \"\",\n        selectedTemplate: {\n            id: 0\n        },\n        selectedStudents: []\n    });\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务详情弹窗状态\n    const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForDetail, setSelectedTaskForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务编辑弹窗状态\n    const [isEditTaskModalVisible, setIsEditTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForEdit, setSelectedTaskForEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 选择状态\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allSchools, setAllSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有学校数据\n    const [allClasses, setAllClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有班级数据\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"全部\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFilterExpanded, setIsFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSchoolFilterExpanded, setIsSchoolFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClassFilterExpanded, setIsClassFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isStatusFilterExpanded, setIsStatusFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSidebarCollapsed, setIsSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 监听屏幕尺寸变化，大屏幕时自动展开筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth >= 1024) {\n                setIsFilterExpanded(true);\n            }\n        };\n        handleResize(); // 初始化时检查\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    const statusOptions = [\n        \"全部\",\n        \"已完成\",\n        \"进行中\",\n        \"未开始\",\n        \"已结束\"\n    ];\n    // 获取用户关联的学校列表\n    const loadUserSchools = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n            setSchools(schoolsData);\n            setAllSchools(schoolsData); // 保存所有学校数据\n            // 默认不选择任何学校，显示\"全部\"状态\n            setSelectedSchool(null);\n            setSelectedClass(null);\n            // 加载所有学校的所有班级\n            await loadAllClasses(schoolsData);\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setError(error.message || \"网络连接失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载所有学校的所有班级\n    const loadAllClasses = async (schoolsData)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setAllClasses([]);\n                return;\n            }\n            let allClassesData = [];\n            // 遍历所有学校，获取每个学校的班级\n            for (const school of schoolsData){\n                try {\n                    const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(school.id, teacherId);\n                    allClassesData = [\n                        ...allClassesData,\n                        ...classesData\n                    ];\n                } catch (error) {\n                    console.error(\"获取学校 \".concat(school.schoolName, \" 的班级失败:\"), error);\n                }\n            }\n            setClasses(allClassesData);\n            setAllClasses(allClassesData);\n            console.log(\"加载所有班级完成，总数:\", allClassesData.length);\n        } catch (error) {\n            console.error(\"加载所有班级失败:\", error);\n            setClasses([]);\n            setAllClasses([]);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用教师班级API\n    const loadSchoolClasses = async (schoolId)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setSelectedClass(null);\n                return;\n            }\n            const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(schoolId, teacherId);\n            setClasses(classesData);\n            // 默认选择第一个班级\n            if (classesData.length > 0) {\n                setSelectedClass(classesData[0]);\n                console.log(\"默认选择班级:\", classesData[0].className, \"(ID:\", classesData[0].id, \")\");\n            } else {\n                setSelectedClass(null);\n                setStudents([]);\n                console.log(\"没有班级数据，清空选择\");\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            setClasses([]);\n            setSelectedClass(null);\n        }\n    };\n    // 处理班级选择变化\n    const handleClassChange = (classItem)=>{\n        console.log(\"=== 切换班级 ===\");\n        console.log(\"从班级:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className, \"(ID:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id, \")\");\n        console.log(\"切换到班级:\", (classItem === null || classItem === void 0 ? void 0 : classItem.className) || \"全部\", \"(ID:\", (classItem === null || classItem === void 0 ? void 0 : classItem.id) || \"all\", \")\");\n        // 如果点击的是当前已选中的班级，不需要切换\n        if ((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === (classItem === null || classItem === void 0 ? void 0 : classItem.id)) {\n            console.log(\"点击的是当前班级，无需切换\");\n            return;\n        }\n        // 更新选中的班级\n        setSelectedClass(classItem);\n        console.log(\"班级切换完成\");\n    };\n    // 处理学校选择变化\n    const handleSchoolChange = (school)=>{\n        console.log(\"=== 切换学校 ===\");\n        console.log(\"从学校:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName, \"(ID:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id, \")\");\n        console.log(\"切换到学校:\", (school === null || school === void 0 ? void 0 : school.schoolName) || \"全部\", \"(ID:\", (school === null || school === void 0 ? void 0 : school.id) || \"all\", \")\");\n        setSelectedClass(null);\n        setSelectedSchool(school);\n        if (school === null) {\n            // 选择\"全部学校\"，显示所有班级\n            setClasses(allClasses);\n            console.log(\"选择全部学校，显示所有班级:\", allClasses.length);\n        } else {\n            // 选择特定学校，只显示该学校的班级\n            console.log(\"选择特定学校，开始获取该学校的班级...\");\n            loadSchoolClasses(school.id);\n        }\n    };\n    // 获取班级的真实任务数据 - 使用工具函数\n    const loadClassTasks = async (classId)=>{\n        try {\n            return await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchClassTasks)(classId);\n        } catch (error) {\n            console.error(\"获取班级任务数据失败:\", error);\n            return [];\n        }\n    };\n    // 组件挂载时获取数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserSchools();\n    }, []);\n    // 监听selectedClass变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"selectedClass状态变化:\", selectedClass);\n    }, [\n        selectedClass\n    ]);\n    // 当选择的班级改变时，加载任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass) {\n            console.log(\"开始加载班级任务数据:\", selectedClass.className);\n            loadTasksData();\n        } else if (selectedClass === null && classes.length > 0) {\n            console.log(\"选择全部班级，加载所有班级任务数据\");\n            loadAllTasksData();\n        } else {\n            console.log(\"没有班级数据，清空任务数据\");\n            setTasks([]);\n        }\n    }, [\n        selectedClass\n    ]);\n    // 当班级列表变化且没有选中特定班级时，重新加载全部任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass === null && classes.length > 0) {\n            console.log(\"班级列表更新，重新加载全部班级任务数据\");\n            loadAllTasksData();\n        }\n    }, [\n        classes.length\n    ]); // 只监听班级数量变化，避免频繁重新渲染\n    // 获取任务数据（使用真实API）\n    const loadTasksData = async ()=>{\n        if (!selectedClass) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            const tasksData = await loadClassTasks(selectedClass.id);\n            setTasks(tasksData);\n        } catch (error) {\n            console.error(\"加载任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取所有班级的任务数据\n    const loadAllTasksData = async ()=>{\n        if (!classes || classes.length === 0) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            let allTasksData = [];\n            // 遍历所有班级，获取每个班级的任务\n            for (const classItem of classes){\n                try {\n                    const tasksData = await loadClassTasks(classItem.id);\n                    allTasksData = [\n                        ...allTasksData,\n                        ...tasksData\n                    ];\n                } catch (error) {\n                    console.error(\"获取班级 \".concat(classItem.className, \" 的任务失败:\"), error);\n                }\n            }\n            setTasks(allTasksData);\n            console.log(\"加载所有任务完成，总数:\", allTasksData.length);\n        } catch (error) {\n            console.error(\"加载所有任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 计算剩余时间\n    const calculateRemainingTime = (task)=>{\n        // 如果任务已完成或已结束，返回 \"-\"\n        if (task.status === \"已完成\" || task.status === \"已结束\") {\n            return \"-\";\n        }\n        // 解析截止时间\n        const deadline = new Date(task.deadline);\n        const now = new Date();\n        // 如果截止时间已过，返回 \"已超时\"\n        if (deadline < now) {\n            return \"已超时\";\n        }\n        // 计算剩余时间（毫秒）\n        const diffMs = deadline.getTime() - now.getTime();\n        // 计算剩余天数（不向上取整，精确计算）\n        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n        if (diffDays >= 1) {\n            // 剩余1天或以上，显示天数\n            return \"\".concat(diffDays, \"天\");\n        } else {\n            // 不足1天，显示小时数\n            const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));\n            if (diffHours <= 0) {\n                return \"已超时\";\n            }\n            return \"\".concat(diffHours, \"小时\");\n        }\n    };\n    // 获取剩余时间的样式类\n    const getRemainingTimeClass = (task)=>{\n        const remainingTime = calculateRemainingTime(task);\n        if (remainingTime === \"-\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished);\n        } else if (remainingTime === \"已超时\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue);\n        } else if (remainingTime.includes(\"小时\") || remainingTime === \"1天\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent);\n        } else {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal);\n        }\n    };\n    // 根据筛选条件过滤任务数据\n    const getFilteredTasks = ()=>{\n        const filters = {\n            statusFilter,\n            startDate,\n            endDate,\n            searchQuery\n        };\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_10__.filterTasks)(tasks, filters);\n    };\n    // 处理日期输入框点击事件\n    const handleDateInputClick = (event)=>{\n        var _input_showPicker;\n        const input = event.currentTarget;\n        (_input_showPicker = input.showPicker) === null || _input_showPicker === void 0 ? void 0 : _input_showPicker.call(input);\n    };\n    // 处理开始日期变化\n    const handleStartDateChange = (event)=>{\n        const newStartDate = event.target.value;\n        setStartDate(newStartDate);\n        // 如果开始日期晚于结束日期，清空结束日期\n        if (endDate && newStartDate > endDate) {\n            setEndDate(\"\");\n        }\n    };\n    // 处理结束日期变化\n    const handleEndDateChange = (event)=>{\n        const newEndDate = event.target.value;\n        // 如果结束日期早于开始日期，不允许设置\n        if (startDate && newEndDate < startDate) {\n            alert(\"结束日期不能早于开始日期\");\n            return;\n        }\n        setEndDate(newEndDate);\n    };\n    // 处理刷新列表\n    const handleRefreshList = async ()=>{\n        try {\n            setRefreshing(true);\n            // 如果没有选择学校，重新获取学校数据\n            if (!selectedSchool) {\n                console.log(\"没有选择学校，重新获取学校数据...\");\n                await loadUserSchools();\n                return;\n            }\n            // 如果没有选择班级，重新获取班级数据\n            if (!selectedClass) {\n                console.log(\"没有选择班级，重新获取班级数据...\");\n                await loadSchoolClasses(selectedSchool.id);\n                return;\n            }\n            // 重新获取当前班级的任务数据\n            console.log(\"重新获取班级任务数据...\");\n            await loadTasksData();\n            // 显示刷新成功的提示\n            setTimeout(()=>{\n                getFilteredTasks();\n            }, 100);\n        } catch (error) {\n            console.error(\"❌ 刷新任务列表失败:\", error);\n        // 这里可以添加错误提示给用户\n        } finally{\n            setRefreshing(false);\n        }\n    };\n    // 重置筛选条件\n    const handleResetFilters = ()=>{\n        setStartDate(\"\");\n        setEndDate(\"\");\n        setStatusFilter(\"全部\");\n        setSearchQuery(\"\");\n        console.log(\"已重置所有筛选条件\");\n    };\n    // 处理发布任务按钮点击 - 启动多步骤流程\n    const handlePublishTaskClick = async ()=>{\n        // 如果已经选择了学校和班级，直接跳到模板选择步骤\n        if (selectedSchool && selectedClass) {\n            setModalSelectedSchool(selectedSchool);\n            setIsTemplateModalOpen(true);\n        } else {\n            try {\n                // 获取用户的学校列表\n                const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            } catch (error) {\n                console.error(\"获取学校列表失败:\", error);\n                // 出错时仍然显示学校选择弹窗\n                setIsSchoolModalOpen(true);\n            }\n        }\n    };\n    // 处理学校选择完成\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    // 处理班级选择完成\n    const handleClassSelect = ()=>{\n        setIsClassModalOpen(false);\n        // 发布任务直接跳过模板选择，进入发布任务弹窗\n        // 设置默认的模态框数据\n        setPublishModalData({\n            selectedDistribution: \"\",\n            energyAmount: \"\",\n            selectedTemplate: {\n                id: 0\n            },\n            selectedStudents: []\n        });\n        setIsPublishTaskModalOpen(true);\n    };\n    // 处理最终发布任务确认 - 使用工具函数重构\n    const handlePublishTaskConfirm = async (taskData)=>{\n        try {\n            var _publishModalData_selectedTemplate;\n            console.log(\"发布任务数据:\", taskData);\n            console.log(\"模板数据:\", publishModalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const userId = userData ? JSON.parse(userData).userId : 0;\n            // 准备发布参数\n            const values = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskContent: taskData.taskDescription || \"\",\n                startDate: new Date(),\n                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                selfAssessmentItems: taskData.selfAssessmentItems || [],\n                allowLateSubmission: true,\n                templateId: ((_publishModalData_selectedTemplate = publishModalData.selectedTemplate) === null || _publishModalData_selectedTemplate === void 0 ? void 0 : _publishModalData_selectedTemplate.id) || 0,\n                lateSubmissionPolicy: {\n                    deductionPerDay: 5,\n                    maxDeductionDays: 7,\n                    minScore: 0\n                }\n            };\n            const selectedStudentIds = publishModalData.selectedStudents.length > 0 ? publishModalData.selectedStudents : students.map((s)=>s.id);\n            // 调用工具函数发布任务\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.publishTask)(values, selectedClass, userId, selectedStudentIds, students, [] // 附件列表\n            );\n            if (success) {\n                // 关闭所有弹窗并重置状态\n                setIsPublishTaskModalOpen(false);\n                setPublishModalData({\n                    selectedDistribution: \"\",\n                    energyAmount: \"\",\n                    selectedTemplate: {\n                        id: 0\n                    },\n                    selectedStudents: []\n                });\n                // 刷新任务列表\n                await loadTasksData();\n                // 触发全局任务发布事件\n                const eventData = {\n                    taskId: 0,\n                    taskName: taskData.taskName,\n                    classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                    className: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className,\n                    teacherId: userId\n                };\n                _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__[\"default\"].emit(_app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__.TASK_EVENTS.TASK_PUBLISHED, eventData);\n            }\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].error({\n                message: \"任务发布失败\",\n                description: \"网络错误或服务器异常，请稍后重试\"\n            });\n        }\n    };\n    // 处理任务查看\n    const handleTaskView = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleViewTask)(task);\n            setSelectedTaskForDetail(taskDetail);\n            setIsTaskDetailModalVisible(true);\n        } catch (error) {\n            console.error(\"查看任务失败:\", error);\n            setSelectedTaskForDetail(task);\n            setIsTaskDetailModalVisible(true);\n        }\n    };\n    // 处理任务编辑\n    const handleTaskEdit = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleEditTask)(task);\n            setSelectedTaskForEdit(taskDetail);\n            setIsEditTaskModalVisible(true);\n        } catch (error) {\n            console.error(\"编辑任务失败:\", error);\n            setSelectedTaskForEdit(task);\n            setIsEditTaskModalVisible(true);\n        }\n    };\n    // 处理任务删除\n    const handleTaskDelete = async (task)=>{\n        const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleDeleteTask)(task, loadTasksData);\n        if (success) {\n            console.log(\"任务删除成功\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().classTasksContainer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainLayout),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().leftSidebar), \" \").concat(isSidebarCollapsed ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed) : \"\"),\n                    children: isSidebarCollapsed ? /* 收缩状态：显示白色长方形条 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsedSidebar),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expandBtn),\n                            onClick: ()=>setIsSidebarCollapsed(false),\n                            title: \"展开筛选\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarTitle),\n                                        children: \"筛选\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                        onClick: ()=>setIsSidebarCollapsed(true),\n                                        title: \"收缩筛选\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterContent), \" \").concat(isFilterExpanded ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expanded) : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedSchool ? selectedSchool.schoolName : \"全部学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsSchoolFilterExpanded(!isSchoolFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isSchoolFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"加载中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ff4d4f\"\n                                                    },\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, undefined) : !Array.isArray(schools) || schools.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"暂无数据\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedSchool === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                            onClick: ()=>handleSchoolChange(null),\n                                                            title: \"全部学校\",\n                                                            children: \"全部学校\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>handleSchoolChange(school),\n                                                                title: school.schoolName,\n                                                                children: school.schoolName\n                                                            }, school.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedClass ? selectedClass.className : \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsClassFilterExpanded(!isClassFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isClassFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: [\n                                                    (()=>{\n                                                        return null;\n                                                    })(),\n                                                    !Array.isArray(classes) || classes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"暂无班级数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedClass === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>{\n                                                                    console.log(\"全部班级按钮被点击\");\n                                                                    handleClassChange(null);\n                                                                },\n                                                                title: \"全部班级\",\n                                                                children: \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === classItem.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                    onClick: ()=>{\n                                                                        console.log(\"班级按钮被点击:\", classItem.className);\n                                                                        handleClassChange(classItem);\n                                                                    },\n                                                                    title: classItem.className,\n                                                                    children: classItem.className\n                                                                }, classItem.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"所有类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: statusFilter\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsStatusFilterExpanded(!isStatusFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isStatusFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(statusFilter === status ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                        onClick: ()=>setStatusFilter(status),\n                                                        title: status,\n                                                        children: status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateRange),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: startDate,\n                                                                onChange: handleStartDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: \"\",\n                                                                title: \"开始日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !startDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"开始\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 36\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateSeparator),\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: endDate,\n                                                                onChange: handleEndDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: startDate || \"\",\n                                                                title: \"结束日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !endDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"结束\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 34\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    (startDate || endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().clearDateBtn),\n                                                        onClick: ()=>{\n                                                            setStartDate(\"\");\n                                                            setEndDate(\"\");\n                                                        },\n                                                        title: \"清除日期筛选\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().rightContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchBox),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: 18,\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchIcon)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 876,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索任务名称、任务内容...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchInput)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 877,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().publishTaskBtn),\n                                    onClick: handlePublishTaskClick,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"发布任务\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tasksTable),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableScrollContainer),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableHeader),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"剩余时间\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"完成率\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 902,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"操作\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 898,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableBody),\n                                            children: getFilteredTasks().length > 0 ? getFilteredTasks().map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableRow),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: task.taskName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().statusBadge), \" \").concat(getStatusClassName(task.status)),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 913,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: (()=>{\n                                                                const remainingTime = calculateRemainingTime(task);\n                                                                let className = (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTime);\n                                                                if (remainingTime === \"-\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished));\n                                                                } else if (remainingTime === \"已超时\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue));\n                                                                } else if (remainingTime.includes(\"小时\")) {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent));\n                                                                } else {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal));\n                                                                }\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: className,\n                                                                    children: remainingTime\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 935,\n                                                                    columnNumber: 23\n                                                                }, undefined);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 919,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: [\n                                                                task.completionRate,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actions),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().viewBtn)),\n                                                                        onClick: ()=>handleTaskView(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"查看\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 944,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().editBtn)),\n                                                                        onClick: ()=>handleTaskEdit(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"编辑\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 951,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().deleteBtn)),\n                                                                        onClick: ()=>handleTaskDelete(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"删除\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 943,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 15\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyState),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyIcon),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"64\",\n                                                            height: \"64\",\n                                                            viewBox: \"0 0 64 64\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"32\",\n                                                                    cy: \"32\",\n                                                                    r: \"30\",\n                                                                    stroke: \"#E5E7EB\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"#F9FAFB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M24 28h16M24 32h12M24 36h8\",\n                                                                    stroke: \"#9CA3AF\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"44\",\n                                                                    cy: \"20\",\n                                                                    r: \"8\",\n                                                                    fill: \"#FEF3C7\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M41 20h6M44 17v6\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyTitle),\n                                                        children: \"暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyDescription),\n                                                        children: classes.length === 0 ? \"暂无班级数据\" : selectedClass === null ? \"当前显示所有班级的任务数据\" : \"当前班级暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 896,\n                                columnNumber: 9\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 895,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            isOpen: isSchoolModalOpen,\n                            onClose: ()=>setIsSchoolModalOpen(false),\n                            onSchoolSelect: handleSchoolSelect,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            isOpen: isClassModalOpen,\n                            onClose: ()=>setIsClassModalOpen(false),\n                            onBack: ()=>{\n                                setIsClassModalOpen(false);\n                                setIsSchoolModalOpen(true);\n                            },\n                            onClassSelect: handleClassSelect,\n                            selectedSchool: modalSelectedSchool,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            isOpen: isTemplateModalOpen,\n                            onClose: ()=>setIsTemplateModalOpen(false),\n                            onBack: ()=>{\n                                setIsTemplateModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            selectedSchool: modalSelectedSchool,\n                            selectedClass: selectedClass,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1011,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isOpen: isPublishTaskModalOpen,\n                            onClose: ()=>setIsPublishTaskModalOpen(false),\n                            onBack: ()=>{\n                                setIsPublishTaskModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            modalData: publishModalData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__.TaskDetailModal, {\n                            task: selectedTaskForDetail,\n                            visible: isTaskDetailModalVisible,\n                            onClose: ()=>{\n                                setIsTaskDetailModalVisible(false);\n                                setSelectedTaskForDetail(null);\n                            },\n                            students: students.map((student)=>({\n                                    id: student.id,\n                                    userId: student.id,\n                                    studentNumber: \"\".concat(student.id),\n                                    nickName: student.name,\n                                    avatarUrl: \"\",\n                                    availablePoints: 0,\n                                    totalPoints: 0,\n                                    classId: student.classId,\n                                    className: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className) || \"\",\n                                    schoolId: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) || 0,\n                                    schoolName: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName) || \"\"\n                                })),\n                            onRefresh: loadTasksData,\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1036,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__.EditTaskModal, {\n                            visible: isEditTaskModalVisible,\n                            task: selectedTaskForEdit,\n                            onClose: ()=>{\n                                setIsEditTaskModalVisible(false);\n                                setSelectedTaskForEdit(null);\n                            },\n                            onSuccess: ()=>{\n                                // 编辑成功后刷新任务列表\n                                loadTasksData();\n                            },\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1061,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 872,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n            lineNumber: 643,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n        lineNumber: 641,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassTasks, \"mULc1hhR6Iaw9KD3Igc769HEwBc=\");\n_c = ClassTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassTasks);\nvar _c;\n$RefreshReg$(_c, \"ClassTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\n"));

/***/ })

});