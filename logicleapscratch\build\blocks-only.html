<!doctype html><html><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="google" value="notranslate"><link rel="shortcut icon" href="static/favicon.ico"><title>LogicLeapingAI: Blocks Only Example</title><style>#initial-loader{position:fixed;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,#4766c2,#2a3d7c);display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:99999999;opacity:1;visibility:visible;transition:opacity .8s ease,visibility .8s ease}#initial-loader.fade-out{opacity:0;visibility:hidden}.loader-container{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;max-width:300px;height:400px;transform:scale(1);transition:transform .8s ease}#initial-loader.fade-out .loader-container{transform:scale(.95)}.svg-container{position:relative;width:150px;height:150px;margin-bottom:120px}.gegga{width:0}.snurra{position:absolute;top:0;left:0;width:100%;height:100%;filter:url(#gegga)}.skugga{position:absolute;top:0;left:0;width:100%;height:100%;filter:blur(5px);opacity:.3;transform:translate(3px,3px)}.stopp1{stop-color:#4766C2}.stopp2{stop-color:#96b3ff}.halvan{animation:Snurra1 10s infinite linear;stroke-dasharray:180 800;fill:none;stroke:url(#gradient);stroke-width:23;stroke-linecap:round}.strecken{animation:Snurra1 3s infinite linear;stroke-dasharray:26 54;fill:none;stroke:url(#gradient);stroke-width:23;stroke-linecap:round}@keyframes Snurra1{0%{stroke-dashoffset:0}100%{stroke-dashoffset:-403px}}.text-container{position:absolute;bottom:40px;left:0;right:0;text-align:center;min-height:120px;display:flex;flex-direction:column;justify-content:center;align-items:center}.progress-text{color:#fff;font-size:48px;line-height:1;font-weight:700;text-shadow:0 0 20px rgba(71,102,194,.5);margin-bottom:1.5rem;min-width:100px;text-align:center;font-family:Arial,sans-serif}.message{color:rgba(255,255,255,.9);font-size:16px;line-height:1.2;text-shadow:0 0 10px rgba(71,102,194,.3);white-space:nowrap;font-family:Arial,sans-serif;min-height:20px}#app{opacity:0;transition:opacity .8s ease}#app.loaded{opacity:1}</style><script defer="defer" src="/logicleap/chunks/tensorflow.2cd86211bdb26685885d.js"></script><script defer="defer" src="/logicleap/chunks/react.36dc9d8d8b793c84f2c4.js"></script><script defer="defer" src="/logicleap/chunks/vendors.e3e8a55fe48004c591a3.js"></script><script defer="defer" src="/logicleap/chunks/commons.91314337026827ea538d.js"></script><script defer="defer" src="/logicleap/static/js/blocksonly.c7825f3b0c3f421615b8.js"></script></head><body><div id="initial-loader"><div class="loader-container"><div class="svg-container"><svg class="gegga"><defs><filter id="gegga"><feGaussianBlur in="SourceGraphic" stdDeviation="7" result="blur"/><feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 20 -10" result="inreGegga"/><feComposite in="SourceGraphic" in2="inreGegga" operator="atop"/></filter></defs></svg> <svg class="snurra" width="200" height="200" viewBox="0 0 200 200"><defs><linearGradient id="linjärGradient"><stop class="stopp1" offset="0"/><stop class="stopp2" offset="1"/></linearGradient><linearGradient y2="160" x2="160" y1="40" x1="40" gradientUnits="userSpaceOnUse" id="gradient" xlink:href="#linjärGradient"/></defs><path class="halvan" d="m 164,100 c 0,-35.346224 -28.65378,-64 -64,-64 -35.346224,0 -64,28.653776 -64,64 0,35.34622 28.653776,64 64,64 35.34622,0 64,-26.21502 64,-64 0,-37.784981 -26.92058,-64 -64,-64 -37.079421,0 -65.267479,26.922736 -64,64 1.267479,37.07726 26.703171,65.05317 64,64 37.29683,-1.05317 64,-64 64,-64"/><circle class="strecken" cx="100" cy="100" r="64"/></svg> <svg class="skugga" width="200" height="200" viewBox="0 0 200 200"><path class="halvan" d="m 164,100 c 0,-35.346224 -28.65378,-64 -64,-64 -35.346224,0 -64,28.653776 -64,64 0,35.34622 28.653776,64 64,64 35.34622,0 64,-26.21502 64,-64 0,-37.784981 -26.92058,-64 -64,-64 -37.079421,0 -65.267479,26.922736 -64,64 1.267479,37.07726 26.703171,65.05317 64,64 37.29683,-1.05317 64,-64 64,-64"/><circle class="strecken" cx="100" cy="100" r="64"/></svg></div><div class="text-container"><div class="progress-text">0%</div><div class="message">正在加载中...</div></div></div></div><div id="app"></div><script>function startBaseLoading(){const e=setInterval((()=>{window.currentProgress>=60||window.baseLoadingComplete?clearInterval(e):window.updateLoaderProgress(window.currentProgress+1)}),30);setTimeout((()=>{window.baseLoadingComplete=!0,window.currentProgress<60&&window.updateLoaderProgress(60)}),1800)}function calculateProgress(e,o){if(!window.baseLoadingComplete)return window.currentProgress;if(window.loadingProject)return window.currentProgress;const t=o>0?Math.floor(e/o*35):0;return Math.min(95,60+t)}window.initialLoadTime=Date.now(),window.loadedResources=0,window.totalResources=0,window.currentProgress=0,window.isCompleted=!1,window.guiLoaded=!1,window.baseLoadingComplete=!1,window.loadingProject=!1,window.updateLoaderProgress=function(e,o){window.isCompleted||requestAnimationFrame((()=>{e=Math.floor(Math.max(window.currentProgress,e)),window.currentProgress=e;const t=document.querySelector("#initial-loader .progress-text"),n=document.querySelector("#initial-loader .message");t&&(t.textContent=e+"%"),n&&!o&&(n.textContent=e<30?"正在加载中...":e<60?"准备环境中...":e<85?"加载编辑器...":"即将完成...")}))},window.updateLoaderMessage=function(e){const o=document.querySelector("#initial-loader .message");o&&e&&(o.textContent=e)},window.showLoader=function(e,o){const t=document.getElementById("initial-loader");if(t){window.isCompleted=!1,window.loadingProject=!0,window.currentProgress=e||60,t.style.display="flex",t.classList.remove("fade-out");const n=document.querySelector("#initial-loader .progress-text");n&&(n.textContent=window.currentProgress+"%"),o&&window.updateLoaderMessage(o);const r=document.getElementById("app");r&&r.classList.remove("loaded")}},window.completeLoading=function(){if(window.isCompleted)return;window.isCompleted=!0,window.loadingProject=!1;const e=document.getElementById("initial-loader"),o=document.getElementById("app");if(e&&o){const t=n=>{window.currentProgress=Math.floor(n);const r=document.querySelector("#initial-loader .progress-text");r&&(r.textContent=Math.floor(n)+"%"),n>=100?setTimeout((()=>{o.classList.add("loaded"),e.classList.add("fade-out"),setTimeout((()=>{e.style.display="none"}),800)}),200):requestAnimationFrame((()=>{t(n+.5)}))};t(window.currentProgress)}};const observer=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{if("resource"===e.entryType){window.loadedResources++;const e=calculateProgress(window.loadedResources,window.totalResources);window.updateLoaderProgress(e)}}))}));observer.observe({entryTypes:["resource"]}),window.addEventListener("DOMContentLoaded",(()=>{const e=performance.getEntriesByType("resource");window.totalResources=Math.max(e.length,1),window.loadedResources=e.filter((e=>e.duration>0)).length,startBaseLoading()})),window.onGUILoaded=function(){if(window.guiLoaded=!0,window.baseLoadingComplete){const e=calculateProgress(window.loadedResources,window.totalResources);window.updateLoaderProgress(e)}},window.addEventListener("load",(()=>{window.onGUILoaded(),setTimeout((()=>{window.loadingProject||window.completeLoading()}),500)}))</script><link rel="stylesheet" href=""></body></html>