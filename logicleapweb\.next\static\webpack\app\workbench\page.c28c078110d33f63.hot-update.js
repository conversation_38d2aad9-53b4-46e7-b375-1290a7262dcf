"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx":
/*!*************************************************************!*\
  !*** ./app/workbench/components/TemplateSelectionModal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _TemplatePickerModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplatePickerModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_task__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 导入API\n\n\n\n// 导入工具函数\n\nconst TemplateSelectionModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, actionType, selectedSchool, selectedClass } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredOption, setHoveredOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [focusedInput, setFocusedInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // 跟踪哪个输入框有焦点\n    // 输入框引用\n    const assignInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const distributeInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 移除教师能量相关状态，因为不需要检查教师能量池\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 学生相关状态\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentPointsMap, setStudentPointsMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [loadingStudentPoints, setLoadingStudentPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 存储模态框数据的状态\n    const [modalData, setModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"none\",\n        assignEnergyAmount: \"\",\n        distributeEnergyAmount: \"\",\n        selectedTemplate: null,\n        selectedStudents: []\n    });\n    // 输入验证错误状态\n    const [inputErrors, setInputErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        assignEnergyError: \"\",\n        distributeEnergyError: \"\"\n    });\n    // 当前步骤状态\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"template\");\n    // 发布任务相关状态\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        duration: \"1小时\",\n        startTime: \"\",\n        endTime: \"\"\n    });\n    // 持续时间选择器状态\n    const [showDurationSelector, setShowDurationSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 点击外部关闭持续时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (showDurationSelector && !target.closest(\".duration-field-container\")) {\n                setShowDurationSelector(false);\n            }\n        };\n        if (showDurationSelector) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationSelector\n    ]);\n    // 持续时间选择函数\n    const handleDurationSelect = (duration)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                duration\n            }));\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布任务加载状态\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const pageSize = 10;\n    // 阻止背景页面滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            // 保存原始的 overflow 样式\n            const originalStyle = window.getComputedStyle(document.body).overflow;\n            // 阻止背景滚动\n            document.body.style.overflow = \"hidden\";\n            return ()=>{\n                // 恢复原始样式\n                document.body.style.overflow = originalStyle;\n            };\n        }\n    }, [\n        isOpen\n    ]);\n    // 获取作品列表（分页懒加载）\n    const fetchWorksData = async function() {\n        let pageNum = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, isLoadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const currentState = {\n            works,\n            selectedWorkIds,\n            page,\n            hasMore,\n            loadingWorks,\n            loadingMore\n        };\n        const setState = (newState)=>{\n            if (newState.works !== undefined) setWorks(newState.works);\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n            if (newState.page !== undefined) setPage(newState.page);\n            if (newState.hasMore !== undefined) setHasMore(newState.hasMore);\n            if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);\n            if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);\n        };\n        await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchWorks)(pageNum, pageSize, isLoadMore, currentState, setState);\n    };\n    // 加载更多作品\n    const loadMoreWorksData = ()=>{\n        if (!loadingMore && hasMore) {\n            const nextPage = page + 1;\n            fetchWorksData(nextPage, true);\n        }\n    };\n    // 选择作品（支持多选）\n    const handleSelectWorkData = (workId)=>{\n        const setState = (newState)=>{\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleSelectWork)(workId, selectedWorkIds, setState);\n    };\n    // 创建鼠标处理函数的包装器\n    const handleMouseDownWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseDown)(e, setDragState);\n    };\n    const handleMouseMoveWrapper = (e)=>{\n        const dragState = {\n            isDragging,\n            dragStart\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseMove)(e, dragState);\n    };\n    const handleMouseUpWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseUp)(e, setDragState);\n    };\n    const handleMouseLeaveWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseLeave)(e, setDragState);\n    };\n    // 当切换到发布任务步骤且选择资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStep === \"publish\" && activeTab === \"resources\" && mounted) {\n            // 重置作品状态\n            setWorks([]);\n            setPage(1);\n            setHasMore(true);\n            setSelectedWorkIds([]);\n            setLoadingMore(false);\n            fetchWorksData(1, false);\n        }\n    }, [\n        currentStep,\n        activeTab,\n        mounted\n    ]);\n    // 清空所有数据的函数\n    const clearAllData = ()=>{\n        // 重置步骤状态\n        setCurrentStep(\"template\");\n        // 重置模态框数据\n        setModalData({\n            selectedDistribution: \"\",\n            assignEnergyAmount: \"\",\n            distributeEnergyAmount: \"\",\n            selectedTemplate: null,\n            selectedStudents: []\n        });\n        // 重置错误状态\n        setInputErrors({\n            assignEnergyError: \"\",\n            distributeEnergyError: \"\"\n        });\n        // 重置任务数据\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            duration: \"1小时\",\n            startTime: \"\",\n            endTime: \"\"\n        });\n        // 重置其他状态\n        setActiveTab(\"task\");\n        setWorks([]);\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setHoveredOption(\"\");\n        setIsTemplatePickerOpen(false);\n        setIsBatchUseKeyPackageModalOpen(false);\n        setPage(1);\n        setHasMore(true);\n        setLoadingMore(false);\n        // 重置学生相关数据\n        setStudents([]);\n        setStudentPointsMap(new Map());\n    };\n    // 监听模态框关闭，清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            clearAllData();\n        }\n    }, [\n        isOpen\n    ]);\n    // 组件卸载时清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearAllData();\n        };\n    }, []);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 移除获取教师可分配能量的函数，因为不需要检查教师能量池\n    // 获取班级学生列表和能量信息（并行处理）\n    const fetchClassStudentsData = async ()=>{\n        if (!(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || !mounted) return;\n        setLoadingStudentPoints(true); // 提前设置能量加载状态\n        try {\n            const studentsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchClassStudentsWithNotification)(selectedClass.id, notification);\n            setStudents(studentsData);\n            // 立即并行获取学生能量信息，不等待学生列表完全处理完\n            if (studentsData.length > 0) {\n                // 不等待，立即开始获取能量信息\n                const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(studentsData.map((s)=>s.userId), notification);\n                setStudentPointsMap(pointsMap);\n            }\n            setLoadingStudentPoints(false);\n        } catch (error) {\n            console.error(\"获取学生列表失败:\", error);\n            setStudents([]);\n            setLoadingStudentPoints(false);\n        }\n    };\n    // 移除获取教师能量的 useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedClass) {\n            fetchClassStudentsData();\n            // 重置模态框数据\n            setModalData({\n                selectedDistribution: isBlocksOnlyMode ? \"none\" : \"none\",\n                assignEnergyAmount: \"\",\n                distributeEnergyAmount: \"\",\n                selectedTemplate: null,\n                selectedStudents: []\n            });\n            // 重置错误状态\n            setInputErrors({\n                assignEnergyError: \"\",\n                distributeEnergyError: \"\"\n            });\n            // 禁用body滚动\n            document.body.style.overflow = \"hidden\";\n        } else {\n            // 恢复body滚动\n            document.body.style.overflow = \"\";\n        }\n        // 清理函数：组件卸载时恢复滚动\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen,\n        selectedClass,\n        mounted\n    ]);\n    // 当学生列表加载完成后，自动选择所有学生\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (students.length > 0) {\n            setModalData((prev)=>({\n                    ...prev,\n                    selectedStudents: students.map((s)=>s.userId)\n                }));\n        }\n    }, [\n        students\n    ]);\n    const distributionOptions = [\n        {\n            id: \"none\",\n            label: \"不分配\",\n            description: \"保持原有设置\",\n            hasInput: false\n        },\n        {\n            id: \"assign\",\n            label: \"分配\",\n            description: \"分配给学生\",\n            hasInput: true\n        },\n        {\n            id: \"distribute\",\n            label: \"分配至\",\n            description: \"分配到指定位置\",\n            hasInput: true\n        }\n    ];\n    // 判断是否为纯积木分配模式（不涉及能量）\n    const isBlocksOnlyMode = actionType === \"分配积木\";\n    // 判断是否为纯能量分配模式（不涉及积木）\n    const isEnergyOnlyMode = actionType === \"分配能量\";\n    // 判断是否为纯发布任务模式（不涉及能量和积木分配）\n    const isPublishOnlyMode = actionType === \"发布任务\";\n    const handleDistributionSelect = (optionId)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedDistribution: optionId\n            }));\n        // 自动聚焦到对应的输入框\n        setTimeout(()=>{\n            if (optionId === \"assign\" && assignInputRef.current) {\n                assignInputRef.current.focus();\n            } else if (optionId === \"distribute\" && distributeInputRef.current) {\n                distributeInputRef.current.focus();\n            }\n        }, 100); // 延迟一点确保输入框已经渲染\n    };\n    const handleTemplateSelect = (template)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: template\n            }));\n    };\n    const handleCancelTemplate = ()=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: null\n            }));\n    };\n    const handleTemplatePickerOpen = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    const handleTemplatePickerClose = ()=>{\n        setIsTemplatePickerOpen(false);\n    };\n    // 处理批量兑换密钥模态框\n    const handleBatchUseKeyPackageModalOpen = ()=>{\n        setIsBatchUseKeyPackageModalOpen(true);\n    };\n    const handleBatchUseKeyPackageModalClose = ()=>{\n        setIsBatchUseKeyPackageModalOpen(false);\n    };\n    const handleBatchUseKeyPackageSuccess = async ()=>{\n        // 兑换成功后重新获取学生能量信息\n        if (students.length > 0) {\n            const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(students.map((s)=>s.userId), notification);\n            setStudentPointsMap(pointsMap);\n        }\n        notification.success(\"密钥兑换成功！\");\n    };\n    // 获取当前选中分配方式对应的能量数值\n    const getCurrentEnergyAmount = ()=>{\n        if (modalData.selectedDistribution === \"assign\") {\n            return modalData.assignEnergyAmount;\n        } else if (modalData.selectedDistribution === \"distribute\") {\n            return modalData.distributeEnergyAmount;\n        }\n        return \"\";\n    };\n    // 计算所有学生的最低可分配能量\n    const getMinAvailablePoints = ()=>{\n        if (modalData.selectedStudents.length === 0) return 0;\n        const selectedStudentPoints = modalData.selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n        return Math.min(...selectedStudentPoints);\n    };\n    // 获取当前分配方式的提示信息\n    const getEnergyDisplayInfo = ()=>{\n        if (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") {\n            return {\n                label: \"最低可分配能量\",\n                value: getMinAvailablePoints()\n            };\n        }\n        return {\n            label: \"\",\n            value: 0\n        };\n    };\n    const handleNext = ()=>{\n        console.log(\"选择的分配方式:\", modalData.selectedDistribution);\n        console.log(\"选择的模板:\", modalData.selectedTemplate);\n        // 在纯积木分配模式时，跳过能量验证\n        if (!isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\")) {\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            console.log(\"分配能量数量:\", currentEnergyAmount);\n            // 检查是否有输入错误\n            const errorKey = modalData.selectedDistribution === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n            if (inputErrors[errorKey]) {\n                notification.error(\"请修正输入错误后再继续\");\n                return;\n            }\n            // 检查能量数量是否有效\n            const energyAmountNum = Number(currentEnergyAmount);\n            if (!currentEnergyAmount || energyAmountNum <= 0) {\n                notification.error(\"请输入有效的分配能量数量\");\n                return;\n            }\n            if (modalData.selectedDistribution === \"assign\") {\n                // \"分配\"按钮：检查选中学生的可分配能量是否足够\n                const insufficientStudents = modalData.selectedStudents.filter((studentId)=>{\n                    const studentAvailablePoints = studentPointsMap.get(studentId);\n                    return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;\n                });\n                if (insufficientStudents.length > 0) {\n                    const insufficientNames = insufficientStudents.map((studentId)=>{\n                        const student = students.find((s)=>s.userId === studentId);\n                        const availablePoints = studentPointsMap.get(studentId) || 0;\n                        return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(可分配: \").concat(availablePoints, \")\");\n                    }).join(\"、\");\n                    notification.error(\"积分不足：以下学生的可分配能量不足 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                    return;\n                }\n            } else if (modalData.selectedDistribution === \"distribute\") {\n                // \"分配至\"按钮：检查需要补充能量的学生\n                const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                    const currentPoints = studentPointsMap.get(studentId) || 0;\n                    return currentPoints < energyAmountNum;\n                });\n                if (studentsNeedingEnergy.length > 0) {\n                    // 检查这些学生是否有足够的可分配能量来达到目标值\n                    const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = energyAmountNum - currentPoints;\n                        const studentAvailablePoints = studentPointsMap.get(studentId);\n                        return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                    });\n                    if (insufficientStudents.length > 0) {\n                        const insufficientNames = insufficientStudents.map((studentId)=>{\n                            const student = students.find((s)=>s.userId === studentId);\n                            const currentPoints = studentPointsMap.get(studentId) || 0;\n                            const neededPoints = energyAmountNum - currentPoints;\n                            const availablePoints = studentPointsMap.get(studentId) || 0;\n                            return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(需要: \").concat(neededPoints, \", 可分配: \").concat(availablePoints, \")\");\n                        }).join(\"、\");\n                        notification.error(\"积分不足：以下学生无法达到目标能量值 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                        return;\n                    }\n                }\n            }\n        }\n        // 根据操作类型决定下一步\n        if (actionType === \"发布任务\" || actionType === \"快速上课\") {\n            // 发布任务或快速上课：切换到发布任务步骤\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n        } else {\n            // 分配积木或分配能量：直接执行分配操作并关闭弹窗\n            console.log(\"\".concat(actionType, \"操作执行\"), {\n                selectedDistribution: modalData.selectedDistribution,\n                selectedTemplate: modalData.selectedTemplate,\n                selectedStudents: modalData.selectedStudents,\n                energyAmount: getCurrentEnergyAmount()\n            });\n            // 这里可以调用相应的API来执行分配操作\n            // TODO: 实现分配积木和分配能量的API调用\n            notification.success(\"\".concat(actionType, \"成功！\"));\n            clearAllData();\n            onClose();\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep === \"publish\") {\n            // 从发布任务步骤返回时重置持续时间\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\"\n                }));\n            setCurrentStep(\"template\");\n        } else {\n            onBack();\n        }\n    };\n    // 确认发布（与NewPublishTaskModal保持一致）\n    const handleConfirm = ()=>{\n        if (!taskData.taskName.trim()) {\n            notification.error(\"请输入任务名称\");\n            return;\n        }\n        const times = (0,_utils__WEBPACK_IMPORTED_MODULE_11__.getTaskTimes)(taskData.duration);\n        const finalTaskData = {\n            ...taskData,\n            startTime: times.startTime,\n            endTime: times.endTime,\n            selectedWorkIds,\n            attachments,\n            modalData\n        };\n        // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑\n        if (onConfirm) {\n            onConfirm(finalTaskData);\n        } else {\n            // 保留原有的发布逻辑作为后备\n            handlePublishTaskOk();\n        }\n    };\n    const handlePublishTaskOk = async ()=>{\n        // 防止重复点击\n        if (isPublishing) {\n            return;\n        }\n        try {\n            var _modalData_selectedTemplate;\n            setIsPublishing(true);\n            // 验证必填字段\n            if (!taskData.taskName.trim()) {\n                notification.error(\"请输入任务名称\");\n                setIsPublishing(false);\n                return;\n            }\n            console.log(\"发布任务:\", taskData);\n            console.log(\"选中的作品ID:\", selectedWorkIds);\n            console.log(\"模态框数据:\", modalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const user = userData ? JSON.parse(userData) : null;\n            const teacherId = user === null || user === void 0 ? void 0 : user.userId;\n            if (!teacherId) {\n                notification.error(\"未找到用户信息\");\n                setIsPublishing(false);\n                return;\n            }\n            // 处理时间\n            const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();\n            const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n            // 构建任务发布参数\n            const taskParams = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskType: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.TaskType.GRAPHIC,\n                priority: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.Priority.NORMAL,\n                startDate: startDate,\n                endDate: endDate,\n                taskContent: taskData.taskDescription || \"\",\n                attachments: attachments.map((file)=>file.name) || [],\n                isPublic: 1,\n                allowLateSubmission: false,\n                studentIds: modalData.selectedStudents,\n                classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                templateId: (_modalData_selectedTemplate = modalData.selectedTemplate) === null || _modalData_selectedTemplate === void 0 ? void 0 : _modalData_selectedTemplate.id,\n                workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim() !== \"\") || [] // 过滤空的自评项\n            };\n            console.log(\"任务发布参数:\", taskParams);\n            console.log(\"作品ID数组:\", taskParams.workIds);\n            // 准备并行请求数组\n            const requests = [];\n            // 1. 任务发布请求（必须执行）\n            requests.push(_lib_api_task__WEBPACK_IMPORTED_MODULE_9__[\"default\"].publishTask(taskParams));\n            // 2. 能量分配请求（如果需要）\n            let energyRequest = null;\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            if ((modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && currentEnergyAmount) {\n                const targetAmount = Number(currentEnergyAmount);\n                const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n                if (modalData.selectedDistribution === \"assign\") {\n                    // \"分配\"按钮：给每个学生分配固定数量的能量\n                    const studentExpiries = {};\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        studentExpiries[studentId] = defaultExpireTime;\n                    });\n                    energyRequest = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                        availablePoints: targetAmount,\n                        studentExpiries,\n                        remark: \"任务发布 - \".concat(taskData.taskName)\n                    });\n                    requests.push(energyRequest);\n                } else if (modalData.selectedDistribution === \"distribute\") {\n                    // \"分配至\"按钮：将学生能量补充到目标值\n                    const energyRequests = [];\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = targetAmount - currentPoints;\n                        // 只有当学生当前能量小于目标值时才分配\n                        if (neededPoints > 0) {\n                            const studentExpiries = {};\n                            studentExpiries[studentId] = defaultExpireTime;\n                            const request = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                                availablePoints: neededPoints,\n                                studentExpiries,\n                                remark: \"任务发布 - \".concat(taskData.taskName, \" (补充至\").concat(targetAmount, \")\")\n                            });\n                            energyRequests.push(request);\n                        }\n                    });\n                    // 将所有能量分配请求添加到主请求列表\n                    requests.push(...energyRequests);\n                }\n            }\n            // 3. 模板分配请求（如果需要）\n            let templateRequest = null;\n            if (modalData.selectedTemplate) {\n                const users = modalData.selectedStudents.map((studentId)=>({\n                        userId: studentId,\n                        roleId: 1,\n                        templateId: modalData.selectedTemplate.id,\n                        originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id\n                    }));\n                templateRequest = (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_10__.batchAddUserJoinRole)({\n                    users\n                });\n                requests.push(templateRequest);\n            }\n            // 并行执行所有请求\n            const results = await Promise.allSettled(requests);\n            // 处理任务发布结果\n            const taskResult = results[0];\n            if (taskResult.status === \"fulfilled\" && taskResult.value.data.code === 200) {\n                // 显示成功发布任务的提示\n                notification.success(\"\\uD83C\\uDF89 任务发布成功！学生可以开始学习了\");\n            } else {\n                const errorMsg = taskResult.status === \"fulfilled\" ? taskResult.value.data.message || \"任务发布失败\" : \"任务发布失败\";\n                notification.error(errorMsg);\n                setIsPublishing(false);\n                return; // 任务发布失败则直接返回\n            }\n            // 处理能量分配结果\n            let resultIndex = 1;\n            if (energyRequest) {\n                const energyResult = results[resultIndex];\n                if (energyResult.status === \"fulfilled\" && energyResult.value.data.code === 200) {\n                    notification.success(\"能量分配完成！\");\n                } else {\n                    console.error(\"能量分配失败:\", energyResult);\n                    notification.warning(\"能量分配失败\");\n                }\n                resultIndex++;\n            }\n            // 处理模板分配结果\n            if (templateRequest) {\n                const templateResult = results[resultIndex];\n                if (templateResult.status === \"fulfilled\" && templateResult.value.data.code === 200) {\n                    notification.success(\"模板分配完成！\");\n                } else {\n                    console.error(\"模板分配失败:\", templateResult);\n                    notification.warning(\"模板分配失败\");\n                }\n            }\n            // 延迟关闭弹窗，让用户能看到成功提示\n            setTimeout(()=>{\n                setIsPublishing(false);\n                clearAllData();\n                onClose();\n            }, 800);\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n            setIsPublishing(false);\n        }\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted || !isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        onWheel: (e)=>{\n            // 只阻止事件传播，依赖CSS控制滚动行为\n            e.stopPropagation();\n        },\n        onTouchMove: (e)=>{\n            // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: ()=>{\n                            clearAllData();\n                            onClose();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                            lineNumber: 832,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 828,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content template-selection-modal\",\n                        \"data-step\": currentStep,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step completed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"选择班级\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"template\" ? \"active\" : \"completed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: actionType === \"分配积木\" ? \"分配积木\" : actionType === \"分配能量\" ? \"分配能量\" : actionType === \"发布任务\" ? \"发布任务\" : \"能量和积木\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    actionType === \"快速上课\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content-body\",\n                                children: [\n                                    currentStep === \"template\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-title\",\n                                                    children: \"为学生分配能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: [\n                                                    !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"distribution-options\",\n                                                        children: distributionOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"distribution-card \".concat(modalData.selectedDistribution === option.id ? \"selected\" : \"\"),\n                                                                onClick: ()=>handleDistributionSelect(option.id),\n                                                                onMouseEnter: ()=>setHoveredOption(option.id),\n                                                                onMouseLeave: ()=>setHoveredOption(\"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"distribution-label\",\n                                                                        children: (()=>{\n                                                                            const currentAmount = option.id === \"assign\" ? modalData.assignEnergyAmount : option.id === \"distribute\" ? modalData.distributeEnergyAmount : \"\";\n                                                                            return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id ? \"\".concat(option.label, \" \").concat(currentAmount, \"能量\") : option.label;\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 882,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-container\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ref: option.id === \"assign\" ? assignInputRef : distributeInputRef,\n                                                                                type: \"number\",\n                                                                                className: \"energy-input\",\n                                                                                placeholder: option.id === \"assign\" ? \"输入能量\" : \"输入目标值\",\n                                                                                value: option.id === \"assign\" ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount,\n                                                                                min: \"1\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    const updateKey = option.id === \"assign\" ? \"assignEnergyAmount\" : \"distributeEnergyAmount\";\n                                                                                    const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                    // 清除之前的错误\n                                                                                    setInputErrors((prev)=>({\n                                                                                            ...prev,\n                                                                                            [errorKey]: \"\"\n                                                                                        }));\n                                                                                    // 允许空值或正整数\n                                                                                    if (value === \"\") {\n                                                                                        setModalData((prev)=>({\n                                                                                                ...prev,\n                                                                                                [updateKey]: value\n                                                                                            }));\n                                                                                    } else {\n                                                                                        const numValue = Number(value);\n                                                                                        if (Number.isInteger(numValue)) {\n                                                                                            if (numValue < 1) {\n                                                                                                // 设置错误提示\n                                                                                                setInputErrors((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [errorKey]: \"输入能量不能低于1\"\n                                                                                                    }));\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                            } else {\n                                                                                                // 有效输入\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                                // 输入数字时自动选中当前悬停的分配按钮\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        selectedDistribution: option.id\n                                                                                                    }));\n                                                                                            }\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    // 点击输入框时自动选中当前悬停的分配按钮\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onFocus: ()=>{\n                                                                                    setFocusedInput(option.id);\n                                                                                    // 聚焦时也自动选中分配选项\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    setFocusedInput(\"\");\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 893,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            (()=>{\n                                                                                const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                const errorMessage = inputErrors[errorKey];\n                                                                                return errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: \"#ef4444\",\n                                                                                        fontSize: \"12px\",\n                                                                                        marginTop: \"4px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: errorMessage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 29\n                                                                                }, undefined);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 892,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, option.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 19\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && modalData.selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-available-energy\",\n                                                        children: loadingStudentPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#6b7280\",\n                                                                fontStyle: \"italic\"\n                                                            },\n                                                            children: \"⏳ 正在获取能量信息...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 967,\n                                                            columnNumber: 21\n                                                        }, undefined) : (()=>{\n                                                            const displayInfo = getEnergyDisplayInfo();\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#64748b\",\n                                                                            fontSize: \"12px\"\n                                                                        },\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            displayInfo.label,\n                                                                            \": \",\n                                                                            displayInfo.value\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 965,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && getCurrentEnergyAmount() && !loadingStudentPoints && (()=>{\n                                                        const energyAmountNum = Number(getCurrentEnergyAmount());\n                                                        // 根据分配方式进行不同的验证\n                                                        let shouldShowError = false;\n                                                        let errorMessage = \"\";\n                                                        if (modalData.selectedDistribution === \"assign\") {\n                                                            const minAvailable = getMinAvailablePoints();\n                                                            if (energyAmountNum > minAvailable) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"可分配积分不足\";\n                                                            }\n                                                        } else if (modalData.selectedDistribution === \"distribute\") {\n                                                            // 对于\"分配至\"，检查是否有学生无法达到目标值\n                                                            const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                return currentPoints < energyAmountNum;\n                                                            });\n                                                            const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                const neededPoints = energyAmountNum - currentPoints;\n                                                                const studentAvailablePoints = studentPointsMap.get(studentId);\n                                                                return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                                                            });\n                                                            if (insufficientStudents.length > 0) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"部分学生积分不足以达到目标值\";\n                                                            }\n                                                        }\n                                                        if (shouldShowError) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: \"#fef2f2\",\n                                                                    border: \"1px solid #fecaca\",\n                                                                    borderRadius: \"8px\",\n                                                                    padding: \"12px 16px\",\n                                                                    marginTop: \"8px\",\n                                                                    marginBottom: \"12px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"space-between\",\n                                                                    gap: \"12px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: \"8px\",\n                                                                            flex: 1\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: \"500\"\n                                                                            },\n                                                                            children: [\n                                                                                \"⚠️ \",\n                                                                                errorMessage\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1033,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        style: {\n                                                                            background: \"#f97316\",\n                                                                            color: \"white\",\n                                                                            border: \"none\",\n                                                                            borderRadius: \"6px\",\n                                                                            padding: \"6px 12px\",\n                                                                            fontSize: \"12px\",\n                                                                            fontWeight: \"500\",\n                                                                            cursor: \"pointer\",\n                                                                            transition: \"all 0.2s ease\",\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.background = \"#ea580c\";\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.background = \"#f97316\";\n                                                                        },\n                                                                        onClick: ()=>{\n                                                                            handleBatchUseKeyPackageModalOpen();\n                                                                        },\n                                                                        children: \"兑换密钥\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1047,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1021,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        }\n                                                        return null;\n                                                    })(),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"section-title\",\n                                                        children: \"为学生分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-selection-area\",\n                                                        children: modalData.selectedTemplate ? // 已选择模板时显示模板信息\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-selected\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-icon\",\n                                                                    children: \"⭐\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1090,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-name\",\n                                                                            children: modalData.selectedTemplate.templateName || modalData.selectedTemplate.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1094,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-label\",\n                                                                            children: \"已选择模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1097,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1093,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"change-template-btn\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: \"更换\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"cancel-template-btn\",\n                                                                    onClick: handleCancelTemplate,\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 21\n                                                        }, undefined) : // 未选择模板时显示选择选项\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-options\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-none selected\",\n                                                                    onClick: ()=>{},\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1119,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"不分配模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1121,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"保持原有设置\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1122,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1120,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-select\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"\\uD83E\\uDDE9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"选择模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1131,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"为用户提供积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1132,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1125,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1114,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : // 发布任务步骤的内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"publish-task-content\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"tab-switcher\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"task\"),\n                                                        children: \"任务信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1146,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"resources\"),\n                                                        children: \"资源与附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"task-info-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"任务名称\",\n                                                                value: taskData.taskName,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskName: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1163,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                className: \"form-textarea\",\n                                                                placeholder: \"任务描述\",\n                                                                value: taskData.taskDescription,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskDescription: e.target.value\n                                                                        })),\n                                                                rows: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1174,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"self-assessment-section\",\n                                                                children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"add-self-assessment-btn\",\n                                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                                ...prev,\n                                                                                selfAssessmentItems: [\n                                                                                    \"\"\n                                                                                ]\n                                                                            })),\n                                                                    children: \"添加自评项\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1186,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label\",\n                                                                            children: \"自评项\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1195,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"self-assessment-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        className: \"form-input\",\n                                                                                        placeholder: \"自评项 \".concat(index + 1),\n                                                                                        value: item,\n                                                                                        onChange: (e)=>{\n                                                                                            const newItems = [\n                                                                                                ...taskData.selfAssessmentItems\n                                                                                            ];\n                                                                                            newItems[index] = e.target.value;\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1198,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"remove-btn\",\n                                                                                        onClick: ()=>{\n                                                                                            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        },\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1197,\n                                                                                columnNumber: 31\n                                                                            }, undefined)),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"add-btn\",\n                                                                            onClick: ()=>setTaskData((prev)=>({\n                                                                                        ...prev,\n                                                                                        selfAssessmentItems: [\n                                                                                            ...prev.selfAssessmentItems,\n                                                                                            \"\"\n                                                                                        ]\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"14px\"\n                                                                                    },\n                                                                                    children: \"➕\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1226,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"添加自评项\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1221,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1184,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1183,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"time-settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"form-label\",\n                                                                        children: \"任务持续时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"duration-options\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1小时\"),\n                                                                                children: \"1小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1239,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"3小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"3小时\"),\n                                                                                children: \"3小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1245,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1天\"),\n                                                                                children: \"1天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1251,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"7天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"7天\"),\n                                                                                children: \"7天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1257,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1个月\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1个月\"),\n                                                                                children: \"1个月\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1263,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1238,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"duration-hint\",\n                                                                        children: \"任务将从创建时开始，持续所选时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1270,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1236,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"resources-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"works-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"选择作品\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1277,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"help-text\",\n                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1278,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative works-scroll-wrapper\",\n                                                                    onWheel: _utils__WEBPACK_IMPORTED_MODULE_11__.handleWheelScroll,\n                                                                    onMouseDown: handleMouseDownWrapper,\n                                                                    onMouseMove: handleMouseMoveWrapper,\n                                                                    onMouseUp: handleMouseUpWrapper,\n                                                                    onMouseLeave: handleMouseLeaveWrapper,\n                                                                    style: {\n                                                                        minHeight: \"200px\",\n                                                                        cursor: \"grab\",\n                                                                        userSelect: \"none\"\n                                                                    },\n                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"loading-container\",\n                                                                        style: {\n                                                                            minHeight: \"200px\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"loading-spinner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1297,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"加载中...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1298,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1293,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"works-horizontal-scroll\",\n                                                                        children: [\n                                                                            works.sort((a, b)=>{\n                                                                                const aSelected = selectedWorkIds.includes(a.id);\n                                                                                const bSelected = selectedWorkIds.includes(b.id);\n                                                                                // 已选中的排在前面\n                                                                                if (aSelected && !bSelected) return -1;\n                                                                                if (!aSelected && bSelected) return 1;\n                                                                                return 0;\n                                                                            }).map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                    onClick: ()=>handleSelectWorkData(work.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-preview\",\n                                                                                            children: [\n                                                                                                work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fixImageUrl)(work.coverImage || work.screenShotImage),\n                                                                                                    alt: work.title,\n                                                                                                    className: \"work-image\",\n                                                                                                    onError: (e)=>{\n                                                                                                        e.currentTarget.style.display = \"none\";\n                                                                                                        const nextElement = e.currentTarget.nextElementSibling;\n                                                                                                        if (nextElement) {\n                                                                                                            nextElement.style.display = \"flex\";\n                                                                                                        }\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1321,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined) : null,\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"work-placeholder\",\n                                                                                                    style: {\n                                                                                                        display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"24\",\n                                                                                                        height: \"24\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1336,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z\",\n                                                                                                                fill: \"currentColor\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1337,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M21 15L16 10L11 15H21Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\",\n                                                                                                                strokeLinejoin: \"round\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1338,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1335,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1334,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"selection-indicator \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"12\",\n                                                                                                        height: \"12\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M9 12L11 14L15 10\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                            lineNumber: 1344,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1343,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1342,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1319,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-info\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"work-title\",\n                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1350,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1349,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, work.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1313,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)),\n                                                                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"load-more-container\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"load-more-btn\",\n                                                                                    onClick: loadMoreWorksData,\n                                                                                    disabled: loadingMore,\n                                                                                    children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"loading-spinner-small\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1364,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载中...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1365,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载更多\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1369,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                width: \"16\",\n                                                                                                height: \"16\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                fill: \"none\",\n                                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M12 5V19M5 12L12 19L19 12\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    strokeWidth: \"2\",\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1371,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1370,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1357,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1356,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-text\",\n                                                                            children: \"作品列表\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1381,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1380,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1279,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"attachments-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"附件上传\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1388,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            multiple: true,\n                                                                            accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                                            onChange: (e)=>{\n                                                                                if (e.target.files) {\n                                                                                    const files = Array.from(e.target.files);\n                                                                                    const validFiles = [];\n                                                                                    const invalidFiles = [];\n                                                                                    // 支持的文件格式\n                                                                                    const allowedTypes = [\n                                                                                        \"image/jpeg\",\n                                                                                        \"image/jpg\",\n                                                                                        \"image/png\",\n                                                                                        \"image/gif\",\n                                                                                        \"application/pdf\",\n                                                                                        \"application/msword\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                                                                                        \"application/vnd.ms-excel\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                                                                                        \"application/vnd.ms-powerpoint\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                                                                                        \"text/plain\"\n                                                                                    ];\n                                                                                    // 文件扩展名检查（作为备用验证）\n                                                                                    const allowedExtensions = [\n                                                                                        \".jpg\",\n                                                                                        \".jpeg\",\n                                                                                        \".png\",\n                                                                                        \".gif\",\n                                                                                        \".pdf\",\n                                                                                        \".doc\",\n                                                                                        \".docx\",\n                                                                                        \".xls\",\n                                                                                        \".xlsx\",\n                                                                                        \".ppt\",\n                                                                                        \".pptx\",\n                                                                                        \".txt\"\n                                                                                    ];\n                                                                                    files.forEach((file)=>{\n                                                                                        var _file_name_split_pop;\n                                                                                        // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）\n                                                                                        if (file.size > 10 * 1024 * 1024) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：文件大小超过10MB\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        // 检查文件类型\n                                                                                        const fileExtension = \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n                                                                                        const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);\n                                                                                        if (!isValidType) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：不支持的文件格式\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        validFiles.push(file);\n                                                                                    });\n                                                                                    // 添加有效文件\n                                                                                    if (validFiles.length > 0) {\n                                                                                        setAttachments((prev)=>[\n                                                                                                ...prev,\n                                                                                                ...validFiles\n                                                                                            ]);\n                                                                                    }\n                                                                                    // 显示错误信息\n                                                                                    if (invalidFiles.length > 0) {\n                                                                                        alert(\"以下文件无法上传：\\n\".concat(invalidFiles.join(\"\\n\")));\n                                                                                    }\n                                                                                    // 重置input的value，确保可以重复选择相同文件\n                                                                                    e.target.value = \"\";\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                display: \"none\"\n                                                                            },\n                                                                            id: \"file-upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1390,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"file-upload\",\n                                                                            className: \"upload-btn\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1449,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"file-format-info\",\n                                                                            children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1452,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1389,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachments-list\",\n                                                                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"file-name\",\n                                                                                    children: file.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1460,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setAttachments((prev)=>prev.filter((_, i)=>i !== index)),\n                                                                                    className: \"remove-attachment-btn\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1461,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1459,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1457,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1387,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"prev-btn\",\n                                                onClick: handlePrevious,\n                                                children: \"上一步\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1480,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"next-btn \".concat(currentStep === \"template\" && (isEnergyOnlyMode ? modalData.selectedDistribution : isBlocksOnlyMode ? modalData.selectedTemplate : modalData.selectedTemplate || modalData.selectedDistribution) || currentStep === \"publish\" && taskData.taskName.trim() && !isPublishing ? \"enabled\" : \"disabled\", \" \").concat(isPublishing ? \"publishing\" : \"\"),\n                                                onClick: currentStep === \"template\" ? handleNext : handleConfirm,\n                                                disabled: currentStep === \"template\" ? isEnergyOnlyMode ? !modalData.selectedDistribution : isBlocksOnlyMode ? !modalData.selectedTemplate : !modalData.selectedTemplate && !modalData.selectedDistribution : !taskData.taskName.trim() || isPublishing,\n                                                children: currentStep === \"template\" ? actionType === \"发布任务\" || actionType === \"快速上课\" ? \"下一步\" : \"分配\" : isPublishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"publishing-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1511,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"发布中...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1510,\n                                                    columnNumber: 19\n                                                }, undefined) : \"开始上课\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1479,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 827,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: handleTemplatePickerClose,\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1523,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalOpen,\n                selectedStudentIds: modalData.selectedStudents,\n                students: students,\n                onClose: handleBatchUseKeyPackageModalClose,\n                onSuccess: handleBatchUseKeyPackageSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1530,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n        lineNumber: 816,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateSelectionModal, \"3fkWfUC8OKkJ1JgoGu0y/qNfEA0=\");\n_c = TemplateSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"TemplateSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\n"));

/***/ })

});