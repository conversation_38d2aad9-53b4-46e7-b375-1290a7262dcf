"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ctrl";
exports.ids = ["vendor-chunks/@ctrl"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js":
/*!****************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/conversion.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertDecimalToHex: () => (/* binding */ convertDecimalToHex),\n/* harmony export */   convertHexToDecimal: () => (/* binding */ convertHexToDecimal),\n/* harmony export */   hslToRgb: () => (/* binding */ hslToRgb),\n/* harmony export */   hsvToRgb: () => (/* binding */ hsvToRgb),\n/* harmony export */   numberInputToObject: () => (/* binding */ numberInputToObject),\n/* harmony export */   parseIntFromHex: () => (/* binding */ parseIntFromHex),\n/* harmony export */   rgbToHex: () => (/* binding */ rgbToHex),\n/* harmony export */   rgbToHsl: () => (/* binding */ rgbToHsl),\n/* harmony export */   rgbToHsv: () => (/* binding */ rgbToHsv),\n/* harmony export */   rgbToRgb: () => (/* binding */ rgbToRgb),\n/* harmony export */   rgbaToArgbHex: () => (/* binding */ rgbaToArgbHex),\n/* harmony export */   rgbaToHex: () => (/* binding */ rgbaToHex)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nfunction rgbToRgb(r, g, b) {\n    return {\n        r: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255) * 255,\n        g: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255) * 255,\n        b: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nfunction rgbToHsl(r, g, b) {\n    r = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255);\n    g = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255);\n    b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(h, 360);\n    s = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(s, 100);\n    l = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nfunction rgbToHsv(r, g, b) {\n    r = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255);\n    g = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255);\n    b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nfunction hsvToRgb(h, s, v) {\n    h = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(h, 360) * 6;\n    s = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(s, 100);\n    v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nfunction rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nfunction rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(convertDecimalToHex(a)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/** Converts a decimal to a hex value */\nfunction convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nfunction convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nfunction parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nfunction numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   names: () => (/* binding */ names)\n/* harmony export */ });\n// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nvar names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/format-input.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inputToRGB: () => (/* binding */ inputToRGB),\n/* harmony export */   isValidCSSUnit: () => (/* binding */ isValidCSSUnit),\n/* harmony export */   stringInputToObject: () => (/* binding */ stringInputToObject)\n/* harmony export */ });\n/* harmony import */ var _conversion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conversion.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\");\n/* harmony import */ var _css_color_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./css-color-names.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n\n\n\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nfunction inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToRgb)(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.s);\n            v = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.v);\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.hsvToRgb)(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.s);\n            l = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.l);\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.hslToRgb)(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.boundAlpha)(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nfunction stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (_css_color_names_js__WEBPACK_IMPORTED_MODULE_2__.names[color]) {\n        color = _css_color_names_js__WEBPACK_IMPORTED_MODULE_2__.names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3]),\n            a: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.convertHexToDecimal)(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1] + match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2] + match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3] + match[3]),\n            a: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.convertHexToDecimal)(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1] + match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2] + match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nfunction isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TinyColor: () => (/* binding */ TinyColor),\n/* harmony export */   tinycolor: () => (/* binding */ tinycolor)\n/* harmony export */ });\n/* harmony import */ var _conversion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conversion.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\");\n/* harmony import */ var _css_color_names_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./css-color-names.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\");\n/* harmony import */ var _format_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./format-input */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n\n\n\n\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.numberInputToObject)(color);\n        }\n        this.originalInput = color;\n        var rgb = (0,_format_input__WEBPACK_IMPORTED_MODULE_1__.inputToRGB)(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.boundAlpha)(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsv)(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsv)(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsl)(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsl)(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHex)(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbaToHex)(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.bound01)(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.bound01)(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHex)(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(_css_color_names_js__WEBPACK_IMPORTED_MODULE_3__.names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\n\n// kept for backwards compatability with v1\nfunction tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGN0cmwvdGlueWNvbG9yL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErRjtBQUNsRDtBQUNEO0FBQ2E7QUFDekQ7QUFDQTtBQUNBLGdDQUFnQztBQUNoQywrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsbUVBQW1CO0FBQ3ZDO0FBQ0E7QUFDQSxrQkFBa0IseURBQVU7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLG9EQUFVO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isd0RBQVE7QUFDMUIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBUTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isd0RBQVE7QUFDMUIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBUTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQyxlQUFlLHdEQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQyxlQUFlLHlEQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsNEJBQTRCLGlEQUFPO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsa0JBQWtCLGlEQUFPO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isd0RBQVE7QUFDaEMsNkNBQTZDLHNEQUFLLEdBQUcsZ0JBQWdCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxnQkFBZ0IsaURBQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQSxnQkFBZ0IsaURBQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLGdCQUFnQixpREFBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsZ0JBQWdCLGlEQUFPO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEMsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSxVQUFVO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsa0JBQWtCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qix1Q0FBdUM7QUFDbkUsNEJBQTRCLHdDQUF3QztBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0Isd0NBQXdDLGtEQUFrRDtBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDb0I7QUFDckI7QUFDTztBQUNQLDRCQUE0QjtBQUM1QiwyQkFBMkI7QUFDM0I7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy9AY3RybC90aW55Y29sb3IvZGlzdC9tb2R1bGUvaW5kZXguanM/YjNkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBudW1iZXJJbnB1dFRvT2JqZWN0LCByZ2JhVG9IZXgsIHJnYlRvSGV4LCByZ2JUb0hzbCwgcmdiVG9Ic3YgfSBmcm9tICcuL2NvbnZlcnNpb24uanMnO1xuaW1wb3J0IHsgbmFtZXMgfSBmcm9tICcuL2Nzcy1jb2xvci1uYW1lcy5qcyc7XG5pbXBvcnQgeyBpbnB1dFRvUkdCIH0gZnJvbSAnLi9mb3JtYXQtaW5wdXQnO1xuaW1wb3J0IHsgYm91bmQwMSwgYm91bmRBbHBoYSwgY2xhbXAwMSB9IGZyb20gJy4vdXRpbC5qcyc7XG52YXIgVGlueUNvbG9yID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIFRpbnlDb2xvcihjb2xvciwgb3B0cykge1xuICAgICAgICBpZiAoY29sb3IgPT09IHZvaWQgMCkgeyBjb2xvciA9ICcnOyB9XG4gICAgICAgIGlmIChvcHRzID09PSB2b2lkIDApIHsgb3B0cyA9IHt9OyB9XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgLy8gSWYgaW5wdXQgaXMgYWxyZWFkeSBhIHRpbnljb2xvciwgcmV0dXJuIGl0c2VsZlxuICAgICAgICBpZiAoY29sb3IgaW5zdGFuY2VvZiBUaW55Q29sb3IpIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zdHJ1Y3Rvci1yZXR1cm5cbiAgICAgICAgICAgIHJldHVybiBjb2xvcjtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIGNvbG9yID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgY29sb3IgPSBudW1iZXJJbnB1dFRvT2JqZWN0KGNvbG9yKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLm9yaWdpbmFsSW5wdXQgPSBjb2xvcjtcbiAgICAgICAgdmFyIHJnYiA9IGlucHV0VG9SR0IoY29sb3IpO1xuICAgICAgICB0aGlzLm9yaWdpbmFsSW5wdXQgPSBjb2xvcjtcbiAgICAgICAgdGhpcy5yID0gcmdiLnI7XG4gICAgICAgIHRoaXMuZyA9IHJnYi5nO1xuICAgICAgICB0aGlzLmIgPSByZ2IuYjtcbiAgICAgICAgdGhpcy5hID0gcmdiLmE7XG4gICAgICAgIHRoaXMucm91bmRBID0gTWF0aC5yb3VuZCgxMDAgKiB0aGlzLmEpIC8gMTAwO1xuICAgICAgICB0aGlzLmZvcm1hdCA9IChfYSA9IG9wdHMuZm9ybWF0KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiByZ2IuZm9ybWF0O1xuICAgICAgICB0aGlzLmdyYWRpZW50VHlwZSA9IG9wdHMuZ3JhZGllbnRUeXBlO1xuICAgICAgICAvLyBEb24ndCBsZXQgdGhlIHJhbmdlIG9mIFswLDI1NV0gY29tZSBiYWNrIGluIFswLDFdLlxuICAgICAgICAvLyBQb3RlbnRpYWxseSBsb3NlIGEgbGl0dGxlIGJpdCBvZiBwcmVjaXNpb24gaGVyZSwgYnV0IHdpbGwgZml4IGlzc3VlcyB3aGVyZVxuICAgICAgICAvLyAuNSBnZXRzIGludGVycHJldGVkIGFzIGhhbGYgb2YgdGhlIHRvdGFsLCBpbnN0ZWFkIG9mIGhhbGYgb2YgMVxuICAgICAgICAvLyBJZiBpdCB3YXMgc3VwcG9zZWQgdG8gYmUgMTI4LCB0aGlzIHdhcyBhbHJlYWR5IHRha2VuIGNhcmUgb2YgYnkgYGlucHV0VG9SZ2JgXG4gICAgICAgIGlmICh0aGlzLnIgPCAxKSB7XG4gICAgICAgICAgICB0aGlzLnIgPSBNYXRoLnJvdW5kKHRoaXMucik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuZyA8IDEpIHtcbiAgICAgICAgICAgIHRoaXMuZyA9IE1hdGgucm91bmQodGhpcy5nKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5iIDwgMSkge1xuICAgICAgICAgICAgdGhpcy5iID0gTWF0aC5yb3VuZCh0aGlzLmIpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaXNWYWxpZCA9IHJnYi5vaztcbiAgICB9XG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5pc0RhcmsgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmdldEJyaWdodG5lc3MoKSA8IDEyODtcbiAgICB9O1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuaXNMaWdodCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuICF0aGlzLmlzRGFyaygpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgcGVyY2VpdmVkIGJyaWdodG5lc3Mgb2YgdGhlIGNvbG9yLCBmcm9tIDAtMjU1LlxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuZ2V0QnJpZ2h0bmVzcyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgLy8gaHR0cDovL3d3dy53My5vcmcvVFIvQUVSVCNjb2xvci1jb250cmFzdFxuICAgICAgICB2YXIgcmdiID0gdGhpcy50b1JnYigpO1xuICAgICAgICByZXR1cm4gKHJnYi5yICogMjk5ICsgcmdiLmcgKiA1ODcgKyByZ2IuYiAqIDExNCkgLyAxMDAwO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgcGVyY2VpdmVkIGx1bWluYW5jZSBvZiBhIGNvbG9yLCBmcm9tIDAtMS5cbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLmdldEx1bWluYW5jZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgLy8gaHR0cDovL3d3dy53My5vcmcvVFIvMjAwOC9SRUMtV0NBRzIwLTIwMDgxMjExLyNyZWxhdGl2ZWx1bWluYW5jZWRlZlxuICAgICAgICB2YXIgcmdiID0gdGhpcy50b1JnYigpO1xuICAgICAgICB2YXIgUjtcbiAgICAgICAgdmFyIEc7XG4gICAgICAgIHZhciBCO1xuICAgICAgICB2YXIgUnNSR0IgPSByZ2IuciAvIDI1NTtcbiAgICAgICAgdmFyIEdzUkdCID0gcmdiLmcgLyAyNTU7XG4gICAgICAgIHZhciBCc1JHQiA9IHJnYi5iIC8gMjU1O1xuICAgICAgICBpZiAoUnNSR0IgPD0gMC4wMzkyOCkge1xuICAgICAgICAgICAgUiA9IFJzUkdCIC8gMTIuOTI7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcHJlZmVyLWV4cG9uZW50aWF0aW9uLW9wZXJhdG9yXG4gICAgICAgICAgICBSID0gTWF0aC5wb3coKFJzUkdCICsgMC4wNTUpIC8gMS4wNTUsIDIuNCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKEdzUkdCIDw9IDAuMDM5MjgpIHtcbiAgICAgICAgICAgIEcgPSBHc1JHQiAvIDEyLjkyO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHByZWZlci1leHBvbmVudGlhdGlvbi1vcGVyYXRvclxuICAgICAgICAgICAgRyA9IE1hdGgucG93KChHc1JHQiArIDAuMDU1KSAvIDEuMDU1LCAyLjQpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChCc1JHQiA8PSAwLjAzOTI4KSB7XG4gICAgICAgICAgICBCID0gQnNSR0IgLyAxMi45MjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBwcmVmZXItZXhwb25lbnRpYXRpb24tb3BlcmF0b3JcbiAgICAgICAgICAgIEIgPSBNYXRoLnBvdygoQnNSR0IgKyAwLjA1NSkgLyAxLjA1NSwgMi40KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gMC4yMTI2ICogUiArIDAuNzE1MiAqIEcgKyAwLjA3MjIgKiBCO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgYWxwaGEgdmFsdWUgb2YgYSBjb2xvciwgZnJvbSAwLTEuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5nZXRBbHBoYSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFNldHMgdGhlIGFscGhhIHZhbHVlIG9uIHRoZSBjdXJyZW50IGNvbG9yLlxuICAgICAqXG4gICAgICogQHBhcmFtIGFscGhhIC0gVGhlIG5ldyBhbHBoYSB2YWx1ZS4gVGhlIGFjY2VwdGVkIHJhbmdlIGlzIDAtMS5cbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLnNldEFscGhhID0gZnVuY3Rpb24gKGFscGhhKSB7XG4gICAgICAgIHRoaXMuYSA9IGJvdW5kQWxwaGEoYWxwaGEpO1xuICAgICAgICB0aGlzLnJvdW5kQSA9IE1hdGgucm91bmQoMTAwICogdGhpcy5hKSAvIDEwMDtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIHdoZXRoZXIgdGhlIGNvbG9yIGlzIG1vbm9jaHJvbWUuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5pc01vbm9jaHJvbWUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBzID0gdGhpcy50b0hzbCgpLnM7XG4gICAgICAgIHJldHVybiBzID09PSAwO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgb2JqZWN0IGFzIGEgSFNWQSBvYmplY3QuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS50b0hzdiA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIGhzdiA9IHJnYlRvSHN2KHRoaXMuciwgdGhpcy5nLCB0aGlzLmIpO1xuICAgICAgICByZXR1cm4geyBoOiBoc3YuaCAqIDM2MCwgczogaHN2LnMsIHY6IGhzdi52LCBhOiB0aGlzLmEgfTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIGhzdmEgdmFsdWVzIGludGVycG9sYXRlZCBpbnRvIGEgc3RyaW5nIHdpdGggdGhlIGZvbGxvd2luZyBmb3JtYXQ6XG4gICAgICogXCJoc3ZhKHh4eCwgeHh4LCB4eHgsIHh4KVwiLlxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9Ic3ZTdHJpbmcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBoc3YgPSByZ2JUb0hzdih0aGlzLnIsIHRoaXMuZywgdGhpcy5iKTtcbiAgICAgICAgdmFyIGggPSBNYXRoLnJvdW5kKGhzdi5oICogMzYwKTtcbiAgICAgICAgdmFyIHMgPSBNYXRoLnJvdW5kKGhzdi5zICogMTAwKTtcbiAgICAgICAgdmFyIHYgPSBNYXRoLnJvdW5kKGhzdi52ICogMTAwKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuYSA9PT0gMSA/IFwiaHN2KFwiLmNvbmNhdChoLCBcIiwgXCIpLmNvbmNhdChzLCBcIiUsIFwiKS5jb25jYXQodiwgXCIlKVwiKSA6IFwiaHN2YShcIi5jb25jYXQoaCwgXCIsIFwiKS5jb25jYXQocywgXCIlLCBcIikuY29uY2F0KHYsIFwiJSwgXCIpLmNvbmNhdCh0aGlzLnJvdW5kQSwgXCIpXCIpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgb2JqZWN0IGFzIGEgSFNMQSBvYmplY3QuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS50b0hzbCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIGhzbCA9IHJnYlRvSHNsKHRoaXMuciwgdGhpcy5nLCB0aGlzLmIpO1xuICAgICAgICByZXR1cm4geyBoOiBoc2wuaCAqIDM2MCwgczogaHNsLnMsIGw6IGhzbC5sLCBhOiB0aGlzLmEgfTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIGhzbGEgdmFsdWVzIGludGVycG9sYXRlZCBpbnRvIGEgc3RyaW5nIHdpdGggdGhlIGZvbGxvd2luZyBmb3JtYXQ6XG4gICAgICogXCJoc2xhKHh4eCwgeHh4LCB4eHgsIHh4KVwiLlxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9Ic2xTdHJpbmcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciBoc2wgPSByZ2JUb0hzbCh0aGlzLnIsIHRoaXMuZywgdGhpcy5iKTtcbiAgICAgICAgdmFyIGggPSBNYXRoLnJvdW5kKGhzbC5oICogMzYwKTtcbiAgICAgICAgdmFyIHMgPSBNYXRoLnJvdW5kKGhzbC5zICogMTAwKTtcbiAgICAgICAgdmFyIGwgPSBNYXRoLnJvdW5kKGhzbC5sICogMTAwKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuYSA9PT0gMSA/IFwiaHNsKFwiLmNvbmNhdChoLCBcIiwgXCIpLmNvbmNhdChzLCBcIiUsIFwiKS5jb25jYXQobCwgXCIlKVwiKSA6IFwiaHNsYShcIi5jb25jYXQoaCwgXCIsIFwiKS5jb25jYXQocywgXCIlLCBcIikuY29uY2F0KGwsIFwiJSwgXCIpLmNvbmNhdCh0aGlzLnJvdW5kQSwgXCIpXCIpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgaGV4IHZhbHVlIG9mIHRoZSBjb2xvci5cbiAgICAgKiBAcGFyYW0gYWxsb3czQ2hhciB3aWxsIHNob3J0ZW4gaGV4IHZhbHVlIHRvIDMgY2hhciBpZiBwb3NzaWJsZVxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9IZXggPSBmdW5jdGlvbiAoYWxsb3czQ2hhcikge1xuICAgICAgICBpZiAoYWxsb3czQ2hhciA9PT0gdm9pZCAwKSB7IGFsbG93M0NoYXIgPSBmYWxzZTsgfVxuICAgICAgICByZXR1cm4gcmdiVG9IZXgodGhpcy5yLCB0aGlzLmcsIHRoaXMuYiwgYWxsb3czQ2hhcik7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIHRoZSBoZXggdmFsdWUgb2YgdGhlIGNvbG9yIC13aXRoIGEgIyBwcmVmaXhlZC5cbiAgICAgKiBAcGFyYW0gYWxsb3czQ2hhciB3aWxsIHNob3J0ZW4gaGV4IHZhbHVlIHRvIDMgY2hhciBpZiBwb3NzaWJsZVxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9IZXhTdHJpbmcgPSBmdW5jdGlvbiAoYWxsb3czQ2hhcikge1xuICAgICAgICBpZiAoYWxsb3czQ2hhciA9PT0gdm9pZCAwKSB7IGFsbG93M0NoYXIgPSBmYWxzZTsgfVxuICAgICAgICByZXR1cm4gJyMnICsgdGhpcy50b0hleChhbGxvdzNDaGFyKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIGhleCA4IHZhbHVlIG9mIHRoZSBjb2xvci5cbiAgICAgKiBAcGFyYW0gYWxsb3c0Q2hhciB3aWxsIHNob3J0ZW4gaGV4IHZhbHVlIHRvIDQgY2hhciBpZiBwb3NzaWJsZVxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9IZXg4ID0gZnVuY3Rpb24gKGFsbG93NENoYXIpIHtcbiAgICAgICAgaWYgKGFsbG93NENoYXIgPT09IHZvaWQgMCkgeyBhbGxvdzRDaGFyID0gZmFsc2U7IH1cbiAgICAgICAgcmV0dXJuIHJnYmFUb0hleCh0aGlzLnIsIHRoaXMuZywgdGhpcy5iLCB0aGlzLmEsIGFsbG93NENoYXIpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgaGV4IDggdmFsdWUgb2YgdGhlIGNvbG9yIC13aXRoIGEgIyBwcmVmaXhlZC5cbiAgICAgKiBAcGFyYW0gYWxsb3c0Q2hhciB3aWxsIHNob3J0ZW4gaGV4IHZhbHVlIHRvIDQgY2hhciBpZiBwb3NzaWJsZVxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9IZXg4U3RyaW5nID0gZnVuY3Rpb24gKGFsbG93NENoYXIpIHtcbiAgICAgICAgaWYgKGFsbG93NENoYXIgPT09IHZvaWQgMCkgeyBhbGxvdzRDaGFyID0gZmFsc2U7IH1cbiAgICAgICAgcmV0dXJuICcjJyArIHRoaXMudG9IZXg4KGFsbG93NENoYXIpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgc2hvcnRlciBoZXggdmFsdWUgb2YgdGhlIGNvbG9yIGRlcGVuZHMgb24gaXRzIGFscGhhIC13aXRoIGEgIyBwcmVmaXhlZC5cbiAgICAgKiBAcGFyYW0gYWxsb3dTaG9ydENoYXIgd2lsbCBzaG9ydGVuIGhleCB2YWx1ZSB0byAzIG9yIDQgY2hhciBpZiBwb3NzaWJsZVxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9IZXhTaG9ydFN0cmluZyA9IGZ1bmN0aW9uIChhbGxvd1Nob3J0Q2hhcikge1xuICAgICAgICBpZiAoYWxsb3dTaG9ydENoYXIgPT09IHZvaWQgMCkgeyBhbGxvd1Nob3J0Q2hhciA9IGZhbHNlOyB9XG4gICAgICAgIHJldHVybiB0aGlzLmEgPT09IDEgPyB0aGlzLnRvSGV4U3RyaW5nKGFsbG93U2hvcnRDaGFyKSA6IHRoaXMudG9IZXg4U3RyaW5nKGFsbG93U2hvcnRDaGFyKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIG9iamVjdCBhcyBhIFJHQkEgb2JqZWN0LlxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9SZ2IgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICByOiBNYXRoLnJvdW5kKHRoaXMuciksXG4gICAgICAgICAgICBnOiBNYXRoLnJvdW5kKHRoaXMuZyksXG4gICAgICAgICAgICBiOiBNYXRoLnJvdW5kKHRoaXMuYiksXG4gICAgICAgICAgICBhOiB0aGlzLmEsXG4gICAgICAgIH07XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIHRoZSBSR0JBIHZhbHVlcyBpbnRlcnBvbGF0ZWQgaW50byBhIHN0cmluZyB3aXRoIHRoZSBmb2xsb3dpbmcgZm9ybWF0OlxuICAgICAqIFwiUkdCQSh4eHgsIHh4eCwgeHh4LCB4eClcIi5cbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLnRvUmdiU3RyaW5nID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgciA9IE1hdGgucm91bmQodGhpcy5yKTtcbiAgICAgICAgdmFyIGcgPSBNYXRoLnJvdW5kKHRoaXMuZyk7XG4gICAgICAgIHZhciBiID0gTWF0aC5yb3VuZCh0aGlzLmIpO1xuICAgICAgICByZXR1cm4gdGhpcy5hID09PSAxID8gXCJyZ2IoXCIuY29uY2F0KHIsIFwiLCBcIikuY29uY2F0KGcsIFwiLCBcIikuY29uY2F0KGIsIFwiKVwiKSA6IFwicmdiYShcIi5jb25jYXQociwgXCIsIFwiKS5jb25jYXQoZywgXCIsIFwiKS5jb25jYXQoYiwgXCIsIFwiKS5jb25jYXQodGhpcy5yb3VuZEEsIFwiKVwiKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIG9iamVjdCBhcyBhIFJHQkEgb2JqZWN0LlxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9QZXJjZW50YWdlUmdiID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgZm10ID0gZnVuY3Rpb24gKHgpIHsgcmV0dXJuIFwiXCIuY29uY2F0KE1hdGgucm91bmQoYm91bmQwMSh4LCAyNTUpICogMTAwKSwgXCIlXCIpOyB9O1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcjogZm10KHRoaXMuciksXG4gICAgICAgICAgICBnOiBmbXQodGhpcy5nKSxcbiAgICAgICAgICAgIGI6IGZtdCh0aGlzLmIpLFxuICAgICAgICAgICAgYTogdGhpcy5hLFxuICAgICAgICB9O1xuICAgIH07XG4gICAgLyoqXG4gICAgICogUmV0dXJucyB0aGUgUkdCQSByZWxhdGl2ZSB2YWx1ZXMgaW50ZXJwb2xhdGVkIGludG8gYSBzdHJpbmdcbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLnRvUGVyY2VudGFnZVJnYlN0cmluZyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIHJuZCA9IGZ1bmN0aW9uICh4KSB7IHJldHVybiBNYXRoLnJvdW5kKGJvdW5kMDEoeCwgMjU1KSAqIDEwMCk7IH07XG4gICAgICAgIHJldHVybiB0aGlzLmEgPT09IDFcbiAgICAgICAgICAgID8gXCJyZ2IoXCIuY29uY2F0KHJuZCh0aGlzLnIpLCBcIiUsIFwiKS5jb25jYXQocm5kKHRoaXMuZyksIFwiJSwgXCIpLmNvbmNhdChybmQodGhpcy5iKSwgXCIlKVwiKVxuICAgICAgICAgICAgOiBcInJnYmEoXCIuY29uY2F0KHJuZCh0aGlzLnIpLCBcIiUsIFwiKS5jb25jYXQocm5kKHRoaXMuZyksIFwiJSwgXCIpLmNvbmNhdChybmQodGhpcy5iKSwgXCIlLCBcIikuY29uY2F0KHRoaXMucm91bmRBLCBcIilcIik7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBUaGUgJ3JlYWwnIG5hbWUgb2YgdGhlIGNvbG9yIC1pZiB0aGVyZSBpcyBvbmUuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS50b05hbWUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmICh0aGlzLmEgPT09IDApIHtcbiAgICAgICAgICAgIHJldHVybiAndHJhbnNwYXJlbnQnO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmEgPCAxKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGhleCA9ICcjJyArIHJnYlRvSGV4KHRoaXMuciwgdGhpcy5nLCB0aGlzLmIsIGZhbHNlKTtcbiAgICAgICAgZm9yICh2YXIgX2kgPSAwLCBfYSA9IE9iamVjdC5lbnRyaWVzKG5hbWVzKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgICAgIHZhciBfYiA9IF9hW19pXSwga2V5ID0gX2JbMF0sIHZhbHVlID0gX2JbMV07XG4gICAgICAgICAgICBpZiAoaGV4ID09PSB2YWx1ZSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBrZXk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH07XG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uIChmb3JtYXQpIHtcbiAgICAgICAgdmFyIGZvcm1hdFNldCA9IEJvb2xlYW4oZm9ybWF0KTtcbiAgICAgICAgZm9ybWF0ID0gZm9ybWF0ICE9PSBudWxsICYmIGZvcm1hdCAhPT0gdm9pZCAwID8gZm9ybWF0IDogdGhpcy5mb3JtYXQ7XG4gICAgICAgIHZhciBmb3JtYXR0ZWRTdHJpbmcgPSBmYWxzZTtcbiAgICAgICAgdmFyIGhhc0FscGhhID0gdGhpcy5hIDwgMSAmJiB0aGlzLmEgPj0gMDtcbiAgICAgICAgdmFyIG5lZWRzQWxwaGFGb3JtYXQgPSAhZm9ybWF0U2V0ICYmIGhhc0FscGhhICYmIChmb3JtYXQuc3RhcnRzV2l0aCgnaGV4JykgfHwgZm9ybWF0ID09PSAnbmFtZScpO1xuICAgICAgICBpZiAobmVlZHNBbHBoYUZvcm1hdCkge1xuICAgICAgICAgICAgLy8gU3BlY2lhbCBjYXNlIGZvciBcInRyYW5zcGFyZW50XCIsIGFsbCBvdGhlciBub24tYWxwaGEgZm9ybWF0c1xuICAgICAgICAgICAgLy8gd2lsbCByZXR1cm4gcmdiYSB3aGVuIHRoZXJlIGlzIHRyYW5zcGFyZW5jeS5cbiAgICAgICAgICAgIGlmIChmb3JtYXQgPT09ICduYW1lJyAmJiB0aGlzLmEgPT09IDApIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy50b05hbWUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0aGlzLnRvUmdiU3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gJ3JnYicpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9SZ2JTdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm9ybWF0ID09PSAncHJnYicpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9QZXJjZW50YWdlUmdiU3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gJ2hleCcgfHwgZm9ybWF0ID09PSAnaGV4NicpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9IZXhTdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm9ybWF0ID09PSAnaGV4MycpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9IZXhTdHJpbmcodHJ1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gJ2hleDQnKSB7XG4gICAgICAgICAgICBmb3JtYXR0ZWRTdHJpbmcgPSB0aGlzLnRvSGV4OFN0cmluZyh0cnVlKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm9ybWF0ID09PSAnaGV4OCcpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9IZXg4U3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gJ25hbWUnKSB7XG4gICAgICAgICAgICBmb3JtYXR0ZWRTdHJpbmcgPSB0aGlzLnRvTmFtZSgpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3JtYXQgPT09ICdoc2wnKSB7XG4gICAgICAgICAgICBmb3JtYXR0ZWRTdHJpbmcgPSB0aGlzLnRvSHNsU3RyaW5nKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gJ2hzdicpIHtcbiAgICAgICAgICAgIGZvcm1hdHRlZFN0cmluZyA9IHRoaXMudG9Ic3ZTdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZm9ybWF0dGVkU3RyaW5nIHx8IHRoaXMudG9IZXhTdHJpbmcoKTtcbiAgICB9O1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudG9OdW1iZXIgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiAoTWF0aC5yb3VuZCh0aGlzLnIpIDw8IDE2KSArIChNYXRoLnJvdW5kKHRoaXMuZykgPDwgOCkgKyBNYXRoLnJvdW5kKHRoaXMuYik7XG4gICAgfTtcbiAgICBUaW55Q29sb3IucHJvdG90eXBlLmNsb25lID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IFRpbnlDb2xvcih0aGlzLnRvU3RyaW5nKCkpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogTGlnaHRlbiB0aGUgY29sb3IgYSBnaXZlbiBhbW91bnQuIFByb3ZpZGluZyAxMDAgd2lsbCBhbHdheXMgcmV0dXJuIHdoaXRlLlxuICAgICAqIEBwYXJhbSBhbW91bnQgLSB2YWxpZCBiZXR3ZWVuIDEtMTAwXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5saWdodGVuID0gZnVuY3Rpb24gKGFtb3VudCkge1xuICAgICAgICBpZiAoYW1vdW50ID09PSB2b2lkIDApIHsgYW1vdW50ID0gMTA7IH1cbiAgICAgICAgdmFyIGhzbCA9IHRoaXMudG9Ic2woKTtcbiAgICAgICAgaHNsLmwgKz0gYW1vdW50IC8gMTAwO1xuICAgICAgICBoc2wubCA9IGNsYW1wMDEoaHNsLmwpO1xuICAgICAgICByZXR1cm4gbmV3IFRpbnlDb2xvcihoc2wpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQnJpZ2h0ZW4gdGhlIGNvbG9yIGEgZ2l2ZW4gYW1vdW50LCBmcm9tIDAgdG8gMTAwLlxuICAgICAqIEBwYXJhbSBhbW91bnQgLSB2YWxpZCBiZXR3ZWVuIDEtMTAwXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5icmlnaHRlbiA9IGZ1bmN0aW9uIChhbW91bnQpIHtcbiAgICAgICAgaWYgKGFtb3VudCA9PT0gdm9pZCAwKSB7IGFtb3VudCA9IDEwOyB9XG4gICAgICAgIHZhciByZ2IgPSB0aGlzLnRvUmdiKCk7XG4gICAgICAgIHJnYi5yID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCByZ2IuciAtIE1hdGgucm91bmQoMjU1ICogLShhbW91bnQgLyAxMDApKSkpO1xuICAgICAgICByZ2IuZyA9IE1hdGgubWF4KDAsIE1hdGgubWluKDI1NSwgcmdiLmcgLSBNYXRoLnJvdW5kKDI1NSAqIC0oYW1vdW50IC8gMTAwKSkpKTtcbiAgICAgICAgcmdiLmIgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigyNTUsIHJnYi5iIC0gTWF0aC5yb3VuZCgyNTUgKiAtKGFtb3VudCAvIDEwMCkpKSk7XG4gICAgICAgIHJldHVybiBuZXcgVGlueUNvbG9yKHJnYik7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBEYXJrZW4gdGhlIGNvbG9yIGEgZ2l2ZW4gYW1vdW50LCBmcm9tIDAgdG8gMTAwLlxuICAgICAqIFByb3ZpZGluZyAxMDAgd2lsbCBhbHdheXMgcmV0dXJuIGJsYWNrLlxuICAgICAqIEBwYXJhbSBhbW91bnQgLSB2YWxpZCBiZXR3ZWVuIDEtMTAwXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5kYXJrZW4gPSBmdW5jdGlvbiAoYW1vdW50KSB7XG4gICAgICAgIGlmIChhbW91bnQgPT09IHZvaWQgMCkgeyBhbW91bnQgPSAxMDsgfVxuICAgICAgICB2YXIgaHNsID0gdGhpcy50b0hzbCgpO1xuICAgICAgICBoc2wubCAtPSBhbW91bnQgLyAxMDA7XG4gICAgICAgIGhzbC5sID0gY2xhbXAwMShoc2wubCk7XG4gICAgICAgIHJldHVybiBuZXcgVGlueUNvbG9yKGhzbCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBNaXggdGhlIGNvbG9yIHdpdGggcHVyZSB3aGl0ZSwgZnJvbSAwIHRvIDEwMC5cbiAgICAgKiBQcm92aWRpbmcgMCB3aWxsIGRvIG5vdGhpbmcsIHByb3ZpZGluZyAxMDAgd2lsbCBhbHdheXMgcmV0dXJuIHdoaXRlLlxuICAgICAqIEBwYXJhbSBhbW91bnQgLSB2YWxpZCBiZXR3ZWVuIDEtMTAwXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS50aW50ID0gZnVuY3Rpb24gKGFtb3VudCkge1xuICAgICAgICBpZiAoYW1vdW50ID09PSB2b2lkIDApIHsgYW1vdW50ID0gMTA7IH1cbiAgICAgICAgcmV0dXJuIHRoaXMubWl4KCd3aGl0ZScsIGFtb3VudCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBNaXggdGhlIGNvbG9yIHdpdGggcHVyZSBibGFjaywgZnJvbSAwIHRvIDEwMC5cbiAgICAgKiBQcm92aWRpbmcgMCB3aWxsIGRvIG5vdGhpbmcsIHByb3ZpZGluZyAxMDAgd2lsbCBhbHdheXMgcmV0dXJuIGJsYWNrLlxuICAgICAqIEBwYXJhbSBhbW91bnQgLSB2YWxpZCBiZXR3ZWVuIDEtMTAwXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5zaGFkZSA9IGZ1bmN0aW9uIChhbW91bnQpIHtcbiAgICAgICAgaWYgKGFtb3VudCA9PT0gdm9pZCAwKSB7IGFtb3VudCA9IDEwOyB9XG4gICAgICAgIHJldHVybiB0aGlzLm1peCgnYmxhY2snLCBhbW91bnQpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogRGVzYXR1cmF0ZSB0aGUgY29sb3IgYSBnaXZlbiBhbW91bnQsIGZyb20gMCB0byAxMDAuXG4gICAgICogUHJvdmlkaW5nIDEwMCB3aWxsIGlzIHRoZSBzYW1lIGFzIGNhbGxpbmcgZ3JleXNjYWxlXG4gICAgICogQHBhcmFtIGFtb3VudCAtIHZhbGlkIGJldHdlZW4gMS0xMDBcbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLmRlc2F0dXJhdGUgPSBmdW5jdGlvbiAoYW1vdW50KSB7XG4gICAgICAgIGlmIChhbW91bnQgPT09IHZvaWQgMCkgeyBhbW91bnQgPSAxMDsgfVxuICAgICAgICB2YXIgaHNsID0gdGhpcy50b0hzbCgpO1xuICAgICAgICBoc2wucyAtPSBhbW91bnQgLyAxMDA7XG4gICAgICAgIGhzbC5zID0gY2xhbXAwMShoc2wucyk7XG4gICAgICAgIHJldHVybiBuZXcgVGlueUNvbG9yKGhzbCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBTYXR1cmF0ZSB0aGUgY29sb3IgYSBnaXZlbiBhbW91bnQsIGZyb20gMCB0byAxMDAuXG4gICAgICogQHBhcmFtIGFtb3VudCAtIHZhbGlkIGJldHdlZW4gMS0xMDBcbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLnNhdHVyYXRlID0gZnVuY3Rpb24gKGFtb3VudCkge1xuICAgICAgICBpZiAoYW1vdW50ID09PSB2b2lkIDApIHsgYW1vdW50ID0gMTA7IH1cbiAgICAgICAgdmFyIGhzbCA9IHRoaXMudG9Ic2woKTtcbiAgICAgICAgaHNsLnMgKz0gYW1vdW50IC8gMTAwO1xuICAgICAgICBoc2wucyA9IGNsYW1wMDEoaHNsLnMpO1xuICAgICAgICByZXR1cm4gbmV3IFRpbnlDb2xvcihoc2wpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQ29tcGxldGVseSBkZXNhdHVyYXRlcyBhIGNvbG9yIGludG8gZ3JleXNjYWxlLlxuICAgICAqIFNhbWUgYXMgY2FsbGluZyBgZGVzYXR1cmF0ZSgxMDApYFxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuZ3JleXNjYWxlID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kZXNhdHVyYXRlKDEwMCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBTcGluIHRha2VzIGEgcG9zaXRpdmUgb3IgbmVnYXRpdmUgYW1vdW50IHdpdGhpbiBbLTM2MCwgMzYwXSBpbmRpY2F0aW5nIHRoZSBjaGFuZ2Ugb2YgaHVlLlxuICAgICAqIFZhbHVlcyBvdXRzaWRlIG9mIHRoaXMgcmFuZ2Ugd2lsbCBiZSB3cmFwcGVkIGludG8gdGhpcyByYW5nZS5cbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLnNwaW4gPSBmdW5jdGlvbiAoYW1vdW50KSB7XG4gICAgICAgIHZhciBoc2wgPSB0aGlzLnRvSHNsKCk7XG4gICAgICAgIHZhciBodWUgPSAoaHNsLmggKyBhbW91bnQpICUgMzYwO1xuICAgICAgICBoc2wuaCA9IGh1ZSA8IDAgPyAzNjAgKyBodWUgOiBodWU7XG4gICAgICAgIHJldHVybiBuZXcgVGlueUNvbG9yKGhzbCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBNaXggdGhlIGN1cnJlbnQgY29sb3IgYSBnaXZlbiBhbW91bnQgd2l0aCBhbm90aGVyIGNvbG9yLCBmcm9tIDAgdG8gMTAwLlxuICAgICAqIDAgbWVhbnMgbm8gbWl4aW5nIChyZXR1cm4gY3VycmVudCBjb2xvcikuXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5taXggPSBmdW5jdGlvbiAoY29sb3IsIGFtb3VudCkge1xuICAgICAgICBpZiAoYW1vdW50ID09PSB2b2lkIDApIHsgYW1vdW50ID0gNTA7IH1cbiAgICAgICAgdmFyIHJnYjEgPSB0aGlzLnRvUmdiKCk7XG4gICAgICAgIHZhciByZ2IyID0gbmV3IFRpbnlDb2xvcihjb2xvcikudG9SZ2IoKTtcbiAgICAgICAgdmFyIHAgPSBhbW91bnQgLyAxMDA7XG4gICAgICAgIHZhciByZ2JhID0ge1xuICAgICAgICAgICAgcjogKHJnYjIuciAtIHJnYjEucikgKiBwICsgcmdiMS5yLFxuICAgICAgICAgICAgZzogKHJnYjIuZyAtIHJnYjEuZykgKiBwICsgcmdiMS5nLFxuICAgICAgICAgICAgYjogKHJnYjIuYiAtIHJnYjEuYikgKiBwICsgcmdiMS5iLFxuICAgICAgICAgICAgYTogKHJnYjIuYSAtIHJnYjEuYSkgKiBwICsgcmdiMS5hLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gbmV3IFRpbnlDb2xvcihyZ2JhKTtcbiAgICB9O1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuYW5hbG9nb3VzID0gZnVuY3Rpb24gKHJlc3VsdHMsIHNsaWNlcykge1xuICAgICAgICBpZiAocmVzdWx0cyA9PT0gdm9pZCAwKSB7IHJlc3VsdHMgPSA2OyB9XG4gICAgICAgIGlmIChzbGljZXMgPT09IHZvaWQgMCkgeyBzbGljZXMgPSAzMDsgfVxuICAgICAgICB2YXIgaHNsID0gdGhpcy50b0hzbCgpO1xuICAgICAgICB2YXIgcGFydCA9IDM2MCAvIHNsaWNlcztcbiAgICAgICAgdmFyIHJldCA9IFt0aGlzXTtcbiAgICAgICAgZm9yIChoc2wuaCA9IChoc2wuaCAtICgocGFydCAqIHJlc3VsdHMpID4+IDEpICsgNzIwKSAlIDM2MDsgLS1yZXN1bHRzOykge1xuICAgICAgICAgICAgaHNsLmggPSAoaHNsLmggKyBwYXJ0KSAlIDM2MDtcbiAgICAgICAgICAgIHJldC5wdXNoKG5ldyBUaW55Q29sb3IoaHNsKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJldDtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIHRha2VuIGZyb20gaHR0cHM6Ly9naXRodWIuY29tL2luZnVzaW9uL2pRdWVyeS14Y29sb3IvYmxvYi9tYXN0ZXIvanF1ZXJ5Lnhjb2xvci5qc1xuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuY29tcGxlbWVudCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIGhzbCA9IHRoaXMudG9Ic2woKTtcbiAgICAgICAgaHNsLmggPSAoaHNsLmggKyAxODApICUgMzYwO1xuICAgICAgICByZXR1cm4gbmV3IFRpbnlDb2xvcihoc2wpO1xuICAgIH07XG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5tb25vY2hyb21hdGljID0gZnVuY3Rpb24gKHJlc3VsdHMpIHtcbiAgICAgICAgaWYgKHJlc3VsdHMgPT09IHZvaWQgMCkgeyByZXN1bHRzID0gNjsgfVxuICAgICAgICB2YXIgaHN2ID0gdGhpcy50b0hzdigpO1xuICAgICAgICB2YXIgaCA9IGhzdi5oO1xuICAgICAgICB2YXIgcyA9IGhzdi5zO1xuICAgICAgICB2YXIgdiA9IGhzdi52O1xuICAgICAgICB2YXIgcmVzID0gW107XG4gICAgICAgIHZhciBtb2RpZmljYXRpb24gPSAxIC8gcmVzdWx0cztcbiAgICAgICAgd2hpbGUgKHJlc3VsdHMtLSkge1xuICAgICAgICAgICAgcmVzLnB1c2gobmV3IFRpbnlDb2xvcih7IGg6IGgsIHM6IHMsIHY6IHYgfSkpO1xuICAgICAgICAgICAgdiA9ICh2ICsgbW9kaWZpY2F0aW9uKSAlIDE7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlcztcbiAgICB9O1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUuc3BsaXRjb21wbGVtZW50ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgaHNsID0gdGhpcy50b0hzbCgpO1xuICAgICAgICB2YXIgaCA9IGhzbC5oO1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgdGhpcyxcbiAgICAgICAgICAgIG5ldyBUaW55Q29sb3IoeyBoOiAoaCArIDcyKSAlIDM2MCwgczogaHNsLnMsIGw6IGhzbC5sIH0pLFxuICAgICAgICAgICAgbmV3IFRpbnlDb2xvcih7IGg6IChoICsgMjE2KSAlIDM2MCwgczogaHNsLnMsIGw6IGhzbC5sIH0pLFxuICAgICAgICBdO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQ29tcHV0ZSBob3cgdGhlIGNvbG9yIHdvdWxkIGFwcGVhciBvbiBhIGJhY2tncm91bmRcbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLm9uQmFja2dyb3VuZCA9IGZ1bmN0aW9uIChiYWNrZ3JvdW5kKSB7XG4gICAgICAgIHZhciBmZyA9IHRoaXMudG9SZ2IoKTtcbiAgICAgICAgdmFyIGJnID0gbmV3IFRpbnlDb2xvcihiYWNrZ3JvdW5kKS50b1JnYigpO1xuICAgICAgICB2YXIgYWxwaGEgPSBmZy5hICsgYmcuYSAqICgxIC0gZmcuYSk7XG4gICAgICAgIHJldHVybiBuZXcgVGlueUNvbG9yKHtcbiAgICAgICAgICAgIHI6IChmZy5yICogZmcuYSArIGJnLnIgKiBiZy5hICogKDEgLSBmZy5hKSkgLyBhbHBoYSxcbiAgICAgICAgICAgIGc6IChmZy5nICogZmcuYSArIGJnLmcgKiBiZy5hICogKDEgLSBmZy5hKSkgLyBhbHBoYSxcbiAgICAgICAgICAgIGI6IChmZy5iICogZmcuYSArIGJnLmIgKiBiZy5hICogKDEgLSBmZy5hKSkgLyBhbHBoYSxcbiAgICAgICAgICAgIGE6IGFscGhhLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEFsaWFzIGZvciBgcG9seWFkKDMpYFxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudHJpYWQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnBvbHlhZCgzKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEFsaWFzIGZvciBgcG9seWFkKDQpYFxuICAgICAqL1xuICAgIFRpbnlDb2xvci5wcm90b3R5cGUudGV0cmFkID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wb2x5YWQoNCk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBHZXQgcG9seWFkIGNvbG9ycywgbGlrZSAoZm9yIDEsIDIsIDMsIDQsIDUsIDYsIDcsIDgsIGV0Yy4uLilcbiAgICAgKiBtb25hZCwgZHlhZCwgdHJpYWQsIHRldHJhZCwgcGVudGFkLCBoZXhhZCwgaGVwdGFkLCBvY3RhZCwgZXRjLi4uXG4gICAgICovXG4gICAgVGlueUNvbG9yLnByb3RvdHlwZS5wb2x5YWQgPSBmdW5jdGlvbiAobikge1xuICAgICAgICB2YXIgaHNsID0gdGhpcy50b0hzbCgpO1xuICAgICAgICB2YXIgaCA9IGhzbC5oO1xuICAgICAgICB2YXIgcmVzdWx0ID0gW3RoaXNdO1xuICAgICAgICB2YXIgaW5jcmVtZW50ID0gMzYwIC8gbjtcbiAgICAgICAgZm9yICh2YXIgaSA9IDE7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHJlc3VsdC5wdXNoKG5ldyBUaW55Q29sb3IoeyBoOiAoaCArIGkgKiBpbmNyZW1lbnQpICUgMzYwLCBzOiBoc2wucywgbDogaHNsLmwgfSkpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBjb21wYXJlIGNvbG9yIHZzIGN1cnJlbnQgY29sb3JcbiAgICAgKi9cbiAgICBUaW55Q29sb3IucHJvdG90eXBlLmVxdWFscyA9IGZ1bmN0aW9uIChjb2xvcikge1xuICAgICAgICByZXR1cm4gdGhpcy50b1JnYlN0cmluZygpID09PSBuZXcgVGlueUNvbG9yKGNvbG9yKS50b1JnYlN0cmluZygpO1xuICAgIH07XG4gICAgcmV0dXJuIFRpbnlDb2xvcjtcbn0oKSk7XG5leHBvcnQgeyBUaW55Q29sb3IgfTtcbi8vIGtlcHQgZm9yIGJhY2t3YXJkcyBjb21wYXRhYmlsaXR5IHdpdGggdjFcbmV4cG9ydCBmdW5jdGlvbiB0aW55Y29sb3IoY29sb3IsIG9wdHMpIHtcbiAgICBpZiAoY29sb3IgPT09IHZvaWQgMCkgeyBjb2xvciA9ICcnOyB9XG4gICAgaWYgKG9wdHMgPT09IHZvaWQgMCkgeyBvcHRzID0ge307IH1cbiAgICByZXR1cm4gbmV3IFRpbnlDb2xvcihjb2xvciwgb3B0cyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js":
/*!**********************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/util.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bound01: () => (/* binding */ bound01),\n/* harmony export */   boundAlpha: () => (/* binding */ boundAlpha),\n/* harmony export */   clamp01: () => (/* binding */ clamp01),\n/* harmony export */   convertToPercentage: () => (/* binding */ convertToPercentage),\n/* harmony export */   isOnePointZero: () => (/* binding */ isOnePointZero),\n/* harmony export */   isPercentage: () => (/* binding */ isPercentage),\n/* harmony export */   pad2: () => (/* binding */ pad2)\n/* harmony export */ });\n/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nfunction bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nfunction clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nfunction isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nfunction isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nfunction boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nfunction convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nfunction pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\n");

/***/ })

};
;