export declare class CalculationUtils {
    static calculateCompletionRate(publishedCourses: number, totalCourses: number): number;
    static calculateSkip(page: number, pageSize: number): number;
    static calculateTotalPages(total: number, pageSize: number): number;
    static calculateNextOrderIndex(maxOrderIndex: number | null | undefined): number;
    static formatVideoDuration(durationInSeconds: number): string;
    static calculatePaginationInfo(page: number, pageSize: number, total: number): {
        page: number;
        pageSize: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
        skip: number;
    };
}
