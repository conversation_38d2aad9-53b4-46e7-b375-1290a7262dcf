'use client';

import React, { useState, useEffect, useRef } from 'react';
import { X, ArrowLeft, Users, CheckCircle } from 'lucide-react';
import { GetNotification } from 'logic-common/dist/components/Notification';
import TemplatePickerModal from './TemplatePickerModal';
import { BatchUseKeyPackageModal } from './BatchUseKeyPackageModal';

import './TemplatePickerModal.css';
import './NewPublishTaskModal.css';
import './TemplateSelectionModal.css';

// 导入API
import { pointsApi } from '@/lib/api/points';
import taskApi, { TaskType, Priority } from '@/lib/api/task';
import { batchAddUserJoinRole } from '@/lib/api/role';

// 导入工具函数
import {
  Student,
  fetchClassStudentsWithNotification,
  fetchStudentPoints,
  WorksState,
  DragState,
  fixImageUrl,
  fetchWorks,
  handleSelectWork,
  handleWheelScroll,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleMouseLeave,
  getTaskTimes
} from '../utils';

interface TemplateSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onConfirm?: (taskData: any) => void;
  actionType: string;
  selectedSchool: any;
  selectedClass: any;
}



const TemplateSelectionModal: React.FC<TemplateSelectionModalProps> = ({
  isOpen,
  onClose,
  onBack,
  onConfirm,
  actionType,
  selectedSchool,
  selectedClass
}) => {
  const [mounted, setMounted] = useState(false);
  const [hoveredOption, setHoveredOption] = useState<string>('');
  const [focusedInput, setFocusedInput] = useState<string>(''); // 跟踪哪个输入框有焦点

  // 输入框引用
  const assignInputRef = useRef<HTMLInputElement>(null);
  const distributeInputRef = useRef<HTMLInputElement>(null);
  // 移除教师能量相关状态，因为不需要检查教师能量池
  const [isTemplatePickerOpen, setIsTemplatePickerOpen] = useState(false);

  const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = useState(false);

  // 学生相关状态
  const [students, setStudents] = useState<Student[]>([]);
  const [studentPointsMap, setStudentPointsMap] = useState<Map<number, number>>(new Map());
  const [loadingStudentPoints, setLoadingStudentPoints] = useState(false);

  // 存储模态框数据的状态
  const [modalData, setModalData] = useState({
    selectedDistribution: 'none', // 默认选择"不分配"
    assignEnergyAmount: '', // 分配按钮的能量数值
    distributeEnergyAmount: '', // 分配至按钮的能量数值
    selectedTemplate: null as any,
    selectedStudents: [] as number[], // 默认为所有学生
  });

  // 输入验证错误状态
  const [inputErrors, setInputErrors] = useState({
    assignEnergyError: '',
    distributeEnergyError: ''
  });

  // 当前步骤状态
  const [currentStep, setCurrentStep] = useState<'template' | 'publish'>('template');

  // 发布任务相关状态
  const [taskData, setTaskData] = useState({
    taskName: '',
    taskDescription: '',
    selfAssessmentItems: [] as string[],
    duration: '1小时', // 默认持续时间
    startTime: '',
    endTime: ''
  });

  // 持续时间选择器状态
  const [showDurationSelector, setShowDurationSelector] = useState(false);

  // 点击外部关闭持续时间选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showDurationSelector && !target.closest('.duration-field-container')) {
        setShowDurationSelector(false);
      }
    };

    if (showDurationSelector) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDurationSelector]);



  // 持续时间选择函数
  const handleDurationSelect = (duration: string) => {
    setTaskData(prev => ({ ...prev, duration }));
  };




  const [activeTab, setActiveTab] = useState<'task' | 'resources'>('task');
  const [works, setWorks] = useState<any[]>([]);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // 发布任务加载状态
  const [isPublishing, setIsPublishing] = useState(false);

  // 鼠标拖拽滚动状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });
  const pageSize = 10;

  // 阻止背景页面滚动
  useEffect(() => {
    if (isOpen) {
      // 保存原始的 overflow 样式
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // 阻止背景滚动
      document.body.style.overflow = 'hidden';

      return () => {
        // 恢复原始样式
        document.body.style.overflow = originalStyle;
      };
    }
  }, [isOpen]);



  // 获取作品列表（分页懒加载）
  const fetchWorksData = async (pageNum: number = 1, isLoadMore: boolean = false) => {
    const currentState: WorksState = {
      works,
      selectedWorkIds,
      page,
      hasMore,
      loadingWorks,
      loadingMore
    };

    const setState = (newState: Partial<WorksState>) => {
      if (newState.works !== undefined) setWorks(newState.works);
      if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);
      if (newState.page !== undefined) setPage(newState.page);
      if (newState.hasMore !== undefined) setHasMore(newState.hasMore);
      if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);
      if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);
    };

    await fetchWorks(pageNum, pageSize, isLoadMore, currentState, setState);
  };

  // 加载更多作品
  const loadMoreWorksData = () => {
    if (!loadingMore && hasMore) {
      const nextPage = page + 1;
      fetchWorksData(nextPage, true);
    }
  };

  // 选择作品（支持多选）
  const handleSelectWorkData = (workId: number) => {
    const setState = (newState: Partial<WorksState>) => {
      if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);
    };

    handleSelectWork(workId, selectedWorkIds, setState);
  };



  // 创建鼠标处理函数的包装器
  const handleMouseDownWrapper = (e: React.MouseEvent<HTMLDivElement>) => {
    const setDragState = (newState: Partial<DragState>) => {
      if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);
      if (newState.dragStart !== undefined) setDragStart(newState.dragStart);
    };
    handleMouseDown(e, setDragState);
  };

  const handleMouseMoveWrapper = (e: React.MouseEvent<HTMLDivElement>) => {
    const dragState: DragState = { isDragging, dragStart };
    handleMouseMove(e, dragState);
  };

  const handleMouseUpWrapper = (e: React.MouseEvent<HTMLDivElement>) => {
    const setDragState = (newState: Partial<DragState>) => {
      if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);
      if (newState.dragStart !== undefined) setDragStart(newState.dragStart);
    };
    handleMouseUp(e, setDragState);
  };

  const handleMouseLeaveWrapper = (e: React.MouseEvent<HTMLDivElement>) => {
    const setDragState = (newState: Partial<DragState>) => {
      if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);
      if (newState.dragStart !== undefined) setDragStart(newState.dragStart);
    };
    handleMouseLeave(e, setDragState);
  };

  // 当切换到发布任务步骤且选择资源标签页时获取作品列表
  useEffect(() => {
    if (currentStep === 'publish' && activeTab === 'resources' && mounted) {
      // 重置作品状态
      setWorks([]);
      setPage(1);
      setHasMore(true);
      setSelectedWorkIds([]);
      setLoadingMore(false);

      fetchWorksData(1, false);
    }
  }, [currentStep, activeTab, mounted]);

  // 清空所有数据的函数
  const clearAllData = () => {
    // 重置步骤状态
    setCurrentStep('template');

    // 重置模态框数据
    setModalData({
      selectedDistribution: '',
      assignEnergyAmount: '',
      distributeEnergyAmount: '',
      selectedTemplate: null,
      selectedStudents: []
    });

    // 重置错误状态
    setInputErrors({
      assignEnergyError: '',
      distributeEnergyError: ''
    });

    // 重置任务数据
    setTaskData({
      taskName: '',
      taskDescription: '',
      selfAssessmentItems: [],
      duration: '1小时',
      startTime: '',
      endTime: ''
    });

    // 重置其他状态
    setActiveTab('task');
    setWorks([]);
    setSelectedWorkIds([]);
    setAttachments([]);
    setHoveredOption('');
    setIsTemplatePickerOpen(false);
    setIsBatchUseKeyPackageModalOpen(false);
    setPage(1);
    setHasMore(true);
    setLoadingMore(false);

    // 重置学生相关数据
    setStudents([]);
    setStudentPointsMap(new Map());
  };

  // 监听模态框关闭，清空数据
  useEffect(() => {
    if (!isOpen) {
      clearAllData();
    }
  }, [isOpen]);

  // 组件卸载时清空数据
  useEffect(() => {
    return () => {
      clearAllData();
    };
  }, []);



  const notification = GetNotification();

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 移除获取教师可分配能量的函数，因为不需要检查教师能量池

  // 获取班级学生列表和能量信息（并行处理）
  const fetchClassStudentsData = async () => {
    if (!selectedClass?.id || !mounted) return;

    setLoadingStudentPoints(true); // 提前设置能量加载状态

    try {
      const studentsData = await fetchClassStudentsWithNotification(selectedClass.id, notification);
      setStudents(studentsData);

      // 立即并行获取学生能量信息，不等待学生列表完全处理完
      if (studentsData.length > 0) {
        // 不等待，立即开始获取能量信息
        const pointsMap = await fetchStudentPoints(studentsData.map((s: Student) => s.userId), notification);
        setStudentPointsMap(pointsMap);
      }
      setLoadingStudentPoints(false);
    } catch (error) {
      console.error('获取学生列表失败:', error);
      setStudents([]);
      setLoadingStudentPoints(false);
    }
  };



  // 移除获取教师能量的 useEffect

  useEffect(() => {
    if (isOpen && selectedClass) {
      fetchClassStudentsData();
      // 重置模态框数据
      setModalData({
        selectedDistribution: isBlocksOnlyMode ? 'none' : 'none', // 纯积木分配模式时默认不分配能量
        assignEnergyAmount: '',
        distributeEnergyAmount: '',
        selectedTemplate: null,
        selectedStudents: [], // 将在获取学生列表后设置为所有学生
      });

      // 重置错误状态
      setInputErrors({
        assignEnergyError: '',
        distributeEnergyError: ''
      });

      // 禁用body滚动
      document.body.style.overflow = 'hidden';
    } else {
      // 恢复body滚动
      document.body.style.overflow = '';
    }

    // 清理函数：组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, selectedClass, mounted]);

  // 当学生列表加载完成后，自动选择所有学生
  useEffect(() => {
    if (students.length > 0) {
      setModalData(prev => ({
        ...prev,
        selectedStudents: students.map(s => s.userId)
      }));
    }
  }, [students]);

  const distributionOptions = [
    { id: 'none', label: '不分配', description: '保持原有设置', hasInput: false },
    { id: 'assign', label: '分配', description: '分配给学生', hasInput: true },
    { id: 'distribute', label: '分配至', description: '分配到指定位置', hasInput: true }
  ];

  // 判断是否为纯积木分配模式（不涉及能量）
  const isBlocksOnlyMode = actionType === '分配积木';

  // 判断是否为纯能量分配模式（不涉及积木）
  const isEnergyOnlyMode = actionType === '分配能量';

  const handleDistributionSelect = (optionId: string) => {
    setModalData(prev => ({
      ...prev,
      selectedDistribution: optionId
    }));

    // 自动聚焦到对应的输入框
    setTimeout(() => {
      if (optionId === 'assign' && assignInputRef.current) {
        assignInputRef.current.focus();
      } else if (optionId === 'distribute' && distributeInputRef.current) {
        distributeInputRef.current.focus();
      }
    }, 100); // 延迟一点确保输入框已经渲染
  };

  const handleTemplateSelect = (template: any) => {
    setModalData(prev => ({
      ...prev,
      selectedTemplate: template
    }));
  };

  const handleCancelTemplate = () => {
    setModalData(prev => ({
      ...prev,
      selectedTemplate: null
      // 不重置selectedDistribution，保持用户的能量分配选择
    }));
  };

  const handleTemplatePickerOpen = () => {
    setIsTemplatePickerOpen(true);
  };

  const handleTemplatePickerClose = () => {
    setIsTemplatePickerOpen(false);
  };

  // 处理批量兑换密钥模态框
  const handleBatchUseKeyPackageModalOpen = () => {
    setIsBatchUseKeyPackageModalOpen(true);
  };

  const handleBatchUseKeyPackageModalClose = () => {
    setIsBatchUseKeyPackageModalOpen(false);
  };

  const handleBatchUseKeyPackageSuccess = async () => {
    // 兑换成功后重新获取学生能量信息
    if (students.length > 0) {
      const pointsMap = await fetchStudentPoints(students.map(s => s.userId), notification);
      setStudentPointsMap(pointsMap);
    }
    notification.success('密钥兑换成功！');
  };

  // 获取当前选中分配方式对应的能量数值
  const getCurrentEnergyAmount = () => {
    if (modalData.selectedDistribution === 'assign') {
      return modalData.assignEnergyAmount;
    } else if (modalData.selectedDistribution === 'distribute') {
      return modalData.distributeEnergyAmount;
    }
    return '';
  };

  // 计算所有学生的最低可分配能量
  const getMinAvailablePoints = () => {
    if (modalData.selectedStudents.length === 0) return 0;

    const selectedStudentPoints = modalData.selectedStudents.map(studentId =>
      studentPointsMap.get(studentId) || 0
    );

    return Math.min(...selectedStudentPoints);
  };

  // 获取当前分配方式的提示信息
  const getEnergyDisplayInfo = () => {
    if (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') {
      return {
        label: '最低可分配能量',
        value: getMinAvailablePoints()
      };
    }
    return { label: '', value: 0 };
  };

  const handleNext = () => {
    console.log('选择的分配方式:', modalData.selectedDistribution);
    console.log('选择的模板:', modalData.selectedTemplate);

    // 在纯积木分配模式时，跳过能量验证
    if (!isBlocksOnlyMode && (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute')) {
      const currentEnergyAmount = getCurrentEnergyAmount();
      console.log('分配能量数量:', currentEnergyAmount);

      // 检查是否有输入错误
      const errorKey = modalData.selectedDistribution === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';
      if (inputErrors[errorKey as keyof typeof inputErrors]) {
        notification.error('请修正输入错误后再继续');
        return;
      }

      // 检查能量数量是否有效
      const energyAmountNum = Number(currentEnergyAmount);
      if (!currentEnergyAmount || energyAmountNum <= 0) {
        notification.error('请输入有效的分配能量数量');
        return;
      }

      if (modalData.selectedDistribution === 'assign') {
        // "分配"按钮：检查选中学生的可分配能量是否足够
        const insufficientStudents = modalData.selectedStudents.filter(studentId => {
          const studentAvailablePoints = studentPointsMap.get(studentId);
          return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;
        });

        if (insufficientStudents.length > 0) {
          const insufficientNames = insufficientStudents.map(studentId => {
            const student = students.find(s => s.userId === studentId);
            const availablePoints = studentPointsMap.get(studentId) || 0;
            return `${student?.nickName || `学生${studentId}`}(可分配: ${availablePoints})`;
          }).join('、');

          notification.error(`积分不足：以下学生的可分配能量不足 ${energyAmountNum}：${insufficientNames}`);
          return;
        }
      } else if (modalData.selectedDistribution === 'distribute') {
        // "分配至"按钮：检查需要补充能量的学生
        const studentsNeedingEnergy = modalData.selectedStudents.filter(studentId => {
          const currentPoints = studentPointsMap.get(studentId) || 0;
          return currentPoints < energyAmountNum;
        });

        if (studentsNeedingEnergy.length > 0) {
          // 检查这些学生是否有足够的可分配能量来达到目标值
          const insufficientStudents = studentsNeedingEnergy.filter(studentId => {
            const currentPoints = studentPointsMap.get(studentId) || 0;
            const neededPoints = energyAmountNum - currentPoints;
            const studentAvailablePoints = studentPointsMap.get(studentId);
            return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;
          });

          if (insufficientStudents.length > 0) {
            const insufficientNames = insufficientStudents.map(studentId => {
              const student = students.find(s => s.userId === studentId);
              const currentPoints = studentPointsMap.get(studentId) || 0;
              const neededPoints = energyAmountNum - currentPoints;
              const availablePoints = studentPointsMap.get(studentId) || 0;
              return `${student?.nickName || `学生${studentId}`}(需要: ${neededPoints}, 可分配: ${availablePoints})`;
            }).join('、');

            notification.error(`积分不足：以下学生无法达到目标能量值 ${energyAmountNum}：${insufficientNames}`);
            return;
          }
        }
      }
    }

    // 根据操作类型决定下一步
    if (actionType === '发布任务' || actionType === '快速上课') {
      // 发布任务或快速上课：切换到发布任务步骤
      setTaskData(prev => ({
        ...prev,
        duration: '1小时' // 重置为默认持续时间
      }));
      setCurrentStep('publish');
    } else {
      // 分配积木或分配能量：直接执行分配操作并关闭弹窗
      console.log(`${actionType}操作执行`, {
        selectedDistribution: modalData.selectedDistribution,
        selectedTemplate: modalData.selectedTemplate,
        selectedStudents: modalData.selectedStudents,
        energyAmount: getCurrentEnergyAmount()
      });

      // 这里可以调用相应的API来执行分配操作
      // TODO: 实现分配积木和分配能量的API调用

      notification.success(`${actionType}成功！`);
      clearAllData();
      onClose();
    }
  };

  const handlePrevious = () => {
    if (currentStep === 'publish') {
      // 从发布任务步骤返回时重置持续时间
      setTaskData(prev => ({
        ...prev,
        duration: '1小时'
      }));
      setCurrentStep('template');
    } else {
      onBack();
    }
  };



  // 确认发布（与NewPublishTaskModal保持一致）
  const handleConfirm = () => {
    if (!taskData.taskName.trim()) {
      notification.error('请输入任务名称');
      return;
    }

    const times = getTaskTimes(taskData.duration);
    const finalTaskData = {
      ...taskData,
      startTime: times.startTime,
      endTime: times.endTime,
      selectedWorkIds,
      attachments,
      modalData
    };

    // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑
    if (onConfirm) {
      onConfirm(finalTaskData);
    } else {
      // 保留原有的发布逻辑作为后备
      handlePublishTaskOk();
    }
  };

  const handlePublishTaskOk = async () => {
    // 防止重复点击
    if (isPublishing) {
      return;
    }

    try {
      setIsPublishing(true);

      // 验证必填字段
      if (!taskData.taskName.trim()) {
        notification.error('请输入任务名称');
        setIsPublishing(false);
        return;
      }

      console.log('发布任务:', taskData);
      console.log('选中的作品ID:', selectedWorkIds);
      console.log('模态框数据:', modalData);

      // 获取当前用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const teacherId = user?.userId;

      if (!teacherId) {
        notification.error('未找到用户信息');
        setIsPublishing(false);
        return;
      }

      // 处理时间
      const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();
      const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      // 构建任务发布参数
      const taskParams = {
        taskName: taskData.taskName,
        taskDescription: taskData.taskDescription || '',
        taskType: TaskType.GRAPHIC, // 默认为图形化任务
        priority: Priority.NORMAL, // 默认为普通优先级
        startDate: startDate,
        endDate: endDate,
        taskContent: taskData.taskDescription || '', // 使用任务描述作为内容
        attachments: attachments.map(file => file.name) || [], // 转换为文件名数组
        isPublic: 1, // 公开任务
        allowLateSubmission: false,
        studentIds: modalData.selectedStudents,
        classId: selectedClass?.id,
        templateId: modalData.selectedTemplate?.id,
        workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined, // 传递作品ID数组
        selfAssessmentItems: taskData.selfAssessmentItems.filter(item => item.trim() !== '') || [] // 过滤空的自评项
      };

      console.log('任务发布参数:', taskParams);
      console.log('作品ID数组:', taskParams.workIds);

      // 准备并行请求数组
      const requests: Promise<any>[] = [];

      // 1. 任务发布请求（必须执行）
      requests.push(taskApi.publishTask(taskParams));

      // 2. 能量分配请求（如果需要）
      let energyRequest: Promise<any> | null = null;
      const currentEnergyAmount = getCurrentEnergyAmount();
      if ((modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && currentEnergyAmount) {
        const targetAmount = Number(currentEnergyAmount);
        const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

        if (modalData.selectedDistribution === 'assign') {
          // "分配"按钮：给每个学生分配固定数量的能量
          const studentExpiries: { [studentId: number]: string } = {};
          modalData.selectedStudents.forEach(studentId => {
            studentExpiries[studentId] = defaultExpireTime;
          });

          energyRequest = pointsApi.batchAssignPermission({
            availablePoints: targetAmount,
            studentExpiries,
            remark: `任务发布 - ${taskData.taskName}`
          });
          requests.push(energyRequest);
        } else if (modalData.selectedDistribution === 'distribute') {
          // "分配至"按钮：将学生能量补充到目标值
          const energyRequests: Promise<any>[] = [];

          modalData.selectedStudents.forEach(studentId => {
            const currentPoints = studentPointsMap.get(studentId) || 0;
            const neededPoints = targetAmount - currentPoints;

            // 只有当学生当前能量小于目标值时才分配
            if (neededPoints > 0) {
              const studentExpiries: { [studentId: number]: string } = {};
              studentExpiries[studentId] = defaultExpireTime;

              const request = pointsApi.batchAssignPermission({
                availablePoints: neededPoints,
                studentExpiries,
                remark: `任务发布 - ${taskData.taskName} (补充至${targetAmount})`
              });
              energyRequests.push(request);
            }
          });

          // 将所有能量分配请求添加到主请求列表
          requests.push(...energyRequests);
        }
      }

      // 3. 模板分配请求（如果需要）
      let templateRequest: Promise<any> | null = null;
      if (modalData.selectedTemplate) {
        const users = modalData.selectedStudents.map(studentId => ({
          userId: studentId,
          roleId: 1,
          templateId: modalData.selectedTemplate.id,
          originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id
        }));

        templateRequest = batchAddUserJoinRole({ users });
        requests.push(templateRequest);
      }

      // 并行执行所有请求
      const results = await Promise.allSettled(requests);

      // 处理任务发布结果
      const taskResult = results[0];
      if (taskResult.status === 'fulfilled' && taskResult.value.data.code === 200) {
        // 显示成功发布任务的提示
        notification.success('🎉 任务发布成功！学生可以开始学习了');
      } else {
        const errorMsg = taskResult.status === 'fulfilled'
          ? taskResult.value.data.message || '任务发布失败'
          : '任务发布失败';
        notification.error(errorMsg);
        setIsPublishing(false);
        return; // 任务发布失败则直接返回
      }

      // 处理能量分配结果
      let resultIndex = 1;
      if (energyRequest) {
        const energyResult = results[resultIndex];
        if (energyResult.status === 'fulfilled' && energyResult.value.data.code === 200) {
          notification.success('能量分配完成！');
        } else {
          console.error('能量分配失败:', energyResult);
          notification.warning('能量分配失败');
        }
        resultIndex++;
      }

      // 处理模板分配结果
      if (templateRequest) {
        const templateResult = results[resultIndex];
        if (templateResult.status === 'fulfilled' && templateResult.value.data.code === 200) {
          notification.success('模板分配完成！');
        } else {
          console.error('模板分配失败:', templateResult);
          notification.warning('模板分配失败');
        }
      }

      // 延迟关闭弹窗，让用户能看到成功提示
      setTimeout(() => {
        setIsPublishing(false);
        clearAllData();
        onClose();
      }, 800);
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error('任务发布失败，请重试');
      setIsPublishing(false);
    }
  };

  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div
      className="modal-overlay"
      onWheel={(e) => {
        // 只阻止事件传播，依赖CSS控制滚动行为
        e.stopPropagation();
      }}
      onTouchMove={(e) => {
        // 只阻止事件传播，不调用preventDefault避免被动监听器警告
        e.stopPropagation();
      }}
    >
      <div className="modal-wrapper">
        <button className="modal-close-btn-outside" onClick={() => {
          clearAllData();
          onClose();
        }}>
          <X size={20} />
        </button>
        <div className="modal-content template-selection-modal" data-step={currentStep}>

        {/* 步骤指示器 */}
        <div className="step-indicator">
          <div className="step completed">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          <div className={`step ${currentStep === 'template' ? 'active' : 'completed'}`}>
            <div className="step-number">2</div>
            <div className="step-label">
              {actionType === '分配积木' ? '分配积木' :
               actionType === '分配能量' ? '分配能量' :
               '能量和积木'}
            </div>
          </div>
          {(actionType === '发布任务' || actionType === '快速上课') && (
            <div className={`step ${currentStep === 'publish' ? 'active' : ''}`}>
              <div className="step-number">3</div>
              <div className="step-label">发布任务</div>
            </div>
          )}
        </div>

        {/* 内容区域 */}
        <div className="modal-content-body">
          {currentStep === 'template' ? (
            <>
              {/* 能量分配部分 - 只在非纯积木分配模式时显示 */}
              {!isBlocksOnlyMode && (
                <div className="modal-content-header">
                  <h3 className="section-title">为学生分配能量</h3>
                </div>
              )}

              <div className="modal-content-scrollable">
                {/* 分配选项 - 只在非纯积木分配模式时显示 */}
                {!isBlocksOnlyMode && (
                  <div className="distribution-options">
                {distributionOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`distribution-card ${modalData.selectedDistribution === option.id ? 'selected' : ''}`}
                    onClick={() => handleDistributionSelect(option.id)}
                    onMouseEnter={() => setHoveredOption(option.id)}
                    onMouseLeave={() => setHoveredOption('')}
                  >
                    <div className="distribution-label">
                      {(() => {
                        const currentAmount = option.id === 'assign' ? modalData.assignEnergyAmount :
                                            option.id === 'distribute' ? modalData.distributeEnergyAmount : '';
                        return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id
                          ? `${option.label} ${currentAmount}能量`
                          : option.label;
                      })()}
                    </div>
                    {option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && (
                      <div className="energy-input-container">
                        <input
                          ref={option.id === 'assign' ? assignInputRef : distributeInputRef}
                          type="number"
                          className="energy-input"
                          placeholder={option.id === 'assign' ? '输入能量' : '输入目标值'}
                          value={option.id === 'assign' ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount}
                          min="1"
                          onChange={(e) => {
                            const value = e.target.value;
                            const updateKey = option.id === 'assign' ? 'assignEnergyAmount' : 'distributeEnergyAmount';
                            const errorKey = option.id === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';

                            // 清除之前的错误
                            setInputErrors(prev => ({ ...prev, [errorKey]: '' }));

                            // 允许空值或正整数
                            if (value === '') {
                              setModalData(prev => ({ ...prev, [updateKey]: value }));
                            } else {
                              const numValue = Number(value);
                              if (Number.isInteger(numValue)) {
                                if (numValue < 1) {
                                  // 设置错误提示
                                  setInputErrors(prev => ({ ...prev, [errorKey]: '输入能量不能低于1' }));
                                  setModalData(prev => ({ ...prev, [updateKey]: value }));
                                } else {
                                  // 有效输入
                                  setModalData(prev => ({ ...prev, [updateKey]: value }));
                                  // 输入数字时自动选中当前悬停的分配按钮
                                  setModalData(prev => ({ ...prev, selectedDistribution: option.id }));
                                }
                              }
                            }
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            // 点击输入框时自动选中当前悬停的分配按钮
                            setModalData(prev => ({ ...prev, selectedDistribution: option.id }));
                          }}
                          onFocus={() => {
                            setFocusedInput(option.id);
                            // 聚焦时也自动选中分配选项
                            setModalData(prev => ({ ...prev, selectedDistribution: option.id }));
                          }}
                          onBlur={() => {
                            setFocusedInput('');
                          }}
                        />
                        {/* 错误提示 */}
                        {(() => {
                          const errorKey = option.id === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';
                          const errorMessage = inputErrors[errorKey as keyof typeof inputErrors];
                          return errorMessage && (
                            <div style={{
                              color: '#ef4444',
                              fontSize: '12px',
                              marginTop: '4px',
                              textAlign: 'center'
                            }}>
                              {errorMessage}
                            </div>
                          );
                        })()}
                      </div>
                    )}
                  </div>
                ))}
              </div>
                )}

              {/* 显示能量分配信息 - 只在非纯积木分配模式时显示 */}
              {!isBlocksOnlyMode && (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && modalData.selectedStudents.length > 0 && (
                <div className="min-available-energy">
                  {loadingStudentPoints ? (
                    <span style={{ color: '#6b7280', fontStyle: 'italic' }}>
                      ⏳ 正在获取能量信息...
                    </span>
                  ) : (
                    (() => {
                      const displayInfo = getEnergyDisplayInfo();
                      return (
                        <>
                          <span style={{ color: '#64748b', fontSize: '12px' }}>💡</span>
                          <span>{displayInfo.label}: {displayInfo.value}</span>
                        </>
                      );
                    })()
                  )}
                </div>
              )}

              {/* 错误提示 - 只在非纯积木分配模式时显示 */}
              {!isBlocksOnlyMode && (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && getCurrentEnergyAmount() && !loadingStudentPoints && (
                (() => {
                  const energyAmountNum = Number(getCurrentEnergyAmount());

                  // 根据分配方式进行不同的验证
                  let shouldShowError = false;
                  let errorMessage = '';

                  if (modalData.selectedDistribution === 'assign') {
                    const minAvailable = getMinAvailablePoints();
                    if (energyAmountNum > minAvailable) {
                      shouldShowError = true;
                      errorMessage = '可分配积分不足';
                    }
                  } else if (modalData.selectedDistribution === 'distribute') {
                    // 对于"分配至"，检查是否有学生无法达到目标值
                    const studentsNeedingEnergy = modalData.selectedStudents.filter(studentId => {
                      const currentPoints = studentPointsMap.get(studentId) || 0;
                      return currentPoints < energyAmountNum;
                    });

                    const insufficientStudents = studentsNeedingEnergy.filter(studentId => {
                      const currentPoints = studentPointsMap.get(studentId) || 0;
                      const neededPoints = energyAmountNum - currentPoints;
                      const studentAvailablePoints = studentPointsMap.get(studentId);
                      return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;
                    });

                    if (insufficientStudents.length > 0) {
                      shouldShowError = true;
                      errorMessage = '部分学生积分不足以达到目标值';
                    }
                  }

                  if (shouldShowError) {
                    return (
                      <div style={{
                        background: '#fef2f2',
                        border: '1px solid #fecaca',
                        borderRadius: '8px',
                        padding: '12px 16px',
                        marginTop: '8px',
                        marginBottom: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: '12px'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          flex: 1
                        }}>
                          <span style={{
                            color: '#ef4444',
                            fontSize: '13px',
                            fontWeight: '500'
                          }}>
                            ⚠️ {errorMessage}
                          </span>
                        </div>
                        <button
                          style={{
                            background: '#f97316',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '6px 12px',
                            fontSize: '12px',
                            fontWeight: '500',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            flexShrink: 0
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = '#ea580c';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = '#f97316';
                          }}
                          onClick={() => {
                            handleBatchUseKeyPackageModalOpen();
                          }}
                        >
                          兑换密钥
                        </button>
                      </div>
                    );
                  }
                  return null;
                })()
              )}

              {/* 为学生分配积木标题 - 只在非纯能量分配模式时显示 */}
              {!isEnergyOnlyMode && (
                <h3 className="section-title">为学生分配积木</h3>
              )}

              {/* 模板选择区域 - 只在非纯能量分配模式时显示 */}
              {!isEnergyOnlyMode && (
                <div className="template-selection-area">
                  {modalData.selectedTemplate ? (
                    // 已选择模板时显示模板信息
                    <div className="template-selected">
                      <div className="template-icon">
                        ⭐
                      </div>
                      <div className="template-info">
                        <span className="template-name">
                          {modalData.selectedTemplate.templateName || modalData.selectedTemplate.name}
                        </span>
                        <span className="template-label">已选择模板</span>
                      </div>
                      <button
                        className="change-template-btn"
                        onClick={handleTemplatePickerOpen}
                      >
                        更换
                      </button>
                      <button
                        className="cancel-template-btn"
                        onClick={handleCancelTemplate}
                      >
                        ×
                      </button>
                    </div>
                  ) : (
                    // 未选择模板时显示选择选项
                    <div className="template-options">
                      <div
                        className="template-option template-none selected"
                        onClick={() => {/* 不分配模板，保持当前状态 */}}
                      >
                        <div className="option-icon">✏️</div>
                        <div className="option-content">
                          <div className="option-title">不分配模板</div>
                          <div className="option-desc">保持原有设置</div>
                        </div>
                      </div>
                      <div
                        className="template-option template-select"
                        onClick={handleTemplatePickerOpen}
                      >
                        <div className="option-icon">🧩</div>
                        <div className="option-content">
                          <div className="option-title">选择模板</div>
                          <div className="option-desc">为用户提供积木模板</div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              </div>
            </>
          ) : (
            // 发布任务步骤的内容
            <div className="publish-task-content">
              {/* 标签页切换 */}
              <div className="tab-switcher">
                <button
                  className={`tab-btn ${activeTab === 'task' ? 'active' : ''}`}
                  onClick={() => setActiveTab('task')}
                >
                  任务信息
                </button>
                <button
                  className={`tab-btn ${activeTab === 'resources' ? 'active' : ''}`}
                  onClick={() => setActiveTab('resources')}
                >
                  资源与附件
                </button>
              </div>

              <div className="modal-content-scrollable">
                {activeTab === 'task' ? (
                  <div className="task-info-tab">
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-input"
                        placeholder="任务名称"
                        value={taskData.taskName}
                        onChange={(e) => setTaskData(prev => ({ ...prev, taskName: e.target.value }))}
                      />
                    </div>

                    <div className="form-group">
                      <textarea
                        className="form-textarea"
                        placeholder="任务描述"
                        value={taskData.taskDescription}
                        onChange={(e) => setTaskData(prev => ({ ...prev, taskDescription: e.target.value }))}
                        rows={4}
                      />
                    </div>

                    <div className="form-group">
                      <div className="self-assessment-section">
                        {taskData.selfAssessmentItems.length === 0 ? (
                          <button
                            type="button"
                            className="add-self-assessment-btn"
                            onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [''] }))}
                          >
                            添加自评项
                          </button>
                        ) : (
                          <>
                            <label className="form-label">自评项</label>
                            {taskData.selfAssessmentItems.map((item, index) => (
                              <div key={index} className="self-assessment-item">
                                <input
                                  type="text"
                                  className="form-input"
                                  placeholder={`自评项 ${index + 1}`}
                                  value={item}
                                  onChange={(e) => {
                                    const newItems = [...taskData.selfAssessmentItems];
                                    newItems[index] = e.target.value;
                                    setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                                  }}
                                />
                                <button
                                  type="button"
                                  className="remove-btn"
                                  onClick={() => {
                                    const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
                                    setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                                  }}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                            <button
                              type="button"
                              className="add-btn"
                              onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [...prev.selfAssessmentItems, ''] }))}
                            >
                              <span style={{ fontSize: '14px' }}>➕</span>
                              添加自评项
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* 任务持续时间设置 */}
                    <div className="form-group">
                      <div className="time-settings">
                        <label className="form-label">任务持续时间</label>
                        <div className="duration-options">
                          <button
                            className={`duration-option ${taskData.duration === '1小时' ? 'active' : ''}`}
                            onClick={() => handleDurationSelect('1小时')}
                          >
                            1小时
                          </button>
                          <button
                            className={`duration-option ${taskData.duration === '3小时' ? 'active' : ''}`}
                            onClick={() => handleDurationSelect('3小时')}
                          >
                            3小时
                          </button>
                          <button
                            className={`duration-option ${taskData.duration === '1天' ? 'active' : ''}`}
                            onClick={() => handleDurationSelect('1天')}
                          >
                            1天
                          </button>
                          <button
                            className={`duration-option ${taskData.duration === '7天' ? 'active' : ''}`}
                            onClick={() => handleDurationSelect('7天')}
                          >
                            7天
                          </button>
                          <button
                            className={`duration-option ${taskData.duration === '1个月' ? 'active' : ''}`}
                            onClick={() => handleDurationSelect('1个月')}
                          >
                            1个月
                          </button>
                        </div>
                        <p className="duration-hint">任务将从创建时开始，持续所选时间</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="resources-tab">
                    <div className="works-section">
                      <h4>选择作品</h4>
                      <p className="help-text">选择作品作为任务参考资料（可多选）</p>
                      <div
                        className="relative works-scroll-wrapper"
                        onWheel={handleWheelScroll}
                        onMouseDown={handleMouseDownWrapper}
                        onMouseMove={handleMouseMoveWrapper}
                        onMouseUp={handleMouseUpWrapper}
                        onMouseLeave={handleMouseLeaveWrapper}
                        style={{
                          minHeight: '200px',
                          cursor: 'grab',
                          userSelect: 'none'
                        }}
                      >
                        {loadingWorks ? (
                          <div
                            className="loading-container"
                            style={{ minHeight: '200px' }}
                          >
                            <div className="loading-spinner"></div>
                            <span>加载中...</span>
                          </div>
                        ) : works.length > 0 ? (
                          <div className="works-horizontal-scroll">
                            {/* 对作品进行排序：已选中的作品显示在前面 */}
                            {works
                              .sort((a, b) => {
                                const aSelected = selectedWorkIds.includes(a.id);
                                const bSelected = selectedWorkIds.includes(b.id);
                                // 已选中的排在前面
                                if (aSelected && !bSelected) return -1;
                                if (!aSelected && bSelected) return 1;
                                return 0;
                              })
                              .map((work) => (
                              <div
                                key={work.id}
                                className={`work-card ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}
                                onClick={() => handleSelectWorkData(work.id)}
                              >
                                {/* 作品预览区域 */}
                                <div className="work-preview">
                                  {work.coverImage || work.screenShotImage ? (
                                    <img
                                      src={fixImageUrl(work.coverImage || work.screenShotImage)}
                                      alt={work.title}
                                      className="work-image"
                                      onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                        const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                        if (nextElement) {
                                          nextElement.style.display = 'flex';
                                        }
                                      }}
                                    />
                                  ) : null}
                                  <div className="work-placeholder" style={{ display: work.coverImage || work.screenShotImage ? 'none' : 'flex' }}>
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z" stroke="currentColor" strokeWidth="2"/>
                                      <path d="M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z" fill="currentColor"/>
                                      <path d="M21 15L16 10L11 15H21Z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
                                    </svg>
                                  </div>
                                  {/* 选择指示器 */}
                                  <div className={`selection-indicator ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}>
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                  </div>
                                </div>
                                {/* 作品信息区域 */}
                                <div className="work-info">
                                  <div className="work-title">{work.title || work.name || work.workName || '未命名作品'}</div>
                                </div>
                              </div>
                            ))}
                            {/* 加载更多按钮 */}
                            {hasMore && (
                              <div className="load-more-container">
                                <button
                                  className="load-more-btn"
                                  onClick={loadMoreWorksData}
                                  disabled={loadingMore}
                                >
                                  {loadingMore ? (
                                    <>
                                      <div className="loading-spinner-small"></div>
                                      <span>加载中...</span>
                                    </>
                                  ) : (
                                    <>
                                      <span>加载更多</span>
                                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 5V19M5 12L12 19L19 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                      </svg>
                                    </>
                                  )}
                                </button>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="empty-placeholder">
                            <div className="empty-text">作品列表</div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="attachments-section">
                      <h4>附件上传</h4>
                      <div className="upload-area">
                        <input
                          type="file"
                          multiple
                          accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                          onChange={(e) => {
                            if (e.target.files) {
                              const files = Array.from(e.target.files);
                              const validFiles: File[] = [];
                              const invalidFiles: string[] = [];

                              // 支持的文件格式
                              const allowedTypes = [
                                'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                                'application/pdf',
                                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                'text/plain'
                              ];

                              // 文件扩展名检查（作为备用验证）
                              const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'];

                              files.forEach(file => {
                                // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）
                                if (file.size > 10 * 1024 * 1024) {
                                  invalidFiles.push(`${file.name}：文件大小超过10MB`);
                                  return;
                                }

                                // 检查文件类型
                                const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
                                const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);

                                if (!isValidType) {
                                  invalidFiles.push(`${file.name}：不支持的文件格式`);
                                  return;
                                }

                                validFiles.push(file);
                              });

                              // 添加有效文件
                              if (validFiles.length > 0) {
                                setAttachments(prev => [...prev, ...validFiles]);
                              }

                              // 显示错误信息
                              if (invalidFiles.length > 0) {
                                alert(`以下文件无法上传：\n${invalidFiles.join('\n')}`);
                              }

                              // 重置input的value，确保可以重复选择相同文件
                              e.target.value = '';
                            }
                          }}
                          style={{ display: 'none' }}
                          id="file-upload"
                        />
                        <label htmlFor="file-upload" className="upload-btn">
                          +
                        </label>
                        <span className="file-format-info">
                          支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB
                        </span>
                      </div>
                      {attachments.length > 0 && (
                        <div className="attachments-list">
                          {attachments.map((file, index) => (
                            <div key={index} className="attachment-item">
                              <span className="file-name">{file.name}</span>
                              <button
                                onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                                className="remove-attachment-btn"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className="modal-footer">
            <button className="prev-btn" onClick={handlePrevious}>
              上一步
            </button>
            <button
              className={`next-btn ${
                (currentStep === 'template' && (
                  isEnergyOnlyMode
                    ? modalData.selectedDistribution
                    : isBlocksOnlyMode
                      ? modalData.selectedTemplate
                      : (modalData.selectedTemplate || modalData.selectedDistribution)
                )) ||
                (currentStep === 'publish' && taskData.taskName.trim() && !isPublishing)
                ? 'enabled' : 'disabled'
              } ${isPublishing ? 'publishing' : ''}`}
              onClick={currentStep === 'template' ? handleNext : handleConfirm}
              disabled={
                currentStep === 'template'
                  ? (isEnergyOnlyMode
                      ? !modalData.selectedDistribution
                      : isBlocksOnlyMode
                        ? !modalData.selectedTemplate
                        : (!modalData.selectedTemplate && !modalData.selectedDistribution))
                  : !taskData.taskName.trim() || isPublishing
              }
            >
              {currentStep === 'template' ? (
                (actionType === '发布任务' || actionType === '快速上课') ? '下一步' : '分配'
              ) : (
                isPublishing ? (
                  <span className="publishing-content">
                    <span className="spinner"></span>
                    发布中...
                  </span>
                ) : '开始上课'
              )}
            </button>
          </div>
        </div>
      </div>
      </div>

      {/* 模板选择弹窗 */}
      <TemplatePickerModal
        isOpen={isTemplatePickerOpen}
        onClose={handleTemplatePickerClose}
        onTemplateSelect={handleTemplateSelect}
      />

      {/* 批量兑换密钥模态框 */}
      <BatchUseKeyPackageModal
        open={isBatchUseKeyPackageModalOpen}
        selectedStudentIds={modalData.selectedStudents}
        students={students}
        onClose={handleBatchUseKeyPackageModalClose}
        onSuccess={handleBatchUseKeyPackageSuccess}
      />
    </div>
  );
};

export default TemplateSelectionModal;
