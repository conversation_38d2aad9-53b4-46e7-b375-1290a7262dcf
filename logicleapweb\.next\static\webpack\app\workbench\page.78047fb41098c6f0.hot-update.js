"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/ClassTasks.tsx":
/*!*************************************************!*\
  !*** ./app/workbench/components/ClassTasks.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronLeft,ChevronRight,Delete,Edit,Eye,Plus,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/delete.js\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ClassTasks.module.css */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.module.css\");\n/* harmony import */ var _ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=notification!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/notification/index.js\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.tsx\");\n/* harmony import */ var _teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../teacher-space/components/task-detail-modal */ \"(app-pages-browser)/./app/teacher-space/components/task-detail-modal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../teacher-space/components/modals/edit-task-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/edit-task-modal.tsx\");\n/* harmony import */ var _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/utils/task-event-manager */ \"(app-pages-browser)/./app/utils/task-event-manager.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* harmony import */ var _lib_utils_user__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/user */ \"(app-pages-browser)/./lib/utils/user.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ClassTasks = ()=>{\n    _s();\n    // 移除对模板上下文的依赖，只使用全局事件\n    // const { notifyTaskPublished } = useTemplate();\n    // 获取状态对应的CSS类名\n    const getStatusClassName = (status)=>{\n        switch(status){\n            case \"已完成\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().completed);\n            case \"进行中\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().inProgress);\n            case \"已结束\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().ended);\n            case \"未开始\":\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().notStarted);\n            default:\n                return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default()[\"default\"]);\n        }\n    };\n    // 数据状态\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 发布任务多步骤弹窗状态\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishTaskModalOpen, setIsPublishTaskModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishModalData, setPublishModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"\",\n        energyAmount: \"\",\n        selectedTemplate: {\n            id: 0\n        },\n        selectedStudents: []\n    });\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务详情弹窗状态\n    const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForDetail, setSelectedTaskForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 任务编辑弹窗状态\n    const [isEditTaskModalVisible, setIsEditTaskModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTaskForEdit, setSelectedTaskForEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 选择状态\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [allSchools, setAllSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有学校数据\n    const [allClasses, setAllClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // 保存所有班级数据\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"全部\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFilterExpanded, setIsFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSchoolFilterExpanded, setIsSchoolFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isClassFilterExpanded, setIsClassFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isStatusFilterExpanded, setIsStatusFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSidebarCollapsed, setIsSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 监听屏幕尺寸变化，大屏幕时自动展开筛选\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth >= 1024) {\n                setIsFilterExpanded(true);\n            }\n        };\n        handleResize(); // 初始化时检查\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    const statusOptions = [\n        \"全部\",\n        \"已完成\",\n        \"进行中\",\n        \"未开始\",\n        \"已结束\"\n    ];\n    // 获取用户关联的学校列表\n    const loadUserSchools = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n            setSchools(schoolsData);\n            setAllSchools(schoolsData); // 保存所有学校数据\n            // 默认不选择任何学校，显示\"全部\"状态\n            setSelectedSchool(null);\n            setSelectedClass(null);\n            // 加载所有学校的所有班级\n            await loadAllClasses(schoolsData);\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setError(error.message || \"网络连接失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载所有学校的所有班级\n    const loadAllClasses = async (schoolsData)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setAllClasses([]);\n                return;\n            }\n            let allClassesData = [];\n            // 遍历所有学校，获取每个学校的班级\n            for (const school of schoolsData){\n                try {\n                    const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(school.id, teacherId);\n                    allClassesData = [\n                        ...allClassesData,\n                        ...classesData\n                    ];\n                } catch (error) {\n                    console.error(\"获取学校 \".concat(school.schoolName, \" 的班级失败:\"), error);\n                }\n            }\n            setClasses(allClassesData);\n            setAllClasses(allClassesData);\n            console.log(\"加载所有班级完成，总数:\", allClassesData.length);\n        } catch (error) {\n            console.error(\"加载所有班级失败:\", error);\n            setClasses([]);\n            setAllClasses([]);\n        }\n    };\n    // 获取指定学校的班级列表 - 使用教师班级API\n    const loadSchoolClasses = async (schoolId)=>{\n        try {\n            const teacherId = (0,_lib_utils_user__WEBPACK_IMPORTED_MODULE_11__.getCurrentUserId)();\n            if (!teacherId) {\n                console.error(\"无法获取教师ID\");\n                setClasses([]);\n                setSelectedClass(null);\n                return;\n            }\n            const classesData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchTeacherClasses)(schoolId, teacherId);\n            setClasses(classesData);\n            // 默认选择第一个班级\n            if (classesData.length > 0) {\n                setSelectedClass(classesData[0]);\n                console.log(\"默认选择班级:\", classesData[0].className, \"(ID:\", classesData[0].id, \")\");\n            } else {\n                setSelectedClass(null);\n                setStudents([]);\n                console.log(\"没有班级数据，清空选择\");\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            setClasses([]);\n            setSelectedClass(null);\n        }\n    };\n    // 处理班级选择变化\n    const handleClassChange = (classItem)=>{\n        console.log(\"=== 切换班级 ===\");\n        console.log(\"从班级:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className, \"(ID:\", selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id, \")\");\n        console.log(\"切换到班级:\", (classItem === null || classItem === void 0 ? void 0 : classItem.className) || \"全部\", \"(ID:\", (classItem === null || classItem === void 0 ? void 0 : classItem.id) || \"all\", \")\");\n        // 如果点击的是当前已选中的班级，不需要切换\n        if ((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === (classItem === null || classItem === void 0 ? void 0 : classItem.id)) {\n            console.log(\"点击的是当前班级，无需切换\");\n            return;\n        }\n        // 更新选中的班级\n        setSelectedClass(classItem);\n        console.log(\"班级切换完成\");\n    };\n    // 处理学校选择变化\n    const handleSchoolChange = (school)=>{\n        console.log(\"=== 切换学校 ===\");\n        console.log(\"从学校:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName, \"(ID:\", selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id, \")\");\n        console.log(\"切换到学校:\", (school === null || school === void 0 ? void 0 : school.schoolName) || \"全部\", \"(ID:\", (school === null || school === void 0 ? void 0 : school.id) || \"all\", \")\");\n        setSelectedClass(null);\n        setSelectedSchool(school);\n        if (school === null) {\n            // 选择\"全部学校\"，显示所有班级\n            setClasses(allClasses);\n            console.log(\"选择全部学校，显示所有班级:\", allClasses.length);\n        } else {\n            // 选择特定学校，只显示该学校的班级\n            console.log(\"选择特定学校，开始获取该学校的班级...\");\n            loadSchoolClasses(school.id);\n        }\n    };\n    // 获取班级的真实任务数据 - 使用工具函数\n    const loadClassTasks = async (classId)=>{\n        try {\n            return await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchClassTasks)(classId);\n        } catch (error) {\n            console.error(\"获取班级任务数据失败:\", error);\n            return [];\n        }\n    };\n    // 组件挂载时获取数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUserSchools();\n    }, []);\n    // 监听selectedClass变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"selectedClass状态变化:\", selectedClass);\n    }, [\n        selectedClass\n    ]);\n    // 当选择的班级改变时，加载任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass) {\n            console.log(\"开始加载班级任务数据:\", selectedClass.className);\n            loadTasksData();\n        } else if (selectedClass === null && classes.length > 0) {\n            console.log(\"选择全部班级，加载所有班级任务数据\");\n            loadAllTasksData();\n        } else {\n            console.log(\"没有班级数据，清空任务数据\");\n            setTasks([]);\n        }\n    }, [\n        selectedClass\n    ]);\n    // 当班级列表变化且没有选中特定班级时，重新加载全部任务数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedClass === null && classes.length > 0) {\n            console.log(\"班级列表更新，重新加载全部班级任务数据\");\n            loadAllTasksData();\n        }\n    }, [\n        classes.length\n    ]); // 只监听班级数量变化，避免频繁重新渲染\n    // 获取任务数据（使用真实API）\n    const loadTasksData = async ()=>{\n        if (!selectedClass) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            const tasksData = await loadClassTasks(selectedClass.id);\n            setTasks(tasksData);\n        } catch (error) {\n            console.error(\"加载任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 获取所有班级的任务数据\n    const loadAllTasksData = async ()=>{\n        if (!classes || classes.length === 0) {\n            setTasks([]);\n            return;\n        }\n        try {\n            setLoading(true);\n            let allTasksData = [];\n            // 遍历所有班级，获取每个班级的任务\n            for (const classItem of classes){\n                try {\n                    const tasksData = await loadClassTasks(classItem.id);\n                    allTasksData = [\n                        ...allTasksData,\n                        ...tasksData\n                    ];\n                } catch (error) {\n                    console.error(\"获取班级 \".concat(classItem.className, \" 的任务失败:\"), error);\n                }\n            }\n            setTasks(allTasksData);\n            console.log(\"加载所有任务完成，总数:\", allTasksData.length);\n        } catch (error) {\n            console.error(\"加载所有任务数据失败:\", error);\n            setTasks([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 计算剩余时间\n    const calculateRemainingTime = (task)=>{\n        // 如果任务已完成或已结束，返回 \"-\"\n        if (task.status === \"已完成\" || task.status === \"已结束\") {\n            return \"-\";\n        }\n        // 解析截止时间\n        const deadline = new Date(task.deadline);\n        const now = new Date();\n        // 如果截止时间已过，返回 \"已超时\"\n        if (deadline < now) {\n            return \"已超时\";\n        }\n        // 计算剩余时间（毫秒）\n        const diffMs = deadline.getTime() - now.getTime();\n        // 计算剩余天数（不向上取整，精确计算）\n        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n        if (diffDays >= 1) {\n            // 剩余1天或以上，显示天数\n            return \"\".concat(diffDays, \"天\");\n        } else {\n            // 不足1天，显示小时数\n            const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));\n            if (diffHours <= 0) {\n                return \"已超时\";\n            }\n            return \"\".concat(diffHours, \"小时\");\n        }\n    };\n    // 获取剩余时间的样式类\n    const getRemainingTimeClass = (task)=>{\n        const remainingTime = calculateRemainingTime(task);\n        if (remainingTime === \"-\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished);\n        } else if (remainingTime === \"已超时\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue);\n        } else if (remainingTime.includes(\"小时\") || remainingTime === \"1天\") {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent);\n        } else {\n            return (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal);\n        }\n    };\n    // 根据筛选条件过滤任务数据\n    const getFilteredTasks = ()=>{\n        const filters = {\n            statusFilter,\n            startDate,\n            endDate,\n            searchQuery\n        };\n        return (0,_utils__WEBPACK_IMPORTED_MODULE_10__.filterTasks)(tasks, filters);\n    };\n    // 处理日期输入框点击事件\n    const handleDateInputClick = (event)=>{\n        var _input_showPicker;\n        const input = event.currentTarget;\n        (_input_showPicker = input.showPicker) === null || _input_showPicker === void 0 ? void 0 : _input_showPicker.call(input);\n    };\n    // 处理开始日期变化\n    const handleStartDateChange = (event)=>{\n        const newStartDate = event.target.value;\n        setStartDate(newStartDate);\n        // 如果开始日期晚于结束日期，清空结束日期\n        if (endDate && newStartDate > endDate) {\n            setEndDate(\"\");\n        }\n    };\n    // 处理结束日期变化\n    const handleEndDateChange = (event)=>{\n        const newEndDate = event.target.value;\n        // 如果结束日期早于开始日期，不允许设置\n        if (startDate && newEndDate < startDate) {\n            alert(\"结束日期不能早于开始日期\");\n            return;\n        }\n        setEndDate(newEndDate);\n    };\n    // 处理刷新列表\n    const handleRefreshList = async ()=>{\n        try {\n            setRefreshing(true);\n            // 如果没有选择学校，重新获取学校数据\n            if (!selectedSchool) {\n                console.log(\"没有选择学校，重新获取学校数据...\");\n                await loadUserSchools();\n                return;\n            }\n            // 如果没有选择班级，重新获取班级数据\n            if (!selectedClass) {\n                console.log(\"没有选择班级，重新获取班级数据...\");\n                await loadSchoolClasses(selectedSchool.id);\n                return;\n            }\n            // 重新获取当前班级的任务数据\n            console.log(\"重新获取班级任务数据...\");\n            await loadTasksData();\n            // 显示刷新成功的提示\n            setTimeout(()=>{\n                getFilteredTasks();\n            }, 100);\n        } catch (error) {\n            console.error(\"❌ 刷新任务列表失败:\", error);\n        // 这里可以添加错误提示给用户\n        } finally{\n            setRefreshing(false);\n        }\n    };\n    // 重置筛选条件\n    const handleResetFilters = ()=>{\n        setStartDate(\"\");\n        setEndDate(\"\");\n        setStatusFilter(\"全部\");\n        setSearchQuery(\"\");\n        console.log(\"已重置所有筛选条件\");\n    };\n    // 处理发布任务按钮点击 - 启动多步骤流程\n    const handlePublishTaskClick = async ()=>{\n        // 如果已经选择了学校和班级，直接跳到模板选择步骤\n        if (selectedSchool && selectedClass) {\n            setModalSelectedSchool(selectedSchool);\n            setIsTemplateModalOpen(true);\n        } else {\n            try {\n                // 获取用户的学校列表\n                const schoolsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.fetchUserSchools)();\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            } catch (error) {\n                console.error(\"获取学校列表失败:\", error);\n                // 出错时仍然显示学校选择弹窗\n                setIsSchoolModalOpen(true);\n            }\n        }\n    };\n    // 处理学校选择完成\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    // 处理班级选择完成\n    const handleClassSelect = ()=>{\n        setIsClassModalOpen(false);\n        // 发布任务直接跳过模板选择，进入发布任务弹窗\n        setIsPublishTaskModalOpen(true);\n    };\n    // 处理模板选择完成\n    const handleTemplateSelect = (modalData)=>{\n        setPublishModalData(modalData);\n        setIsTemplateModalOpen(false);\n        setIsPublishTaskModalOpen(true);\n    };\n    // 处理最终发布任务确认 - 使用工具函数重构\n    const handlePublishTaskConfirm = async (taskData)=>{\n        try {\n            var _publishModalData_selectedTemplate;\n            console.log(\"发布任务数据:\", taskData);\n            console.log(\"模板数据:\", publishModalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const userId = userData ? JSON.parse(userData).userId : 0;\n            // 准备发布参数\n            const values = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskContent: taskData.taskDescription || \"\",\n                startDate: new Date(),\n                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n                selfAssessmentItems: taskData.selfAssessmentItems || [],\n                allowLateSubmission: true,\n                templateId: ((_publishModalData_selectedTemplate = publishModalData.selectedTemplate) === null || _publishModalData_selectedTemplate === void 0 ? void 0 : _publishModalData_selectedTemplate.id) || 0,\n                lateSubmissionPolicy: {\n                    deductionPerDay: 5,\n                    maxDeductionDays: 7,\n                    minScore: 0\n                }\n            };\n            const selectedStudentIds = publishModalData.selectedStudents.length > 0 ? publishModalData.selectedStudents : students.map((s)=>s.id);\n            // 调用工具函数发布任务\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.publishTask)(values, selectedClass, userId, selectedStudentIds, students, [] // 附件列表\n            );\n            if (success) {\n                // 关闭所有弹窗并重置状态\n                setIsPublishTaskModalOpen(false);\n                setPublishModalData({\n                    selectedDistribution: \"\",\n                    energyAmount: \"\",\n                    selectedTemplate: {\n                        id: 0\n                    },\n                    selectedStudents: []\n                });\n                // 刷新任务列表\n                await loadTasksData();\n                // 触发全局任务发布事件\n                const eventData = {\n                    taskId: 0,\n                    taskName: taskData.taskName,\n                    classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                    className: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className,\n                    teacherId: userId\n                };\n                _app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__[\"default\"].emit(_app_utils_task_event_manager__WEBPACK_IMPORTED_MODULE_9__.TASK_EVENTS.TASK_PUBLISHED, eventData);\n            }\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            _barrel_optimize_names_notification_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].error({\n                message: \"任务发布失败\",\n                description: \"网络错误或服务器异常，请稍后重试\"\n            });\n        }\n    };\n    // 处理任务查看\n    const handleTaskView = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleViewTask)(task);\n            setSelectedTaskForDetail(taskDetail);\n            setIsTaskDetailModalVisible(true);\n        } catch (error) {\n            console.error(\"查看任务失败:\", error);\n            setSelectedTaskForDetail(task);\n            setIsTaskDetailModalVisible(true);\n        }\n    };\n    // 处理任务编辑\n    const handleTaskEdit = async (task)=>{\n        try {\n            const taskDetail = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleEditTask)(task);\n            setSelectedTaskForEdit(taskDetail);\n            setIsEditTaskModalVisible(true);\n        } catch (error) {\n            console.error(\"编辑任务失败:\", error);\n            setSelectedTaskForEdit(task);\n            setIsEditTaskModalVisible(true);\n        }\n    };\n    // 处理任务删除\n    const handleTaskDelete = async (task)=>{\n        const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_10__.handleDeleteTask)(task, loadTasksData);\n        if (success) {\n            console.log(\"任务删除成功\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().classTasksContainer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainLayout),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().leftSidebar), \" \").concat(isSidebarCollapsed ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed) : \"\"),\n                    children: isSidebarCollapsed ? /* 收缩状态：显示白色长方形条 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsedSidebar),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expandBtn),\n                            onClick: ()=>setIsSidebarCollapsed(false),\n                            title: \"展开筛选\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarHeader),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().sidebarTitle),\n                                        children: \"筛选\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                        onClick: ()=>setIsSidebarCollapsed(true),\n                                        title: \"收缩筛选\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterContent), \" \").concat(isFilterExpanded ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().expanded) : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedSchool ? selectedSchool.schoolName : \"全部学校\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsSchoolFilterExpanded(!isSchoolFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isSchoolFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isSchoolFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"加载中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 21\n                                                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#ff4d4f\"\n                                                    },\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, undefined) : !Array.isArray(schools) || schools.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"暂无数据\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedSchool === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                            onClick: ()=>handleSchoolChange(null),\n                                                            title: \"全部学校\",\n                                                            children: \"全部学校\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>handleSchoolChange(school),\n                                                                title: school.schoolName,\n                                                                children: school.schoolName\n                                                            }, school.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: selectedClass ? selectedClass.className : \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsClassFilterExpanded(!isClassFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isClassFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 740,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isClassFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: [\n                                                    (()=>{\n                                                        return null;\n                                                    })(),\n                                                    !Array.isArray(classes) || classes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"暂无班级数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(selectedClass === null ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                onClick: ()=>{\n                                                                    console.log(\"全部班级按钮被点击\");\n                                                                    handleClassChange(null);\n                                                                },\n                                                                title: \"全部班级\",\n                                                                children: \"全部班级\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            classes.map((classItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) === classItem.id ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                                    onClick: ()=>{\n                                                                        console.log(\"班级按钮被点击:\", classItem.className);\n                                                                        handleClassChange(classItem);\n                                                                    },\n                                                                    title: classItem.className,\n                                                                    children: classItem.className\n                                                                }, classItem.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelRow),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabelContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                                children: \"所有类型\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().selectedHint),\n                                                                children: statusFilter\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 790,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseBtn),\n                                                        onClick: ()=>setIsStatusFilterExpanded(!isStatusFilterExpanded),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapseIcon), \" \").concat(isStatusFilterExpanded ? \"\" : (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().collapsed))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isStatusFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTabs),\n                                                children: statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterTab), \" \").concat(statusFilter === status ? (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : \"\"),\n                                                        onClick: ()=>setStatusFilter(status),\n                                                        title: status,\n                                                        children: status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterRow),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().filterLabel),\n                                                children: \"日期范围\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateRange),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: startDate,\n                                                                onChange: handleStartDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: \"\",\n                                                                title: \"开始日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !startDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"开始\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 36\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateSeparator),\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInputContainer),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: endDate,\n                                                                onChange: handleEndDateChange,\n                                                                onClick: handleDateInputClick,\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().dateInput),\n                                                                min: startDate || \"\",\n                                                                title: \"结束日期\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !endDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().customPlaceholder),\n                                                                children: \"结束\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 34\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    (startDate || endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().clearDateBtn),\n                                                        onClick: ()=>{\n                                                            setStartDate(\"\");\n                                                            setEndDate(\"\");\n                                                        },\n                                                        title: \"清除日期筛选\",\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().rightContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchBox),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: 18,\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchIcon)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索任务名称、任务内容...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().searchInput)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().publishTaskBtn),\n                                    onClick: handlePublishTaskClick,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"发布任务\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tasksTable),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableScrollContainer),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableHeader),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"任务名称\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"状态\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"剩余时间\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"完成率\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().headerCell),\n                                                    children: \"操作\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableBody),\n                                            children: getFilteredTasks().length > 0 ? getFilteredTasks().map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableRow),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: task.taskName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().statusBadge), \" \").concat(getStatusClassName(task.status)),\n                                                                children: task.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: (()=>{\n                                                                const remainingTime = calculateRemainingTime(task);\n                                                                let className = (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTime);\n                                                                if (remainingTime === \"-\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeFinished));\n                                                                } else if (remainingTime === \"已超时\") {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeOverdue));\n                                                                } else if (remainingTime.includes(\"小时\")) {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeUrgent));\n                                                                } else {\n                                                                    className += \" \".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().remainingTimeNormal));\n                                                                }\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: className,\n                                                                    children: remainingTime\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 23\n                                                                }, undefined);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: [\n                                                                task.completionRate,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 17\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().tableCell),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actions),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().viewBtn)),\n                                                                        onClick: ()=>handleTaskView(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 946,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"查看\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 942,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().editBtn)),\n                                                                        onClick: ()=>handleTaskEdit(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 953,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"编辑\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 949,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"\".concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionBtn), \" \").concat((_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().deleteBtn)),\n                                                                        onClick: ()=>handleTaskDelete(task),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronLeft_ChevronRight_Delete_Edit_Eye_Plus_Search_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 23\n                                                                            }, undefined),\n                                                                            \"删除\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    ]\n                                                }, task.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 15\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyState),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyIcon),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"64\",\n                                                            height: \"64\",\n                                                            viewBox: \"0 0 64 64\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"32\",\n                                                                    cy: \"32\",\n                                                                    r: \"30\",\n                                                                    stroke: \"#E5E7EB\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"#F9FAFB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M24 28h16M24 32h12M24 36h8\",\n                                                                    stroke: \"#9CA3AF\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"44\",\n                                                                    cy: \"20\",\n                                                                    r: \"8\",\n                                                                    fill: \"#FEF3C7\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 973,\n                                                                    columnNumber: 19\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M41 20h6M44 17v6\",\n                                                                    stroke: \"#F59E0B\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                                    lineNumber: 974,\n                                                                    columnNumber: 19\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 17\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyTitle),\n                                                        children: \"暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_ClassTasks_module_css__WEBPACK_IMPORTED_MODULE_2___default().emptyDescription),\n                                                        children: classes.length === 0 ? \"暂无班级数据\" : selectedClass === null ? \"当前显示所有班级的任务数据\" : \"当前班级暂无任务数据\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                                lineNumber: 894,\n                                columnNumber: 9\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            isOpen: isSchoolModalOpen,\n                            onClose: ()=>setIsSchoolModalOpen(false),\n                            onSchoolSelect: handleSchoolSelect,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 990,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            isOpen: isClassModalOpen,\n                            onClose: ()=>setIsClassModalOpen(false),\n                            onBack: ()=>{\n                                setIsClassModalOpen(false);\n                                setIsSchoolModalOpen(true);\n                            },\n                            onClassSelect: handleClassSelect,\n                            selectedSchool: modalSelectedSchool,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 997,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            isOpen: isTemplateModalOpen,\n                            onClose: ()=>setIsTemplateModalOpen(false),\n                            onBack: ()=>{\n                                setIsTemplateModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            selectedSchool: modalSelectedSchool,\n                            selectedClass: selectedClass,\n                            actionType: \"发布任务\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1009,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewPublishTaskModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isOpen: isPublishTaskModalOpen,\n                            onClose: ()=>setIsPublishTaskModalOpen(false),\n                            onBack: ()=>{\n                                setIsPublishTaskModalOpen(false);\n                                setIsClassModalOpen(true);\n                            },\n                            onConfirm: handlePublishTaskConfirm,\n                            modalData: publishModalData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_task_detail_modal__WEBPACK_IMPORTED_MODULE_7__.TaskDetailModal, {\n                            task: selectedTaskForDetail,\n                            visible: isTaskDetailModalVisible,\n                            onClose: ()=>{\n                                setIsTaskDetailModalVisible(false);\n                                setSelectedTaskForDetail(null);\n                            },\n                            students: students.map((student)=>({\n                                    id: student.id,\n                                    userId: student.id,\n                                    studentNumber: \"\".concat(student.id),\n                                    nickName: student.name,\n                                    avatarUrl: \"\",\n                                    availablePoints: 0,\n                                    totalPoints: 0,\n                                    classId: student.classId,\n                                    className: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.className) || \"\",\n                                    schoolId: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) || 0,\n                                    schoolName: (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.schoolName) || \"\"\n                                })),\n                            onRefresh: loadTasksData,\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_edit_task_modal__WEBPACK_IMPORTED_MODULE_8__.EditTaskModal, {\n                            visible: isEditTaskModalVisible,\n                            task: selectedTaskForEdit,\n                            onClose: ()=>{\n                                setIsEditTaskModalVisible(false);\n                                setSelectedTaskForEdit(null);\n                            },\n                            onSuccess: ()=>{\n                                // 编辑成功后刷新任务列表\n                                loadTasksData();\n                            },\n                            currentClassId: (selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n                    lineNumber: 870,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n            lineNumber: 641,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\ClassTasks.tsx\",\n        lineNumber: 639,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassTasks, \"mULc1hhR6Iaw9KD3Igc769HEwBc=\");\n_c = ClassTasks;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClassTasks);\nvar _c;\n$RefreshReg$(_c, \"ClassTasks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\n"));

/***/ })

});