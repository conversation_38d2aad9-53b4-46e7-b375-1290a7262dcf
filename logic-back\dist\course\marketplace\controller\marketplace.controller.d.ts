import { MarketplaceService } from '../service/marketplace.service';
import { HttpResponseResultService } from '../../../web/http_response_result/http_response_result.service';
import { GetSeriesListQueryDto } from '../dto/series-list.dto';
import { GetTagsListQueryDto } from '../dto/tags-list.dto';
import { CreateTagDto, UpdateTagDto } from '../dto/tag-management.dto';
export declare class MarketplaceController {
    private readonly marketplaceService;
    private readonly httpResponseResultService;
    constructor(marketplaceService: MarketplaceService, httpResponseResultService: HttpResponseResultService);
    getSeriesList(query: GetSeriesListQueryDto): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/series-list.dto").SeriesListDataDto>>;
    getSeriesDetail(seriesId: number): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null> | import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/series-detail.dto").SeriesDetailDataDto>>;
    getCourseDetail(seriesId: number, courseId: number): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null> | import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/course-detail.dto").CourseDetailDataDto>>;
    getTagsList(query: GetTagsListQueryDto): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/tags-list.dto").TagsListDataDto>>;
    createTag(createTagDto: CreateTagDto): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null> | import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/tag-management.dto").TagDetailDataDto>>;
    updateTag(id: number, updateTagDto: UpdateTagDto): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null> | import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/tag-management.dto").TagDetailDataDto>>;
    deleteTag(id: number): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null>>;
    getTagById(id: number): Promise<import("../../../web/http_response_result/http-response.interface").HttpResponse<null> | import("../../../web/http_response_result/http-response.interface").HttpResponse<import("../dto/tag-management.dto").TagDetailDataDto>>;
}
