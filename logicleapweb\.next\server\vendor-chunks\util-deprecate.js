/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/util-deprecate";
exports.ids = ["vendor-chunks/util-deprecate"];
exports.modules = {

/***/ "(ssr)/./node_modules/util-deprecate/node.js":
/*!*********************************************!*\
  !*** ./node_modules/util-deprecate/node.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */\n\nmodule.exports = __webpack_require__(/*! util */ \"util\").deprecate;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXRpbC1kZXByZWNhdGUvbm9kZS5qcyIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtFQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvZ2ljbGVhcHdlYi8uL25vZGVfbW9kdWxlcy91dGlsLWRlcHJlY2F0ZS9ub2RlLmpzPzYxNTciXSwic291cmNlc0NvbnRlbnQiOlsiXG4vKipcbiAqIEZvciBOb2RlLmpzLCBzaW1wbHkgcmUtZXhwb3J0IHRoZSBjb3JlIGB1dGlsLmRlcHJlY2F0ZWAgZnVuY3Rpb24uXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCd1dGlsJykuZGVwcmVjYXRlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/util-deprecate/node.js\n");

/***/ })

};
;