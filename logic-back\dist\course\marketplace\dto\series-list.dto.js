"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeriesListResponseDto = exports.SeriesListDataDto = exports.FilterStatsDto = exports.PaginationDto = exports.SeriesInfoDto = exports.ContentSummaryDto = exports.TagInfoDto = exports.GetSeriesListQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class GetSeriesListQueryDto {
    page = 1;
    pageSize = 10;
    category;
    status;
    tagIds;
    keyword;
    hasVideo;
    hasDocument;
    hasAudio;
    sortBy = 'latest';
}
exports.GetSeriesListQueryDto = GetSeriesListQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码，默认1', example: 1, minimum: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量，默认10，最大50', example: 10, minimum: 1, maximum: 50 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '分类筛选：0=官方，1=社区', example: 0, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '状态筛选：0=草稿，1=已发布，2=已归档', example: 1, enum: [0, 1, 2] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1, 2]),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '标签ID，多个用逗号分隔', example: '1,2,3' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetSeriesListQueryDto.prototype, "tagIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '搜索关键词', example: 'Node' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetSeriesListQueryDto.prototype, "keyword", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否包含视频：0=否，1=是', example: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否包含文档：0=否，1=是', example: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '是否包含音频：0=否，1=是', example: 1, enum: [0, 1] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsIn)([0, 1]),
    __metadata("design:type", Number)
], GetSeriesListQueryDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '排序方式：latest=最新，popular=热门，duration=时长', example: 'latest', enum: ['latest', 'popular', 'duration'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['latest', 'popular', 'duration']),
    __metadata("design:type", String)
], GetSeriesListQueryDto.prototype, "sortBy", void 0);
class TagInfoDto {
    id;
    name;
    color;
    category;
    categoryLabel;
}
exports.TagInfoDto = TagInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签ID', example: 1 }),
    __metadata("design:type", Number)
], TagInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签名称', example: '编程' }),
    __metadata("design:type", String)
], TagInfoDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签颜色', example: '#007bff' }),
    __metadata("design:type", String)
], TagInfoDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类：0=难度，1=类型，2=特色，3=其他', example: 1 }),
    __metadata("design:type", Number)
], TagInfoDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签分类标签', example: '类型' }),
    __metadata("design:type", String)
], TagInfoDto.prototype, "categoryLabel", void 0);
class ContentSummaryDto {
    hasVideo;
    hasDocument;
    hasAudio;
    videoCourseCount;
    documentCourseCount;
    averageVideoDuration;
    totalResourcesCount;
}
exports.ContentSummaryDto = ContentSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含视频', example: true }),
    __metadata("design:type", Boolean)
], ContentSummaryDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含文档', example: true }),
    __metadata("design:type", Boolean)
], ContentSummaryDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含音频', example: false }),
    __metadata("design:type", Boolean)
], ContentSummaryDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频课程数量', example: 4 }),
    __metadata("design:type", Number)
], ContentSummaryDto.prototype, "videoCourseCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '文档课程数量', example: 5 }),
    __metadata("design:type", Number)
], ContentSummaryDto.prototype, "documentCourseCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '平均视频时长(秒)', example: 1800 }),
    __metadata("design:type", Number)
], ContentSummaryDto.prototype, "averageVideoDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总资源数量1', example: 15 }),
    __metadata("design:type", Number)
], ContentSummaryDto.prototype, "totalResourcesCount", void 0);
class SeriesInfoDto {
    id;
    title;
    description;
    coverImage;
    category;
    categoryLabel;
    status;
    statusLabel;
    projectMembers;
    totalCourses;
    totalStudents;
    contentSummary;
    createdAt;
    tags;
}
exports.SeriesInfoDto = SeriesInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列ID', example: 1 }),
    __metadata("design:type", Number)
], SeriesInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列标题', example: 'Node' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列描述', example: 'Node' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '封面图片URL', example: 'https://example.com/cover1.jpg' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类：0=官方，1=社区', example: 0 }),
    __metadata("design:type", Number)
], SeriesInfoDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类标签', example: '官方' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "categoryLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0=草稿，1=已发布，2=已归档', example: 1 }),
    __metadata("design:type", Number)
], SeriesInfoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态标签', example: '已发布' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '项目成员', example: '张老师、李助教' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "projectMembers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程总数', example: 5 }),
    __metadata("design:type", Number)
], SeriesInfoDto.prototype, "totalCourses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '学生总数', example: 1200 }),
    __metadata("design:type", Number)
], SeriesInfoDto.prototype, "totalStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '内容统计信息', type: ContentSummaryDto }),
    __metadata("design:type", ContentSummaryDto)
], SeriesInfoDto.prototype, "contentSummary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-15T10:30:00Z' }),
    __metadata("design:type", String)
], SeriesInfoDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签列表', type: [TagInfoDto] }),
    __metadata("design:type", Array)
], SeriesInfoDto.prototype, "tags", void 0);
class PaginationDto {
    page;
    pageSize;
    total;
    totalPages;
    hasNext;
    hasPrev;
}
exports.PaginationDto = PaginationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码', example: 1 }),
    __metadata("design:type", Number)
], PaginationDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], PaginationDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总记录数', example: 25 }),
    __metadata("design:type", Number)
], PaginationDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 3 }),
    __metadata("design:type", Number)
], PaginationDto.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有下一页', example: true }),
    __metadata("design:type", Boolean)
], PaginationDto.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否有上一页', example: false }),
    __metadata("design:type", Boolean)
], PaginationDto.prototype, "hasPrev", void 0);
class FilterStatsDto {
    totalSeries;
    officialCount;
    communityCount;
    videoSeriesCount;
    documentSeriesCount;
}
exports.FilterStatsDto = FilterStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列总数', example: 25 }),
    __metadata("design:type", Number)
], FilterStatsDto.prototype, "totalSeries", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '官方系列数量', example: 18 }),
    __metadata("design:type", Number)
], FilterStatsDto.prototype, "officialCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '社区系列数量', example: 7 }),
    __metadata("design:type", Number)
], FilterStatsDto.prototype, "communityCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '包含视频的系列数量', example: 20 }),
    __metadata("design:type", Number)
], FilterStatsDto.prototype, "videoSeriesCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '包含文档的系列数量', example: 22 }),
    __metadata("design:type", Number)
], FilterStatsDto.prototype, "documentSeriesCount", void 0);
class SeriesListDataDto {
    list;
    pagination;
    filterStats;
}
exports.SeriesListDataDto = SeriesListDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '系列课程列表', type: [SeriesInfoDto] }),
    __metadata("design:type", Array)
], SeriesListDataDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分页信息', type: PaginationDto }),
    __metadata("design:type", PaginationDto)
], SeriesListDataDto.prototype, "pagination", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '筛选统计信息', type: FilterStatsDto }),
    __metadata("design:type", FilterStatsDto)
], SeriesListDataDto.prototype, "filterStats", void 0);
class SeriesListResponseDto {
    code;
    message;
    data;
}
exports.SeriesListResponseDto = SeriesListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], SeriesListResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], SeriesListResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应数据', type: () => SeriesListDataDto }),
    __metadata("design:type", SeriesListDataDto)
], SeriesListResponseDto.prototype, "data", void 0);
//# sourceMappingURL=series-list.dto.js.map