"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSeriesResponseDto = exports.UpdateCourseSeriesDto = exports.CreateCourseSeriesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCourseSeriesDto {
    title;
    description;
    coverImage;
    category;
    projectMembers;
    tagIds;
}
exports.CreateCourseSeriesDto = CreateCourseSeriesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系列名称',
        example: 'Node.js后端开发系列'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '系列名称不能为空' }),
    __metadata("design:type", String)
], CreateCourseSeriesDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系列介绍',
        example: '从基础到进阶的Node.js后端开发课程'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCourseSeriesDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系列封面图片URL',
        example: 'https://example.com/nodejs-cover.jpg'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCourseSeriesDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '分类：0=官方，1=社区',
        example: 0,
        default: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateCourseSeriesDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '项目成员名单',
        example: '王老师、李助教、张同学'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCourseSeriesDto.prototype, "projectMembers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '标签ID列表',
        example: [1, 2, 3]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateCourseSeriesDto.prototype, "tagIds", void 0);
class UpdateCourseSeriesDto {
    title;
    description;
    coverImage;
    category;
    projectMembers;
    tagIds;
}
exports.UpdateCourseSeriesDto = UpdateCourseSeriesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系列名称',
        example: 'Node.js后端开发进阶系列'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCourseSeriesDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系列介绍',
        example: '这是更新后的Node.js后端开发进阶系列课程，涵盖高级主题'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCourseSeriesDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '系列封面图片URL',
        example: 'https://example.com/nodejs-advanced-cover.jpg'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCourseSeriesDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '分类：0=官方，1=社区',
        example: 0
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateCourseSeriesDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '项目成员名单',
        example: '王老师、李助教、张同学、新成员'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateCourseSeriesDto.prototype, "projectMembers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '标签ID列表',
        example: [1, 2, 3]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateCourseSeriesDto.prototype, "tagIds", void 0);
class CourseSeriesResponseDto {
    id;
    title;
    description;
    coverImage;
    category;
    status;
    projectMembers;
    totalCourses;
    totalStudents;
    creatorId;
    createdAt;
    updatedAt;
}
exports.CourseSeriesResponseDto = CourseSeriesResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系列ID',
        example: 1
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系列名称',
        example: 'Node.js后端开发系列'
    }),
    __metadata("design:type", String)
], CourseSeriesResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系列介绍',
        nullable: true,
        example: '从基础到进阶的Node.js后端开发课程'
    }),
    __metadata("design:type", String)
], CourseSeriesResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '系列封面图片URL',
        nullable: true,
        example: 'https://example.com/nodejs-cover.jpg'
    }),
    __metadata("design:type", String)
], CourseSeriesResponseDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类：0=官方，1=社区',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态：0=草稿，1=已发布，2=已归档',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '项目成员名单',
        nullable: true,
        example: '王老师、李助教、张同学'
    }),
    __metadata("design:type", String)
], CourseSeriesResponseDto.prototype, "projectMembers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '包含课程数量',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "totalCourses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '总学习人数',
        example: 0
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "totalStudents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建者ID',
        nullable: true,
        example: 123
    }),
    __metadata("design:type", Number)
], CourseSeriesResponseDto.prototype, "creatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建时间',
        example: '2023-07-15T08:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseSeriesResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '更新时间',
        example: '2023-07-15T08:30:00Z'
    }),
    __metadata("design:type", Date)
], CourseSeriesResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=course-series.dto.js.map