/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.\!visible {
  visibility: visible !important;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.-inset-0\.5 {
  inset: -0.125rem;
}
.inset-0 {
  inset: 0px;
}
.inset-\[-200px\] {
  inset: -200px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.\!bottom-2 {
  bottom: 0.5rem !important;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-bottom-12 {
  bottom: -3rem;
}
.-bottom-20 {
  bottom: -5rem;
}
.-bottom-5 {
  bottom: -1.25rem;
}
.-bottom-6 {
  bottom: -1.5rem;
}
.-left-10 {
  left: -2.5rem;
}
.-left-20 {
  left: -5rem;
}
.-left-4 {
  left: -1rem;
}
.-right-1 {
  right: -0.25rem;
}
.-right-10 {
  right: -2.5rem;
}
.-right-2 {
  right: -0.5rem;
}
.-right-20 {
  right: -5rem;
}
.-right-5 {
  right: -1.25rem;
}
.-right-6 {
  right: -1.5rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-10 {
  top: -2.5rem;
}
.-top-12 {
  top: -3rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-20 {
  top: -5rem;
}
.-top-3 {
  top: -0.75rem;
}
.-top-4 {
  top: -1rem;
}
.-top-8 {
  top: -2rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-16 {
  bottom: 4rem;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-5 {
  bottom: 1.25rem;
}
.bottom-\[-100px\] {
  bottom: -100px;
}
.bottom-\[-1px\] {
  bottom: -1px;
}
.bottom-\[15vh\] {
  bottom: 15vh;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-8 {
  left: 2rem;
}
.left-\[-150px\] {
  left: -150px;
}
.left-\[50\%\] {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 1.25rem;
}
.right-6 {
  right: 1.5rem;
}
.right-7 {
  right: 1.75rem;
}
.right-8 {
  right: 2rem;
}
.right-\[-200px\] {
  right: -200px;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-14 {
  top: 3.5rem;
}
.top-2 {
  top: 0.5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-6 {
  top: 1.5rem;
}
.top-8 {
  top: 2rem;
}
.top-\[-150px\] {
  top: -150px;
}
.top-\[50\%\] {
  top: 50%;
}
.top-full {
  top: 100%;
}
.\!z-\[1\] {
  z-index: 1 !important;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[-1\] {
  z-index: -1;
}
.z-\[1000\] {
  z-index: 1000;
}
.z-\[1001\] {
  z-index: 1001;
}
.z-\[100\] {
  z-index: 100;
}
.z-\[200\] {
  z-index: 200;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[999\] {
  z-index: 999;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-12 {
  grid-column: span 12 / span 12;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-4 {
  grid-column: span 4 / span 4;
}
.col-span-6 {
  grid-column: span 6 / span 6;
}
.col-span-full {
  grid-column: 1 / -1;
}
.m-0 {
  margin: 0px;
}
.m-4 {
  margin: 1rem;
}
.m-auto {
  margin: auto;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-1\.5 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.\!mb-0 {
  margin-bottom: 0px !important;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-24 {
  margin-bottom: 6rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-2\.5 {
  margin-right: 0.625rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-2\.5 {
  margin-top: 0.625rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.\!block {
  display: block !important;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.\!flex {
  display: flex !important;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[3\/2\] {
  aspect-ratio: 3/2;
}
.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-28 {
  height: 7rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-44 {
  height: 11rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[100px\] {
  height: 100px;
}
.h-\[120px\] {
  height: 120px;
}
.h-\[160px\] {
  height: 160px;
}
.h-\[180px\] {
  height: 180px;
}
.h-\[1em\] {
  height: 1em;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[250px\] {
  height: 250px;
}
.h-\[2px\] {
  height: 2px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[33px\] {
  height: 33px;
}
.h-\[400px\] {
  height: 400px;
}
.h-\[500px\] {
  height: 500px;
}
.h-\[550px\] {
  height: 550px;
}
.h-\[56px\] {
  height: 56px;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[70\%\] {
  height: 70%;
}
.h-\[700px\] {
  height: 700px;
}
.h-\[75vh\] {
  height: 75vh;
}
.h-\[80vh\] {
  height: 80vh;
}
.h-\[calc\(100\%-40px\)\] {
  height: calc(100% - 40px);
}
.h-\[calc\(100\%-64px\)\] {
  height: calc(100% - 64px);
}
.h-\[calc\(100vh-160px\)\] {
  height: calc(100vh - 160px);
}
.h-\[calc\(100vh-180px\)\] {
  height: calc(100vh - 180px);
}
.h-\[calc\(100vh-240px\)\] {
  height: calc(100vh - 240px);
}
.h-\[calc\(100vh-320px\)\] {
  height: calc(100vh - 320px);
}
.h-\[calc\(90vh-5rem\)\] {
  height: calc(90vh - 5rem);
}
.h-\[calc\(96vh-350px\)\] {
  height: calc(96vh - 350px);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.max-h-36 {
  max-height: 9rem;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[200px\] {
  max-height: 200px;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[400px\] {
  max-height: 400px;
}
.max-h-\[40px\] {
  max-height: 40px;
}
.max-h-\[40rem\] {
  max-height: 40rem;
}
.max-h-\[600px\] {
  max-height: 600px;
}
.max-h-\[700px\] {
  max-height: 700px;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[85vh\] {
  max-height: 85vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-\[calc\(100vh-320px\)\] {
  max-height: calc(100vh - 320px);
}
.min-h-0 {
  min-height: 0px;
}
.min-h-4 {
  min-height: 1rem;
}
.min-h-\[2\.5em\] {
  min-height: 2.5em;
}
.min-h-\[2\.5rem\] {
  min-height: 2.5rem;
}
.min-h-\[200px\] {
  min-height: 200px;
}
.min-h-\[250px\] {
  min-height: 250px;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[3rem\] {
  min-height: 3rem;
}
.min-h-\[40px\] {
  min-height: 40px;
}
.min-h-\[500px\] {
  min-height: 500px;
}
.min-h-\[510px\] {
  min-height: 510px;
}
.min-h-\[60px\] {
  min-height: 60px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-\[calc\(100vh-240px\)\] {
  min-height: calc(100vh - 240px);
}
.min-h-\[calc\(100vh-320px\)\] {
  min-height: calc(100vh - 320px);
}
.min-h-\[h-auto\] {
  min-height: h-auto;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-2\/5 {
  width: 40%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-44 {
  width: 11rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[100px\] {
  width: 100px;
}
.w-\[108px\] {
  width: 108px;
}
.w-\[128px\] {
  width: 128px;
}
.w-\[180px\] {
  width: 180px;
}
.w-\[1px\] {
  width: 1px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[240px\] {
  width: 240px;
}
.w-\[250px\] {
  width: 250px;
}
.w-\[260px\] {
  width: 260px;
}
.w-\[280px\] {
  width: 280px;
}
.w-\[2px\] {
  width: 2px;
}
.w-\[30\%\] {
  width: 30%;
}
.w-\[320px\] {
  width: 320px;
}
.w-\[35\%\] {
  width: 35%;
}
.w-\[400px\] {
  width: 400px;
}
.w-\[500px\] {
  width: 500px;
}
.w-\[520px\] {
  width: 520px;
}
.w-\[600px\] {
  width: 600px;
}
.w-\[65\%\] {
  width: 65%;
}
.w-\[70\%\] {
  width: 70%;
}
.w-\[700px\] {
  width: 700px;
}
.w-\[80\%\] {
  width: 80%;
}
.w-\[800px\] {
  width: 800px;
}
.w-\[90\%\] {
  width: 90%;
}
.w-\[900px\] {
  width: 900px;
}
.w-\[90px\] {
  width: 90px;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.\!min-w-\[100px\] {
  min-width: 100px !important;
}
.\!min-w-\[70px\] {
  min-width: 70px !important;
}
.\!min-w-\[80px\] {
  min-width: 80px !important;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-7 {
  min-width: 1.75rem;
}
.min-w-\[100px\] {
  min-width: 100px;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[180px\] {
  min-width: 180px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.min-w-\[250px\] {
  min-width: 250px;
}
.min-w-\[260px\] {
  min-width: 260px;
}
.min-w-\[300px\] {
  min-width: 300px;
}
.min-w-\[72px\] {
  min-width: 72px;
}
.min-w-\[80px\] {
  min-width: 80px;
}
.min-w-\[900px\] {
  min-width: 900px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[100rem\] {
  max-width: 100rem;
}
.max-w-\[1200px\] {
  max-width: 1200px;
}
.max-w-\[120px\] {
  max-width: 120px;
}
.max-w-\[150px\] {
  max-width: 150px;
}
.max-w-\[180px\] {
  max-width: 180px;
}
.max-w-\[300px\] {
  max-width: 300px;
}
.max-w-\[400px\] {
  max-width: 400px;
}
.max-w-\[500px\] {
  max-width: 500px;
}
.max-w-\[600px\] {
  max-width: 600px;
}
.max-w-\[80\%\] {
  max-width: 80%;
}
.max-w-\[80px\] {
  max-width: 80px;
}
.max-w-\[90\%\] {
  max-width: 90%;
}
.max-w-\[90vw\] {
  max-width: 90vw;
}
.max-w-\[95vw\] {
  max-width: 95vw;
}
.max-w-\[calc\(100\%-70px\)\] {
  max-width: calc(100% - 70px);
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.flex-grow-0 {
  flex-grow: 0;
}
.origin-center {
  transform-origin: center;
}
.origin-left {
  transform-origin: left;
}
.origin-top {
  transform-origin: top;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-\[0\.5px\] {
  --tw-translate-x: -0.5px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-10 {
  --tw-translate-y: 2.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-5 {
  --tw-translate-y: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-12 {
  --tw-rotate: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-skew-x-12 {
  --tw-skew-x: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-75 {
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-\[-1\] {
  --tw-scale-x: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes fadeIn {

  0% {
    opacity: 0;
    transform: translateY(-10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-none {
  touch-action: none;
}
.select-none {
  user-select: none;
}
.select-all {
  user-select: all;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.list-none {
  list-style-type: none;
}
.auto-rows-auto {
  grid-auto-rows: auto;
}
.auto-rows-fr {
  grid-auto-rows: minmax(0, 1fr);
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-evenly {
  justify-content: space-evenly;
}
.gap-0 {
  gap: 0px;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-3 {
  column-gap: 0.75rem;
}
.gap-x-8 {
  column-gap: 2rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.gap-y-6 {
  row-gap: 1.5rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-20 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(5rem * var(--tw-space-x-reverse));
  margin-left: calc(5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.self-center {
  align-self: center;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-\[20px\] {
  border-radius: 20px;
}
.rounded-\[24px\] {
  border-radius: 24px;
}
.rounded-\[2rem\] {
  border-radius: 2rem;
}
.rounded-\[32px\] {
  border-radius: 32px;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-l-2xl {
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}
.rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-lg {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-tl-2xl {
  border-top-left-radius: 1rem;
}
.rounded-tl-lg {
  border-top-left-radius: var(--radius);
}
.rounded-tr-2xl {
  border-top-right-radius: 1rem;
}
.rounded-tr-lg {
  border-top-right-radius: var(--radius);
}
.\!border-0 {
  border-width: 0px !important;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.\!border-b-0 {
  border-bottom-width: 0px !important;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-\[12px\] {
  border-bottom-width: 12px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-l-\[20px\] {
  border-left-width: 20px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-t-\[12px\] {
  border-top-width: 12px;
}
.border-solid {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.\!border-gray-200 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1)) !important;
}
.\!border-red-500 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1)) !important;
}
.border-\[\#4766C2\] {
  --tw-border-opacity: 1;
  border-color: rgb(71 102 194 / var(--tw-border-opacity, 1));
}
.border-\[\#eef0f2\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 240 242 / var(--tw-border-opacity, 1));
}
.border-\[rgb\(229\2c 231\2c 235\)\] {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-amber-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 243 199 / var(--tw-border-opacity, 1));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.border-blue-100\/30 {
  border-color: rgb(219 234 254 / 0.3);
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-300\/50 {
  border-color: rgb(147 197 253 / 0.5);
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-800\/30 {
  border-color: rgb(30 64 175 / 0.3);
}
.border-cyan-200 {
  --tw-border-opacity: 1;
  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));
}
.border-emerald-100 {
  --tw-border-opacity: 1;
  border-color: rgb(209 250 229 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-100\/50 {
  border-color: rgb(243 244 246 / 0.5);
}
.border-gray-100\/80 {
  border-color: rgb(243 244 246 / 0.8);
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-200\/50 {
  border-color: rgb(229 231 235 / 0.5);
}
.border-gray-200\/60 {
  border-color: rgb(229 231 235 / 0.6);
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-indigo-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}
.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.border-orange-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));
}
.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}
.border-pink-100 {
  --tw-border-opacity: 1;
  border-color: rgb(252 231 243 / var(--tw-border-opacity, 1));
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-white\/60 {
  border-color: rgb(255 255 255 / 0.6);
}
.border-yellow-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 249 195 / var(--tw-border-opacity, 1));
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}
.border-b-transparent {
  border-bottom-color: transparent;
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-l-white {
  --tw-border-opacity: 1;
  border-left-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-r-transparent {
  border-right-color: transparent;
}
.border-t-blue-500 {
  --tw-border-opacity: 1;
  border-top-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.border-t-white {
  --tw-border-opacity: 1;
  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-t-white\/90 {
  border-top-color: rgb(255 255 255 / 0.9);
}
.\!bg-\[\#4766C2\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(71 102 194 / var(--tw-bg-opacity, 1)) !important;
}
.\!bg-red-500 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1)) !important;
}
.\!bg-transparent {
  background-color: transparent !important;
}
.bg-\[\#0f3460\] {
  --tw-bg-opacity: 1;
  background-color: rgb(15 52 96 / var(--tw-bg-opacity, 1));
}
.bg-\[\#46B1E1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(70 177 225 / var(--tw-bg-opacity, 1));
}
.bg-\[\#4766C2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(71 102 194 / var(--tw-bg-opacity, 1));
}
.bg-\[\#4B96F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(75 150 248 / var(--tw-bg-opacity, 1));
}
.bg-\[\#96b3ff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(150 179 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F5F9FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 249 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F6F7F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(246 247 249 / var(--tw-bg-opacity, 1));
}
.bg-\[\#e94560\] {
  --tw-bg-opacity: 1;
  background-color: rgb(233 69 96 / var(--tw-bg-opacity, 1));
}
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}
.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/45 {
  background-color: rgb(0 0 0 / 0.45);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/75 {
  background-color: rgb(0 0 0 / 0.75);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-100\/50 {
  background-color: rgb(219 234 254 / 0.5);
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200\/20 {
  background-color: rgb(191 219 254 / 0.2);
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/30 {
  background-color: rgb(96 165 250 / 0.3);
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-50\/20 {
  background-color: rgb(239 246 255 / 0.2);
}
.bg-blue-50\/50 {
  background-color: rgb(239 246 255 / 0.5);
}
.bg-blue-50\/70 {
  background-color: rgb(239 246 255 / 0.7);
}
.bg-blue-50\/80 {
  background-color: rgb(239 246 255 / 0.8);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}
.bg-blue-500\/30 {
  background-color: rgb(59 130 246 / 0.3);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-blue-950\/30 {
  background-color: rgb(23 37 84 / 0.3);
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-cyan-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-cyan-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-100\/80 {
  background-color: rgb(243 244 246 / 0.8);
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-50\/30 {
  background-color: rgb(249 250 251 / 0.3);
}
.bg-gray-50\/50 {
  background-color: rgb(249 250 251 / 0.5);
}
.bg-gray-50\/70 {
  background-color: rgb(249 250 251 / 0.7);
}
.bg-gray-50\/80 {
  background-color: rgb(249 250 251 / 0.8);
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-800\/30 {
  background-color: rgb(31 41 55 / 0.3);
}
.bg-gray-900\/20 {
  background-color: rgb(17 24 39 / 0.2);
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50\/80 {
  background-color: rgb(238 242 255 / 0.8);
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-50\/50 {
  background-color: rgb(254 242 242 / 0.5);
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50\/80 {
  background-color: rgb(254 252 232 / 0.8);
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/80 {
  background-color: rgb(234 179 8 / 0.8);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-opacity-0 {
  --tw-bg-opacity: 0;
}
.bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-70 {
  --tw-bg-opacity: 0.7;
}
.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-\[\#0A0F2C\] {
  --tw-gradient-from: #0A0F2C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(10 15 44 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#4766C2\] {
  --tw-gradient-from: #4766C2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 102 194 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#4766C2\]\/90 {
  --tw-gradient-from: rgb(71 102 194 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 102 194 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/50 {
  --tw-gradient-from: rgb(0 0 0 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/60 {
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100\/50 {
  --tw-gradient-from: rgb(219 234 254 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400\/50 {
  --tw-gradient-from: rgb(96 165 250 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50\/50 {
  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50\/90 {
  --tw-gradient-from: rgb(239 246 255 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-100 {
  --tw-gradient-from: #f3f4f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 244 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-200\/90 {
  --tw-gradient-from: rgb(229 231 235 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(229 231 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-400 {
  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50 {
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500\/20 {
  --tw-gradient-from: rgb(99 102 241 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500\/30 {
  --tw-gradient-from: rgb(99 102 241 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600 {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600\/20 {
  --tw-gradient-from: rgb(79 70 229 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50 {
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600 {
  --tw-gradient-from: #db2777 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-400 {
  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50 {
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600 {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-400 {
  --tw-gradient-from: #f87171 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 113 113 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-sky-50 {
  --tw-gradient-from: #f0f9ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 249 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/30 {
  --tw-gradient-from: rgb(255 255 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/80 {
  --tw-gradient-from: rgb(255 255 255 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/95 {
  --tw-gradient-from: rgb(255 255 255 / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-200\/30 {
  --tw-gradient-from: rgb(254 240 138 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 240 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-50 {
  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-500 {
  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[\#4766C2\] {
  --tw-gradient-to: rgb(71 102 194 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #4766C2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-\[\#6d8ae8\] {
  --tw-gradient-to: rgb(109 138 232 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #6d8ae8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-\[\#6d8ae8\]\/90 {
  --tw-gradient-to: rgb(109 138 232 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(109 138 232 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/0 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-200\/30 {
  --tw-gradient-to: rgb(191 219 254 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(191 219 254 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-600 {
  --tw-gradient-to: rgb(37 99 235 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #2563eb var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-emerald-600 {
  --tw-gradient-to: rgb(5 150 105 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #059669 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-50 {
  --tw-gradient-to: rgb(238 242 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eef2ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-500 {
  --tw-gradient-to: rgb(249 115 22 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f97316 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-400 {
  --tw-gradient-to: rgb(192 132 252 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #c084fc var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-400\/50 {
  --tw-gradient-to: rgb(192 132 252 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(192 132 252 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-500 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-600 {
  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-500 {
  --tw-gradient-to: rgb(239 68 68 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ef4444 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/20 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[\#16213E\] {
  --tw-gradient-to: #16213E var(--tw-gradient-to-position);
}
.to-\[\#2a3d7c\] {
  --tw-gradient-to: #2a3d7c var(--tw-gradient-to-position);
}
.to-\[\#5C7ED9\] {
  --tw-gradient-to: #5C7ED9 var(--tw-gradient-to-position);
}
.to-\[\#5C7ED9\]\/90 {
  --tw-gradient-to: rgb(92 126 217 / 0.9) var(--tw-gradient-to-position);
}
.to-\[\#5E7CE2\] {
  --tw-gradient-to: #5E7CE2 var(--tw-gradient-to-position);
}
.to-\[\#5E7DE6\] {
  --tw-gradient-to: #5E7DE6 var(--tw-gradient-to-position);
}
.to-\[\#96b3ff\] {
  --tw-gradient-to: #96b3ff var(--tw-gradient-to-position);
}
.to-\[\#96b3ff\]\/90 {
  --tw-gradient-to: rgb(150 179 255 / 0.9) var(--tw-gradient-to-position);
}
.to-amber-50 {
  --tw-gradient-to: #fffbeb var(--tw-gradient-to-position);
}
.to-amber-500 {
  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);
}
.to-black\/20 {
  --tw-gradient-to: rgb(0 0 0 / 0.2) var(--tw-gradient-to-position);
}
.to-black\/30 {
  --tw-gradient-to: rgb(0 0 0 / 0.3) var(--tw-gradient-to-position);
}
.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-blue-200 {
  --tw-gradient-to: #bfdbfe var(--tw-gradient-to-position);
}
.to-blue-300 {
  --tw-gradient-to: #93c5fd var(--tw-gradient-to-position);
}
.to-blue-300\/50 {
  --tw-gradient-to: rgb(147 197 253 / 0.5) var(--tw-gradient-to-position);
}
.to-blue-400 {
  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);
}
.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-blue-50\/80 {
  --tw-gradient-to: rgb(239 246 255 / 0.8) var(--tw-gradient-to-position);
}
.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-blue-700 {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.to-emerald-100 {
  --tw-gradient-to: #d1fae5 var(--tw-gradient-to-position);
}
.to-emerald-50 {
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}
.to-emerald-500 {
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}
.to-gray-100 {
  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);
}
.to-gray-100\/90 {
  --tw-gradient-to: rgb(243 244 246 / 0.9) var(--tw-gradient-to-position);
}
.to-gray-200 {
  --tw-gradient-to: #e5e7eb var(--tw-gradient-to-position);
}
.to-gray-50\/30 {
  --tw-gradient-to: rgb(249 250 251 / 0.3) var(--tw-gradient-to-position);
}
.to-gray-50\/50 {
  --tw-gradient-to: rgb(249 250 251 / 0.5) var(--tw-gradient-to-position);
}
.to-gray-50\/80 {
  --tw-gradient-to: rgb(249 250 251 / 0.8) var(--tw-gradient-to-position);
}
.to-green-100 {
  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);
}
.to-green-400 {
  --tw-gradient-to: #4ade80 var(--tw-gradient-to-position);
}
.to-indigo-100 {
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}
.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}
.to-indigo-50\/50 {
  --tw-gradient-to: rgb(238 242 255 / 0.5) var(--tw-gradient-to-position);
}
.to-indigo-500 {
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-indigo-700 {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}
.to-orange-100 {
  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);
}
.to-orange-50 {
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}
.to-orange-500 {
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}
.to-pink-100 {
  --tw-gradient-to: #fce7f3 var(--tw-gradient-to-position);
}
.to-pink-100\/50 {
  --tw-gradient-to: rgb(252 231 243 / 0.5) var(--tw-gradient-to-position);
}
.to-pink-200\/30 {
  --tw-gradient-to: rgb(251 207 232 / 0.3) var(--tw-gradient-to-position);
}
.to-pink-500 {
  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);
}
.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}
.to-purple-400 {
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}
.to-purple-50 {
  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-purple-700 {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}
.to-red-100 {
  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);
}
.to-red-600 {
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}
.to-red-700 {
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}
.to-rose-600 {
  --tw-gradient-to: #e11d48 var(--tw-gradient-to-position);
}
.to-slate-50 {
  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);
}
.to-teal-700 {
  --tw-gradient-to: #0f766e var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.to-white\/80 {
  --tw-gradient-to: rgb(255 255 255 / 0.8) var(--tw-gradient-to-position);
}
.to-white\/90 {
  --tw-gradient-to: rgb(255 255 255 / 0.9) var(--tw-gradient-to-position);
}
.to-yellow-300 {
  --tw-gradient-to: #fde047 var(--tw-gradient-to-position);
}
.to-yellow-600 {
  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);
}
.to-yellow-700 {
  --tw-gradient-to: #a16207 var(--tw-gradient-to-position);
}
.bg-clip-padding {
  background-clip: padding-box;
}
.bg-clip-text {
  background-clip: text;
}
.object-contain {
  object-fit: contain;
}
.object-cover {
  object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-7 {
  padding: 1.75rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[1px\] {
  padding: 1px;
}
.\!px-4 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}
.\!py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-20 {
  padding-left: 5rem;
  padding-right: 5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pr-0 {
  padding-right: 0px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-14 {
  padding-top: 3.5rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-\[\'DM_Sans\'\] {
  font-family: 'DM Sans';
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[11px\] {
  font-size: 11px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[15px\] {
  font-size: 15px;
}
.text-\[48px\] {
  font-size: 48px;
}
.text-\[8px\] {
  font-size: 8px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.italic {
  font-style: italic;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-7 {
  line-height: 1.75rem;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#18191C\] {
  --tw-text-opacity: 1;
  color: rgb(24 25 28 / var(--tw-text-opacity, 1));
}
.text-\[\#1D2129\] {
  --tw-text-opacity: 1;
  color: rgb(29 33 41 / var(--tw-text-opacity, 1));
}
.text-\[\#4766C2\] {
  --tw-text-opacity: 1;
  color: rgb(71 102 194 / var(--tw-text-opacity, 1));
}
.text-\[\#9499A0\] {
  --tw-text-opacity: 1;
  color: rgb(148 153 160 / var(--tw-text-opacity, 1));
}
.text-\[\#FB7299\] {
  --tw-text-opacity: 1;
  color: rgb(251 114 153 / var(--tw-text-opacity, 1));
}
.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-cyan-400 {
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-cyan-700 {
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.text-emerald-600 {
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-pink-400 {
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}
.text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-rose-700 {
  --tw-text-opacity: 1;
  color: rgb(190 18 60 / var(--tw-text-opacity, 1));
}
.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}
.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}
.text-white\/75 {
  color: rgb(255 255 255 / 0.75);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-white\/95 {
  color: rgb(255 255 255 / 0.95);
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.opacity-0 {
  opacity: 0;
}
.opacity-10 {
  opacity: 0.1;
}
.opacity-100 {
  opacity: 1;
}
.opacity-15 {
  opacity: 0.15;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-\[0\.03\] {
  opacity: 0.03;
}
.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_15px_5px_rgba\(239\2c 68\2c 68\2c 0\.75\)\] {
  --tw-shadow: 0 0 15px 5px rgba(239,68,68,0.75);
  --tw-shadow-colored: 0 0 15px 5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_60px_-15px_rgba\(0\2c 0\2c 0\2c 0\.15\)\] {
  --tw-shadow: 0 0 60px -15px rgba(0,0,0,0.15);
  --tw-shadow-colored: 0 0 60px -15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_2px_10px_rgb\(0\2c 0\2c 0\2c 0\.03\)\] {
  --tw-shadow: 0 2px 10px rgb(0,0,0,0.03);
  --tw-shadow-colored: 0 2px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_2px_15px_rgb\(0\2c 0\2c 0\2c 0\.03\)\] {
  --tw-shadow: 0 2px 15px rgb(0,0,0,0.03);
  --tw-shadow-colored: 0 2px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_15px_rgb\(71\2c 102\2c 194\2c 0\.2\)\] {
  --tw-shadow: 0 4px 15px rgb(71,102,194,0.2);
  --tw-shadow-colored: 0 4px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_15px_rgb\(71\2c 102\2c 194\2c 0\.3\)\] {
  --tw-shadow: 0 4px 15px rgb(71,102,194,0.3);
  --tw-shadow-colored: 0 4px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_20px_rgb\(0\2c 0\2c 0\2c 0\.04\)\] {
  --tw-shadow: 0 4px 20px rgb(0,0,0,0.04);
  --tw-shadow-colored: 0 4px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_30px_rgb\(0\2c 0\2c 0\2c 0\.04\)\] {
  --tw-shadow: 0 8px 30px rgb(0,0,0,0.04);
  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_30px_rgb\(0\2c 0\2c 0\2c 0\.12\)\] {
  --tw-shadow: 0 8px 30px rgb(0,0,0,0.12);
  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[inset_0_2px_4px_rgba\(0\2c 0\2c 0\2c 0\.1\)\] {
  --tw-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
  --tw-shadow-colored: inset 0 2px 4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-blue-100\/50 {
  --tw-shadow-color: rgb(219 234 254 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-200\/50 {
  --tw-shadow-color: rgb(191 219 254 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/20 {
  --tw-shadow-color: rgb(59 130 246 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/30 {
  --tw-shadow-color: rgb(59 130 246 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-gray-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity, 1));
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.ring-white\/30 {
  --tw-ring-color: rgb(255 255 255 / 0.3);
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[0\.5px\] {
  --tw-blur: blur(0.5px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[1px\] {
  --tw-blur: blur(1px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[40px\] {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[60px\] {
  --tw-blur: blur(60px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-md {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[height\] {
  transition-property: height;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-transform {
  will-change: transform;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.duration-100 {
  animation-duration: 100ms;
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.duration-500 {
  animation-duration: 500ms;
}
.duration-700 {
  animation-duration: 700ms;
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* 在您的CSS文件中添加 */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-text-fill-color: inherit !important;
  transition: background-color 5000s ease-in-out 0s;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 240, 240, 240;
}

/* 添加全局文字和图片不可选中的样式 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 保持输入框和文本区域可选中 */
input,
textarea,
[contenteditable='true'] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  overflow-x: hidden;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

.container {
  max-width: 1200px;
}

@keyframes breatheShadow {
  0% {
    box-shadow: 0 0 20px rgba(71, 102, 194, 0.2);
  }

  50% {
    box-shadow: 0 0 30px rgba(71, 102, 194, 0.4);
  }

  100% {
    box-shadow: 0 0 20px rgba(71, 102, 194, 0.2);
  }
}

.carousel-container {
  position: relative;
  margin: 0;

  /* 给轮播图容器添加呼吸灯效果 */
  .slick-list {
    border-radius: 1rem;
    animation: breatheShadow 0.5s ease-in-out infinite;
    margin: 0;
  }

  /* 箭头容器样式 */
  .slick-prev,
  .slick-next {
    z-index: 10;
    width: 32px;
    height: 32px;
    opacity: 0;
    transition: all 0.5s ease;
    background: rgba(0, 0, 0, 0.2) !important;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;

    svg {
      width: 20px;
      height: 20px;
      stroke: white;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
      fill: none;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3) !important;
    }

    &:before {
      display: none;
    }
  }

  .slick-prev {
    left: 16px;
  }

  .slick-next {
    right: 16px;
  }

  /* 鼠标悬停时显示箭头 */
  &:hover {

    .slick-prev,
    .slick-next {
      opacity: 1;
    }
  }

  /* 点点样式 */
  .slick-dots {
    bottom: -24px;
    margin: 0;
    padding: 0;

    li {
      width: 24px;
      height: 8px;
      margin: 0 4px;
      transition: all 0.5s ease;

      button {
        width: 24px;
        height: 8px;
        padding: 0;

        &:before {
          content: '';
          width: 8px;
          height: 8px;
          background: #4766c2;
          border-radius: 50%;
          opacity: 0.3;
          position: absolute;
          top: 0;
          left: 8px;
          transition: all 0.5s ease;
        }
      }

      &.slick-active {
        button:before {
          width: 24px;
          left: 0;
          border-radius: 4px;
          opacity: 1;
        }
      }
    }
  }

  .slick-slide {
    &:focus {
      outline: none;
    }
  }

  /* 移除 slick-track 的外边距 */
  .slick-track {
    margin-left: 0;
    margin-right: 0;
  }
}

.grid-masonry {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 180px;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
}

/* 注释掉特性卡片相关样式 */
/*
.feature-card {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: #f5f5f5;
  position: relative;
  padding: 1.8rem;
  border: 2px solid #c3c6ce;
  transition: 0.5s ease-out;
  overflow: hidden;
}

.feature-card:nth-child(1) {
  height: 380px;
}

.feature-card:nth-child(3) {
  height: 380px;
}

.feature-card:nth-child(5) {
  height: 180px;
}

.feature-card-details {
  color: black;
  height: 100%;
  gap: .5em;
  display: grid;
  place-content: center;
}

.feature-text-body {
  color: rgb(134, 134, 134);
}

.feature-text-title {
  font-size: 1.5em;
  font-weight: bold;
  color: #4766C2;
}

.feature-card:hover {
  border-color: #4766C2;
  box-shadow: 0 4px 18px 0 rgba(71, 102, 194, 0.25);
}
*/

.gallery-container {
  width: 100%;
  padding: 0 1rem;
}

.gallery-header {
  margin-bottom: 2rem;
}

.gallery-tags::-webkit-scrollbar {
  display: none;
}

.gallery-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 0;
  color: #666;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
}

.tag-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: currentColor;
}

.gallery-tag.active .tag-icon,
.gallery-tag:hover .tag-icon {
  color: #4766c2;
}

.gallery-tag svg {
  width: 16px;
  height: 16px;
}

.gallery-tag.active {
  color: #4766c2;
  background: none;
}

.gallery-tag.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #4766c2;
  border-radius: 1px;
}

.gallery-tag:hover {
  color: #4766c2;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  grid-auto-flow: row dense;
}

.gallery-item {
  grid-column: span 1;
  height: fit-content;
  break-inside: avoid;
  page-break-inside: avoid;
}

.gallery-item-inner {
  position: relative;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  transform-origin: center top;
  transition: all 0.5s ease;
}

.gallery-image {
  position: relative;
  width: 100%;
  padding-bottom: 100%;
  /* 默认1:1比例 */
  background: #000;
  transition: all 0.5s ease;
}

.gallery-image img {
  transition: all 0.5s ease;
}

.gallery-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  color: white;
  transform: translateY(100%);
  transition: all 0.5s ease;
  scroll-margin-top: 76px;
  /* 导航栏高度 + 额外间距 */
}

.gallery-item:hover .gallery-content {
  transform: translateY(0);
}

.gallery-title {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 4px;
  color: white;
}

.gallery-description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.gallery-item-tag {
  display: inline-block;
  padding: 2px 8px;
  margin: 0 4px 4px 0;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
}

.gallery-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.gallery-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gallery-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* 创意展示区容器 */
.showcase-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 60px);
  position: relative;
  background: #fff;
  margin-top: 0;
  border-top: 1px solid rgba(71, 102, 194, 0.15);
}

/* 优化侧边栏样式 */
.showcase-sidebar {
  width: 240px;
  min-width: 240px;
  position: sticky;
  top: 60px;
  /* 导航栏高度 */
  height: calc(100vh - 60px);
  background: #fafbff;
  border-right: 1px solid #e5eeff;
  overflow-y: auto;
  z-index: 20;
  display: flex;
  flex-direction: column;
}

/* 优化标题和标签栏样式 */
.showcase-header {
  position: sticky;
  top: 0;
  background: white;
  padding: 1rem 2rem 0 2rem;
  border-bottom: 1px solid #e5eeff;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* 内容区域样式 */
.showcase-content {
  flex: 1;
  min-width: 0;
  background: white;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 筛选按钮样式优化 */
.filter-dropdown {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #4766c2;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
}

/* 标题样式优化 */
.showcase-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
}

.showcase-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .showcase-sidebar {
    width: 200px;
    min-width: 200px;
  }

  .showcase-container {
    height: auto;
    min-height: calc(100vh - 60px);
  }
}

@media (max-width: 768px) {
  .showcase-container {
    flex-direction: column;
  }

  .showcase-sidebar {
    position: relative;
    top: 0;
    width: 100%;
    height: auto;
    min-height: auto;
    border-right: none;
    border-bottom: 1px solid #e5eeff;
    flex-direction: row;
    overflow-x: auto;
    padding: 0.75rem;
  }

  .sidebar-group {
    display: flex;
    flex-shrink: 0;
    margin-right: 1rem;
    margin-bottom: 0;
    border-bottom: none;
  }

  .sidebar-group-title {
    padding: 0.75rem 1rem;
    border-bottom: none;
    white-space: nowrap;
  }

  .side-nav-item {
    white-space: nowrap;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
  }

  /* 隐藏底部内容 */
  .sidebar-footer {
    display: none;
  }
}

/* 移除装饰分割线的固定定位 */
.showcase-header::before {
  display: none;
}

/* 侧边栏分组标题 */
.sidebar-group-title {
  padding: 0.75rem 1.25rem;
  color: #8a94a6;
  font-size: 0.8125rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.sidebar-group-title::after {
  content: '';
  position: absolute;
  left: 1.25rem;
  right: 1.25rem;
  bottom: 0;
  height: 1px;
  background: linear-gradient(to right, #e5eeff 50%, transparent 50%);
  background-size: 8px 1px;
  background-repeat: repeat-x;
}

/* 侧边栏项目式 */
.side-nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.side-nav-item:hover {
  background: rgba(71, 102, 194, 0.05);
}

.side-nav-item.active {
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
}

.side-nav-item.active .side-nav-icon {
  color: #4766c2;
}

.side-nav-icon {
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  color: #666;
  transition: color 0.2s ease;
}

/* 子标题样式 */
.subtitle {
  font-size: 0.75rem;
  color: #8a94a6;
  margin-left: 34px;
}

/* 图标容器 */
.side-nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: currentColor;
  opacity: 0.75;
  transition: all 0.2s ease;
}

/* 悬浮和激活状态 */
.side-nav-item:hover {
  background: linear-gradient(to right, #f6f9ff, transparent);
  color: #4766c2;
}

.side-nav-item.active {
  background: linear-gradient(to right, #f6f9ff, transparent);
  color: #4766c2;
  font-weight: 500;
}

.side-nav-item:hover .side-nav-icon,
.side-nav-item.active .side-nav-icon {
  opacity: 1;
}

/* 滚动条样式 */
.showcase-sidebar::-webkit-scrollbar {
  width: 0px;
}

/* 分组间的间距 */
.sidebar-group {
  padding: 0;
  margin-bottom: 0.5rem;
}

.sidebar-group:last-of-type {
  margin-bottom: 0;
  padding-bottom: 1rem;
}

/* 分割线 */
.sidebar-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 0.5rem 0;
}

/* 内容区 */
.showcase-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* Chrome, Safari and Opera */
.showcase-content::-webkit-scrollbar {
  display: none;
}

.content-container::-webkit-scrollbar {
  display: none;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .showcase-container {
    flex-direction: column;
  }

  .showcase-sidebar {
    position: static;
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding: 0.75rem;
    display: flex;
    overflow-x: auto;
  }

  .side-nav-item {
    white-space: nowrap;
    padding: 0.75rem 1.25rem;
  }

  .side-nav-item.active::before {
    width: 100%;
    height: 3px;
    top: auto;
    bottom: -0.75rem;
  }

  .showcase-content {
    padding: 1.5rem;
  }
}

/* 移除标题相关样式 */
.showcase-title {
  display: none;
}

.gallery-tags {
  display: flex;
  flex-wrap: nowrap;
  gap: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;
  border-bottom: 1px solid #f0f0f0;
}

.gallery-tags::-webkit-scrollbar {
  display: none;
}

.gallery-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 0;
  color: #666;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
}

.gallery-tag .tag-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.gallery-tag:hover {
  color: #4766c2;
}

.gallery-tag:hover .tag-icon {
  opacity: 1;
}

.gallery-tag.active {
  color: #4766c2;
  font-weight: 500;
}

.gallery-tag.active .tag-icon {
  opacity: 1;
}

.gallery-tag.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #4766c2;
  border-radius: 1px;
}

/* 标签栏滚动阴影 */
.gallery-tags::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 40px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff);
  pointer-events: none;
}

/* 深色模式开关样式 */
.dark-mode-toggle {
  display: flex;
  justify-content: space-between !important;
  margin-top: auto;
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #e5eeff;
  background: linear-gradient(to right, #f6f9ff, transparent);
}

.dark-mode-switch {
  position: relative;
  width: 40px;
  height: 20px;
}

.dark-mode-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.dark-mode-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #eee;
  transition: 0.5s;
  border-radius: 34px;
}

.dark-mode-switch label:before {
  position: absolute;
  content: '';
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.5s;
  border-radius: 50%;
}

.dark-mode-switch input:checked+label {
  background-color: #4766c2;
}

.dark-mode-switch input:checked+label:before {
  transform: translateX(20px);
}

/* 侧边栏底部样式 */
.sidebar-footer {
  margin-top: auto;
  padding: 1rem 1.25rem;
  border-top: 1px solid #f0f0f0;
}

/* 加入我们按钮样式优化 */
.join-us-btn {
  margin: 1rem;
  padding: 0.75rem 1rem;
  background: #fff0f0;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  color: #4766c2;
  font-weight: 600;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.join-us-btn::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.join-us-btn:hover {
  background: #f6f9ff;
  transform: translate(0, 0.15em);
}

.join-us-btn:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.join-us-btn:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.join-us-btn:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

/* 底部链接样式 */
.footer-links {
  padding: 1rem 1.25rem;
  border-top: 1px solid #e5eeff;
}

.footer-link-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.footer-link-group a {
  color: #8a94a6;
  font-size: 0.75rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link-group a:hover {
  color: #4766c2;
}

/* 公司信息 */
.footer-company {
  font-size: 0.75rem;
  color: #b0b7c3;
  line-height: 1.5;
}

/* 优化图标对齐 */
.side-nav-icon svg {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.side-nav-item:hover .side-nav-icon svg,
.side-nav-item.active .side-nav-icon svg {
  opacity: 1;
}

/* 添加分组之间的分隔 */
.sidebar-group+.sidebar-group {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5eeff;
}

/* 创作组样式优化 */
.sidebar-group.creation-group .side-nav-item {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1rem;
  background: #fff0f0;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.sidebar-group.creation-group .side-nav-item::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.sidebar-group.creation-group .side-nav-item:hover {
  background: #f6f9ff;
  transform: translate(0, 0.15em);
}

.sidebar-group.creation-group .side-nav-item:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.sidebar-group.creation-group .side-nav-item:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.sidebar-group.creation-group .side-nav-item:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

/* 优化深色模式开关位置 */
.dark-mode-toggle {
  margin-top: 0.5rem;
  /* 减少上方空间 */
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #f0f0f0;
}

/* 调整分组间距 */
.sidebar-group {
  padding: 0;
  margin-bottom: 0.5rem;
}

.sidebar-group.creation-group {
  margin-bottom: 0.75rem;
}

/* 子标位置优化 */
.sidebar-group.creation-group .subtitle {
  margin-left: 0;
  margin-top: 0.25rem;
  display: block;
}

/* 标题区域样式 */
.showcase-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.showcase-title h2 {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* 筛选按钮样式 */
.filter-dropdown {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 1rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #4766c2;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
}

.filter-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5eeff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #333;
  cursor: pointer;
}

/* 视图切换按钮 */
.view-mode {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
  color: #4766c2;
}

/* 内容卡片样式优化 */
.gallery-item-inner {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  border: 1px solid #e5eeff;
  transition: all 0.5s ease;
}

.gallery-item-inner:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.gallery-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.5s ease;
}

.gallery-item-inner:hover .gallery-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  border: none;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: white;
  color: #4766c2;
}

/* 内容元信息样式 */
.gallery-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 0.75rem;
}

.model-tag {
  padding: 0.2rem 0.4rem;
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.author span {
  font-size: 0.75rem;
  color: #666;
}

/* 添加遮罩层样式 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 90;
}

/* 调整筛选菜单级别 */
.filter-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5eeff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 创作工具区域样式 */
.creation-tools-section {
  position: relative;
  z-index: 1;
  padding: 4rem 2rem;
  margin: 2rem auto;
  max-width: 2200px;
  scroll-margin-top: 76px;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #4766c2;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(71, 102, 194, 0.2);
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.tools-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 1rem;
}

.tool-card {
  position: relative;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(71, 102, 194, 0.15);
  box-shadow: 0 4px 20px rgba(71, 102, 194, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transform-style: preserve-3d;
  perspective: 1000px;
  backdrop-filter: blur(10px);
  will-change: transform, box-shadow, border-color, background;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  cursor: pointer;
}

.tool-card * {
  pointer-events: none;
}

/* 允许按钮响应事件 */
.tool-card .create-btn {
  pointer-events: auto;
}

.tool-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(71, 102, 194, 0.2), inset 0 0 0 2px rgba(71, 102, 194, 0.5);
  border-color: rgba(71, 102, 194, 0.5);
  background: rgba(255, 255, 255, 0.95);
}

.tool-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 24px;
  padding: 2px;
  background: linear-gradient(135deg, rgba(71, 102, 194, 0.2), rgba(108, 138, 228, 0.2));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 1;
  transition: opacity 0.5s ease;
}

.tool-card:hover::before {
  background: linear-gradient(135deg, rgba(71, 102, 194, 0.8), rgba(108, 138, 228, 0.8));
}

.tool-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #edf2ff, #f6f9ff);
  border-radius: 24px;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-style: preserve-3d;
  transform: translateZ(20px);
  box-shadow: 0 8px 24px rgba(71, 102, 194, 0.15), inset 0 0 0 1px rgba(71, 102, 194, 0.2);
  position: relative;
  will-change: transform, background, box-shadow;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  pointer-events: none;
}

.tool-icon * {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  pointer-events: none;
}

.tool-icon svg {
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  width: 32px;
  height: 32px;
  will-change: transform, color, filter;
  color: #4766c2;
  filter: drop-shadow(0 2px 4px rgba(71, 102, 194, 0.2));
  pointer-events: none;
  -webkit-user-drag: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tool-card:hover .tool-icon {
  transform: translateZ(30px) scale(1.1);
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  box-shadow: 0 12px 36px rgba(71, 102, 194, 0.3), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.tool-card:hover .tool-icon::before {
  opacity: 0;
}

.tool-card:hover .tool-icon::after {
  opacity: 1;
}

.tool-card:hover .tool-icon svg {
  color: white;
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.tool-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  transform: translateZ(10px);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  will-change: transform, color, text-shadow;
}

.tool-card:hover h3 {
  color: #4766c2;
  transform: translateZ(20px);
  text-shadow: 0 4px 8px rgba(71, 102, 194, 0.2);
}

.tool-card p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
  transform: translateZ(5px);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, color;
}

.tool-card:hover p {
  color: #4766c2;
  transform: translateZ(15px);
}

.create-btn {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  cursor: pointer;
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
  border: 1px solid rgba(71, 102, 194, 0.2);
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(15px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(71, 102, 194, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  isolation: isolate;
  will-change: transform, box-shadow, border-color, color;
  pointer-events: auto;
}

.create-btn::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
  will-change: opacity;
}

.create-btn::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.create-btn:hover {
  color: white;
  border-color: transparent;
  transform: translateZ(25px);
  box-shadow: 0 10px 30px rgba(71, 102, 194, 0.3);
}

.create-btn:hover::before,
.create-btn:hover::after {
  opacity: 1;
}

.create-btn span {
  position: relative;
  z-index: 1;
  mix-blend-mode: multiply;
  transition: mix-blend-mode 0.5s ease;
  pointer-events: none;
}

.create-btn:hover span {
  mix-blend-mode: normal;
}

/* 响应式布局调整 */
@media (max-width: 1920px) {
  .creation-tools-section {
    max-width: 1700px;
  }

  .tools-container {
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 1400px) {
  .creation-tools-section {
    max-width: 1200px;
    padding: 3rem 1.5rem;
  }

  .tools-container {
    grid-template-columns: repeat(3, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .tool-card {
    padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .tools-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .creation-tools-section {
    padding: 2rem 1rem;
  }

  .tools-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tool-card {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }
}

/* 作品网格样式 */
.works-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.work-item {
  background: white;
  border-radius: 20px;
  border: 1px solid #e5eeff;
  transition: 0.2s ease-in-out;
  margin-bottom: 1rem;
  overflow: hidden;
  height: 340px;
}

.work-item:hover {
  cursor: pointer;
  transform: translateY(-5px);
  border-color: #4766c2;
}

.work-item-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.work-image {
  width: 100%;
  height: 180px;
  position: relative;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.work-item:hover .work-image img {
  transform: scale(1.05);
}

.work-content {
  padding: 0.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.work-title {
  font-family: 'Lucida Sans', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.work-description {
  font-family: 'Lucida Sans', sans-serif;
  color: #666;
  font-size: 0.813rem;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.work-meta {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f6f8ff;
  border-radius: 10px;
  color: #4766c2;
  font-size: 0.75rem;
}

.meta-item svg {
  width: 14px;
  height: 14px;
}

.author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.author span {
  font-size: 0.75rem;
  color: #666;
}

/* 添加响应式布局 */
@media (max-width: 1600px) {
  .works-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1280px) {
  .works-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .works-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .showcase-content {
    padding: 0 1rem;
  }
}

@media (max-width: 640px) {
  .works-grid {
    grid-template-columns: 1fr;
  }
}

/* 激活状态的侧边栏项目 */
.side-nav-item.active {
  color: #4766c2;
  background: rgba(71, 102, 194, 0.1);
}

.side-nav-item.active .side-nav-icon {
  color: #4766c2;
}

/* 创作项目样式 */
.side-nav-item.creation-item {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1rem;
  background: #6c8ae4;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  color: white;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.side-nav-item.creation-item::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.side-nav-item.creation-item:hover {
  background: #7d98e8;
  transform: translate(0, 0.15em);
}

.side-nav-item.creation-item:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.side-nav-item.creation-item:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.side-nav-item.creation-item:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

.side-nav-item.creation-item .subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
  position: relative;
  z-index: 1;
}

.side-nav-item.creation-item .side-nav-icon {
  color: white;
  position: relative;
  z-index: 1;
}

/* 确保内容在3D效果上层 */
.side-nav-item.creation-item>* {
  position: relative;
  z-index: 1;
}

/* 为不同类别的创作按钮添加不同的背景色 */
.sidebar-group:nth-child(2) .creation-item,
.sidebar-group:nth-child(3) .creation-item {
  background: #6c8ae4;
}

/* 未开发功能的样式 */
.side-nav-item.developing {
  opacity: 0.8;
  cursor: not-allowed;
}

.side-nav-item.developing:hover {
  background: rgba(71, 102, 194, 0.05);
}

/* 开发中提示的样式 */
.developing-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  z-index: 1000;
}

/* 加入我们按钮样式 */
.side-nav-item.join-us-btn {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1.25rem;
  background-color: #4766c2;
  border: 2px solid #3755ab;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.5s;
  font-weight: 600;
  box-shadow: 0 2px 0 2px #2b4494;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.side-nav-item.join-us-btn::before {
  content: '';
  position: absolute;
  width: 100px;
  height: 120%;
  background-color: #6c8ae4;
  top: 50%;
  transform: skewX(30deg) translate(-150%, -50%);
  transition: all 0.5s;
}

.side-nav-item.join-us-btn:hover {
  background-color: #5577d3;
  color: white;
  box-shadow: 0 2px 0 2px #2b4494;
}

.side-nav-item.join-us-btn:hover::before {
  transform: skewX(30deg) translate(150%, -50%);
  transition-delay: 0.1s;
}

.side-nav-item.join-us-btn:active {
  transform: scale(0.95);
}

.side-nav-item.join-us-btn .side-nav-icon {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  /* 确保图标在动画效果上层 */
  z-index: 1;
}

.side-nav-item.join-us-btn span {
  position: relative;
  /* 确保文字在动画效果上层 */
  z-index: 1;
}

/* 移除重复的加入我们按钮 */
.sidebar-footer .join-us-btn {
  display: none;
}

/* 优化底部布局 */
.sidebar-footer {
  margin-top: auto;
  padding: 1rem;
  border-top: 1px solid #e5eeff;
}

.footer-links {
  padding-top: 1rem;
  opacity: 0.8;
  font-size: 0.75rem;
}

.footer-link-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.footer-link-group a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link-group a:hover {
  color: #4766c2;
}

/* 作品卡片样式 */
.work-item {
  background: white;
  border-radius: 20px;
  border: 1px solid #e5eeff;
  transition: 0.2s ease-in-out;
  margin-bottom: 1rem;
  overflow: hidden;
  height: 340px;
}

.work-item:hover {
  cursor: pointer;
  transform: translateY(-5px);
  border-color: #4766c2;
}

.work-item-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.work-image {
  width: 100%;
  height: 180px;
  position: relative;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.work-item:hover .work-image img {
  transform: scale(1.05);
}

/* 自定义滚动条样式 */
.activity-festival-page {
  scrollbar-width: thin;
  scrollbar-color: rgba(71, 102, 194, 0.3) rgba(71, 102, 194, 0.1);
}

.activity-festival-page::-webkit-scrollbar {
  width: 8px;
}

.activity-festival-page::-webkit-scrollbar-track {
  background: rgba(71, 102, 194, 0.1);
  border-radius: 10px;
}

.activity-festival-page::-webkit-scrollbar-thumb {
  background: rgba(71, 102, 194, 0.3);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.activity-festival-page::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 102, 194, 0.5);
}

/* 活动详情容器滚动条样式 */
.activity-detail-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(71, 102, 194, 0.2) rgba(71, 102, 194, 0.05);
}

.activity-detail-container::-webkit-scrollbar {
  width: 6px;
}

.activity-detail-container::-webkit-scrollbar-track {
  background: rgba(71, 102, 194, 0.05);
  border-radius: 8px;
}

.activity-detail-container::-webkit-scrollbar-thumb {
  background: rgba(71, 102, 194, 0.2);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.activity-detail-container::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 102, 194, 0.4);
}

/* 其他元素保持隐藏滚动条 */
*:not(.activity-festival-page) {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

*:not(.activity-festival-page)::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* 主容器 */
.main-container {
  overflow-y: auto;
  height: 100vh;
}

/* 展示区容器 */
.showcase-container {
  overflow: hidden;
  height: calc(100vh - 60px);
  /* 减去导航栏高度 */
}

/* 内容区 */
.showcase-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding: 0.5rem 2rem;
  width: 100%;
  overflow-y: auto;
}

@media screen and (max-width: 1024px) {
  .carousel-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .carousel-container .slick-slide {
    padding: 0 0.5rem;
  }
}

@media screen and (max-width: 768px) {
  .carousel-container .slick-slide>div {
    margin: 0 -0.5rem;
  }

  /* Logo 容器在小屏幕下的调整 */
  .motion.div {
    margin-top: 2rem !important;
  }
}

/* 确保 Logo 容器在所有屏幕尺寸下都保持合适的比例 */
@media screen and (max-width: 480px) {
  .motion.div {
    margin-top: 1rem !important;
  }
}

/* 添加响应式布局相关样式 */
@media screen and (max-width: 1024px) {
  .carousel-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .carousel-container .slick-slide {
    padding: 0 0.5rem;
  }
}

@media screen and (max-width: 768px) {
  .carousel-container .slick-slide>div {
    margin: 0 -0.5rem;
  }
}

/* 优化标题和内容在不同屏幕尺寸下的显示 */
@media screen and (max-width: 640px) {
  h1.text-4xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  p.text-base {
    font-size: 1rem;
    line-height: 1.5;
  }

  .carousel-container {
    margin-top: 1rem;
  }
}

/* Logo 容器响应式调整 */
@media screen and (max-width: 480px) {
  .motion-div {
    height: 200px !important;
    margin-top: 1rem !important;
  }
}

/* 确保内容在小屏幕上有适当的间距 */
@media screen and (max-width: 768px) {
  .min-h-\[calc\(80vh-6rem\)\] {
    min-height: auto;
    padding-bottom: 2rem;
  }
}

.create-btn:focus {
  outline: none;
}

.create-btn:focus-visible {
  outline: none;
  box-shadow: 0 4px 12px rgba(71, 102, 194, 0.1), inset 0 0 0 2px rgba(71, 102, 194, 0.4);
}

/* 禁用所有可能的默认交互状态 */
.tool-card:focus,
.tool-card:focus-within,
.tool-card:focus-visible,
.tool-icon:focus,
.tool-icon:focus-within,
.tool-icon:focus-visible,
.tool-icon svg:focus,
.tool-icon svg:focus-within,
.tool-icon svg:focus-visible {
  outline: none;
  box-shadow: none;
  -webkit-tap-highlight-color: transparent;
}

/* 隐藏默认的 Slick 箭头 */
.slick-prev:before,
.slick-next:before {
  display: none !important;
}

/* 自定义箭头按钮样式 */
.slick-prev,
.slick-next {
  width: 40px !important;
  height: 40px !important;
  z-index: 10 !important;
}

.slick-prev:hover,
.slick-next:hover {
  opacity: 1 !important;
}

/* 调整箭头位置 */
.slick-prev {
  left: 16px !important;
}

.slick-next {
  right: 16px !important;
}

/* 当只有一张图片时隐藏箭头 */
.slick-slider.single-image .slick-arrow {
  display: none !important;
}

/* 作品轮播图样式 */
.work-slider-container {
  position: relative;
  margin: 0;
  padding: 0;
}

.work-slider {
  position: relative;
}

/* 轮播图片容器样式 */
.work-slider .slick-slide>div {
  padding: 0;
}

/* 图片容器样式 */
.work-slider .slick-slide .relative {
  width: 100%;
  aspect-ratio: 16/9;
  /* 保持 16:9 的宽高比 */
  position: relative;
  overflow: hidden;
}

/* 封面图片样式 */
.work-slider .slick-slide img.object-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 截图样式 */
.work-slider .slick-slide img.object-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #f5f5f5;
  /* 添加浅灰色背景 */
}

/* 箭头按钮样式 */
.work-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  border: none;
  padding: 0;
  transition: all 0.5s ease;
  opacity: 0;
}

.work-slider-container:hover .work-slider-arrow {
  opacity: 1;
}

.work-slider-arrow:hover {
  background: rgba(0, 0, 0, 0.7);
}

.work-slider-arrow.prev {
  left: 16px;
}

.work-slider-arrow.next {
  right: 16px;
}

/* 轮播容器样式 */
.work-slider .slick-list {
  overflow: hidden;
  margin: 0;
  border-radius: 12px;
  /* 添加圆角 */
}

/* 轮播点样式 */
.work-slider .slick-dots {
  bottom: -25px;
}

.work-slider .slick-dots li button:before {
  font-size: 8px;
  color: #4766c2;
}

.work-slider .slick-dots li.slick-active button:before {
  color: #4766c2;
}

/* AI绘画加载动画 */
.analyze svg path.stick {
  transform: translate(0);
  animation: stick 2s ease infinite;
}

.analyze svg path.star-1 {
  fill: #4766c2;
  animation: sparkles 2s ease infinite, scaleStars 2s ease infinite, colorChange 2s ease infinite;
  animation-delay: 150ms;
}

.analyze svg path.star-2 {
  fill: #6c8ae4;
  animation: sparkles 2s ease infinite, scaleStars 2s ease infinite, colorChange 2s ease infinite;
}

.board {
  animation: bounce 2s ease infinite;
}

@keyframes sparkles {
  0% {
    opacity: 1;
  }

  35% {
    opacity: 1;
  }

  55% {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes stick {
  0% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  25% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  50% {
    transform: translate3d(3px, -2px, 0) rotate(8deg);
  }

  75% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  100% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
}

@keyframes scaleStars {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(0);
  }

  75% {
    transform: translateY(-1px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes colorChange {
  0% {
    fill: #4766c2;
  }

  25% {
    fill: #6c8ae4;
  }

  50% {
    fill: #4766c2;
  }

  75% {
    fill: #6c8ae4;
  }

  100% {
    fill: #4766c2;
  }
}

/* 添加响应式样式 */
@media screen and (max-width: 640px) {
  .sidebar-footer {
    padding: 1rem;
  }

  .sidebar-footer .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* 自定义水平滚动条样式 */
.horizontal-scroll-container {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #a0aec0 #edf2f7;
  /* For Firefox */
}

/* For Chrome, Safari, and Edge */
.horizontal-scroll-container::-webkit-scrollbar {
  height: 8px;
}

.horizontal-scroll-container::-webkit-scrollbar-track {
  background-color: #edf2f7;
  border-radius: 10px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb {
  background-color: #a0aec0;
  border-radius: 10px;
  border: 2px solid #edf2f7;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: #718096;
}


/* Background Animation */
@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
    border-radius: 0;
  }

  100% {
    transform: translateY(-1000px) rotate(720deg);
    opacity: 0;
    border-radius: 50%;
  }
}

.background-squares {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.background-squares li {
  position: absolute;
  display: block;
  list-style: none;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  /* Make squares more subtle */
  animation: animate 25s linear infinite;
  bottom: -150px;
}

.background-squares li:nth-child(1) {
  left: 25%;
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.background-squares li:nth-child(2) {
  left: 10%;
  width: 20px;
  height: 20px;
  animation-delay: 2s;
  animation-duration: 12s;
}

.background-squares li:nth-child(3) {
  left: 70%;
  width: 20px;
  height: 20px;
  animation-delay: 4s;
}

.background-squares li:nth-child(4) {
  left: 40%;
  width: 60px;
  height: 60px;
  animation-delay: 0s;
  animation-duration: 18s;
}

.background-squares li:nth-child(5) {
  left: 65%;
  width: 20px;
  height: 20px;
  animation-delay: 0s;
}

.background-squares li:nth-child(6) {
  left: 75%;
  width: 110px;
  height: 110px;
  animation-delay: 0.5s;
}

.background-squares li:nth-child(7) {
  left: 35%;
  width: 150px;
  height: 150px;
  animation-delay: 7s;
}

.background-squares li:nth-child(8) {
  left: 50%;
  width: 25px;
  height: 25px;
  animation-delay: 15s;
  animation-duration: 45s;
}

.background-squares li:nth-child(9) {
  left: 20%;
  width: 15px;
  height: 15px;
  animation-delay: 2s;
  animation-duration: 35s;
}

.background-squares li:nth-child(10) {
  left: 85%;
  width: 150px;
  height: 150px;
  animation-delay: 0s;
  animation-duration: 11s;
}

/* New Glowing Orb Animation */
.glow-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(90px);
  opacity: 0.25;
  z-index: 0;
}

@keyframes float {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(40px, -50px) scale(1.1);
  }

  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-reverse {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(-40px, 50px) scale(0.9);
  }

  100% {
    transform: translate(0, 0);
  }
}

/* Custom styles for Ant Design Select in Login Form */
.student-login-select .ant-select-selector {
  background-color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(96, 165, 250, 0.5) !important;
  border-radius: 0.5rem !important;
  color: #1e3a8a !important;
  display: flex;
}

.student-login-select .ant-select-selection-placeholder,
.student-login-select .ant-select-arrow {
  color: rgba(30, 58, 138, 0.6) !important;
  
}

.student-login-select-dropdown {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(96, 165, 250, 0.5) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  
}

.student-login-select-dropdown .ant-select-item {
  color: #000000 !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.student-login-select-dropdown .ant-select-item-option-active,
.student-login-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #1e3a8a !important;
  border-radius: 6px !important;
}

.student-login-select-dropdown .ant-select-item-empty {
  color: rgba(30, 58, 138, 0.8) !important;
}

.student-login-select.ant-select-focused .ant-select-selector,
.student-login-select .ant-select-selector:hover {
  border-color: #000000 !important;
  /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

.student-login-select.has-error .ant-select-selector {
  border-color: #ef4444 !important;
  /* red-500 */
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

.student-login-select .ant-select-selector:hover {
  border-color: #000000 !important;
  /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

/* 为InlineTeacherAuth组件中的Select添加样式 */
.bg-white\/10 .ant-select-selection-item {
  color: black !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.bg-white\/10.ant-select-focused .ant-select-selector,
.bg-white\/10 .ant-select-selector:hover {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

/* 针对带有bg-white/10类的元素中的ant-select组件 */
[class*="bg-white/10"] .ant-select-selection-item {
  color: white !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

[class*="bg-white/10"] .ant-select-selector {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
}

[class*="bg-white/10"] .ant-select-focused .ant-select-selector,
[class*="bg-white/10"] .ant-select-selector:hover {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

/* 为带有bg-white/10类的元素自定义下拉菜单样式 */
.ant-select-dropdown {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.5) !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-dropdown .ant-select-item {
  color: #000000 !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-dropdown .ant-select-item-option-active,
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #1e3a8a !important;
  border-radius: 6px !important;
}

/* 添加输入框错误样式 */
input.has-error {
  border-color: #ef4444 !important; /* red-500 */
  border-width: 2px !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
  animation: errorShake 0.5s ease-in-out !important;
  background-color: rgba(254, 226, 226, 0.3) !important; /* red-100 with transparency */
}

/* 添加错误抖动动画 */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

/* 添加错误状态的占位符和文本颜色 */
input.has-error::placeholder {
  color: #ef4444 !important;
}

input.has-error + label {
  color: #ef4444 !important;
  font-weight: 500 !important;
}

/* 添加缓动函数 */
.slide-in-right, .slide-out-left, 
.slide-in-left, .slide-out-right {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); /* Material Design 标准缓动 */
}

/* 添加颜色渐变动画 */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background: linear-gradient(-45deg, #4766C2, #2a3f87, #6384e0, #3a5bb8);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
}

.animate-gradient-x {
  background: linear-gradient(90deg, #4766C2, #2a3f87, #6384e0, #3a5bb8);
  background-size: 200% 200%;
  animation: gradientAnimation 15s ease infinite;
}

.animate-gradient-move {
  animation: gradientAnimation 15s ease infinite;
}

/* 添加从右往左滑动的动画样式 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}



@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0.5;
  }
}

.slide-in-right {
  animation: slideInRight 0.5s forwards;
}

.slide-out-left {
  animation: slideOutLeft 0.5s forwards;
}

.slide-in-left {
  animation: slideInLeft 0.5s forwards;
}

.slide-out-right {
  animation: slideOutRight 0.5s forwards;
}

.slide-container {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  overflow: hidden;
}

.slide-content {
  position: absolute;
  width: 100%;
  max-width: 32rem; /* max-w-xl */
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #000000 #f3f4f6;
  /* For Firefox */
}

/* For Chrome, Safari, and Edge */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #ef4444;
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #dc2626;
}

/* 添加蓝色滚动条样式 */
.scrollbar-thin.scrollbar-thumb-color {
  scrollbar-color: #413ea8 #ffffff;
  /* For Firefox */
}

.scrollbar-thin.scrollbar-thumb-blue-500::-webkit-scrollbar-thumb {
  background-color: #000000;
}

.scrollbar-thin.scrollbar-thumb-blue-500::-webkit-scrollbar-thumb:hover {
  background-color: #000000;
}

/* logo发光样式 */
.glow-container {
  position: relative;
}

.glow-container img {
  position: relative;
  z-index: 2;
}

.glow-container::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(70,200,255,0.4) 50%, rgba(0,0,0,0) 70%);
  filter: blur(15px);
  z-index: 1;
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.7; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1.05); }
}

/* 彩虹光晕效果 */
@keyframes rainbowGlow {
  0% { filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.9)); }
  20% { filter: drop-shadow(0 0 15px rgba(255, 165, 0, 0.9)); }
  40% { filter: drop-shadow(0 0 15px rgba(255, 255, 0, 0.9)); }
  60% { filter: drop-shadow(0 0 15px rgba(0, 255, 0, 0.9)); }
  80% { filter: drop-shadow(0 0 15px rgba(0, 0, 255, 0.9)); }
  100% { filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.9)); }
}

/* 霓虹发光效果 */
@keyframes neonGlow {
  0% { filter: drop-shadow(0 0 5px #fff) drop-shadow(0 0 15px #0ff) drop-shadow(0 0 25px #0ff); }
  50% { filter: drop-shadow(0 0 10px #fff) drop-shadow(0 0 25px #0ff) drop-shadow(0 0 40px #0ff); }
  100% { filter: drop-shadow(0 0 5px #fff) drop-shadow(0 0 15px #0ff) drop-shadow(0 0 25px #0ff); }
}
/* 脉冲蓝色光效果： */
@keyframes blueGlow {
  0% { filter: drop-shadow(0 0 8px rgba(0, 150, 255, 0.7)); }
  50% { filter: drop-shadow(0 0 20px rgba(0, 150, 255, 1)) drop-shadow(0 0 30px rgba(70, 200, 255, 0.9)); }
  100% { filter: drop-shadow(0 0 8px rgba(0, 150, 255, 0.7)); }
}

/* 多层光晕效果： */
/* 霓虹蓝光效果： */
/* 白色/银色光晕 */
/* 金色/黄色光晕： */
/* img {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.9)) 
          drop-shadow(0 0 20px rgba(70, 200, 255, 0.8)) 
          drop-shadow(0 0 30px rgba(120, 170, 255, 0.7));
}
img {
  filter: drop-shadow(0 0 10px #fff) 
          drop-shadow(0 0 20px #0af) 
          drop-shadow(0 0 40px #08f) 
          drop-shadow(0 0 60px #06f);
}
img {
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.9)) 
          drop-shadow(0 0 25px rgba(220, 220, 255, 0.8));
}
img {
  filter: drop-shadow(0 0 15px rgba(255, 230, 140, 0.9)) 
          drop-shadow(0 0 25px rgba(255, 200, 80, 0.8));
} */



/* img {
  animation: blueGlow 6s infinite;
} */
/* .logoGlow {
  mix-blend-mode: screen;
  filter: brightness(1.9) contrast(1.2);
} */

@keyframes sweepLight {
  0% {
    background: linear-gradient(90deg, 
      rgba(255,255,255,0) 0%, 
      rgba(255,255,255,0.9) 5%, 
      rgba(255,255,255,0) 10%);
    background-size: 200% 100%;
    background-position: -100% 0;
  }
  100% {
    background: linear-gradient(90deg, 
      rgba(255,255,255,0) 0%, 
      rgba(255,255,255,0.9) 5%, 
      rgba(255,255,255,0) 10%);
    background-size: 200% 100%;
    background-position: 200% 0;
  }
}



/* 添加新的发光动画类 */
@keyframes glowPulse {
  0% { filter: brightness(1) saturate(1); }
  50% { filter: brightness(3) saturate(2.5) drop-shadow(0 2px 10px rgba(175, 197, 241, 0.6)); }
  100% { filter: brightness(1) saturate(1); }
}

.logoSweep {
  position: relative;
  isolation: isolate;
  animation: glowPulse 6s infinite alternate;
}

/* 全局 Ant Design 组件圆角样式 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select .ant-select-selector,
.ant-input-number,
.ant-textarea {
  border-radius: 12px !important;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-focused,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused,
.ant-textarea:focus {
  border-radius: 12px !important;
}

.ant-btn {
  border-radius: 10px !important;
}

.ant-upload {
  border-radius: 10px !important;
}

.ant-upload-list-item {
  border-radius: 8px !important;
}

/* 模态框内的组件圆角 */
.ant-modal .ant-input,
.ant-modal .ant-input-affix-wrapper,
.ant-modal .ant-select .ant-select-selector,
.ant-modal .ant-input-number,
.ant-modal .ant-textarea {
  border-radius: 12px !important;
}
.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.first\:rounded-t-xl:first-child {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.last\:rounded-b-xl:last-child {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.focus-within\:border-blue-500:focus-within {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.hover\:w-\[100px\]:hover {
  width: 100px;
}
.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-x-1:hover {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-y-\[-20px\]:hover {
  --tw-translate-y: -20px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:\!border-0:hover {
  border-width: 0px !important;
}
.hover\:\!border-red-600:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1)) !important;
}
.hover\:border-blue-100:hover {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.hover\:border-green-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.hover\:border-green-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));
}
.hover\:border-indigo-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}
.hover\:border-indigo-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}
.hover\:border-pink-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(251 207 232 / var(--tw-border-opacity, 1));
}
.hover\:border-purple-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.hover\:\!bg-\[\#3d57a7\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(61 87 167 / var(--tw-bg-opacity, 1)) !important;
}
.hover\:\!bg-red-600:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1)) !important;
}
.hover\:bg-\[\#3d57a7\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(61 87 167 / var(--tw-bg-opacity, 1));
}
.hover\:bg-\[\#F6F7F8\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(246 247 248 / var(--tw-bg-opacity, 1));
}
.hover\:bg-amber-50\/30:hover {
  background-color: rgb(255 251 235 / 0.3);
}
.hover\:bg-black\/30:hover {
  background-color: rgb(0 0 0 / 0.3);
}
.hover\:bg-black\/40:hover {
  background-color: rgb(0 0 0 / 0.4);
}
.hover\:bg-black\/90:hover {
  background-color: rgb(0 0 0 / 0.9);
}
.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-50\/30:hover {
  background-color: rgb(239 246 255 / 0.3);
}
.hover\:bg-blue-50\/80:hover {
  background-color: rgb(239 246 255 / 0.8);
}
.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100\/50:hover {
  background-color: rgb(243 244 246 / 0.5);
}
.hover\:bg-gray-100\/80:hover {
  background-color: rgb(243 244 246 / 0.8);
}
.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200\/70:hover {
  background-color: rgb(229 231 235 / 0.7);
}
.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50\/80:hover {
  background-color: rgb(249 250 251 / 0.8);
}
.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-pink-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-50\/30:hover {
  background-color: rgb(254 242 242 / 0.3);
}
.hover\:bg-red-500\/30:hover {
  background-color: rgb(239 68 68 / 0.3);
}
.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}
.hover\:bg-white\/15:hover {
  background-color: rgb(255 255 255 / 0.15);
}
.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}
.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}
.hover\:bg-yellow-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.hover\:bg-opacity-90:hover {
  --tw-bg-opacity: 0.9;
}
.hover\:from-blue-100:hover {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-50\/30:hover {
  --tw-gradient-from: rgb(239 246 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-50\/50:hover {
  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-yellow-600:hover {
  --tw-gradient-from: #ca8a04 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(202 138 4 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-blue-700:hover {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.hover\:to-indigo-100:hover {
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}
.hover\:to-indigo-700:hover {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}
.hover\:to-orange-600:hover {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}
.hover\:to-white:hover {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.hover\:text-\[\#4766C2\]:hover {
  --tw-text-opacity: 1;
  color: rgb(71 102 194 / var(--tw-text-opacity, 1));
}
.hover\:text-\[\#5d7bd4\]:hover {
  --tw-text-opacity: 1;
  color: rgb(93 123 212 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.hover\:text-pink-500:hover {
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:text-white\/80:hover {
  color: rgb(255 255 255 / 0.8);
}
.hover\:text-white\/90:hover {
  color: rgb(255 255 255 / 0.9);
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:opacity-90:hover {
  opacity: 0.9;
}
.hover\:shadow:hover {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_2px_20px_rgb\(0\2c 0\2c 0\2c 0\.05\)\]:hover {
  --tw-shadow: 0 2px 20px rgb(0,0,0,0.05);
  --tw-shadow-colored: 0 2px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_4px_15px_rgb\(71\2c 102\2c 194\2c 0\.15\)\]:hover {
  --tw-shadow: 0 4px 15px rgb(71,102,194,0.15);
  --tw-shadow-colored: 0 4px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_4px_20px_rgb\(71\2c 102\2c 194\2c 0\.3\)\]:hover {
  --tw-shadow: 0 4px 20px rgb(71,102,194,0.3);
  --tw-shadow-colored: 0 4px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_8px_25px_rgb\(71\2c 102\2c 194\2c 0\.35\)\]:hover {
  --tw-shadow: 0 8px 25px rgb(71,102,194,0.35);
  --tw-shadow-colored: 0 8px 25px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-\[0_8px_30px_rgb\(71\2c 102\2c 194\2c 0\.2\)\]:hover {
  --tw-shadow: 0 8px 30px rgb(71,102,194,0.2);
  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-blue-500\/30:hover {
  --tw-shadow-color: rgb(59 130 246 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:shadow-blue-500\/40:hover {
  --tw-shadow-color: rgb(59 130 246 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.hover\:ring-2:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-blue-400:hover {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}
.focus\:\!border-0:focus {
  border-width: 0px !important;
}
.focus\:border-\[\#4766C2\]:focus {
  --tw-border-opacity: 1;
  border-color: rgb(71 102 194 / var(--tw-border-opacity, 1));
}
.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\:border-blue-500\/30:focus {
  border-color: rgb(59 130 246 / 0.3);
}
.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.focus\:border-transparent:focus {
  border-color: transparent;
}
.focus\:\!shadow-none:focus {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-\[\#4766C2\]:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(71 102 194 / var(--tw-ring-opacity, 1));
}
.focus\:ring-blue-100\/50:focus {
  --tw-ring-color: rgb(219 234 254 / 0.5);
}
.focus\:ring-blue-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}
.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\:ring-blue-500\/10:focus {
  --tw-ring-color: rgb(59 130 246 / 0.1);
}
.focus\:ring-blue-500\/40:focus {
  --tw-ring-color: rgb(59 130 246 / 0.4);
}
.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}
.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus\:ring-offset-blue-100:focus {
  --tw-ring-offset-color: #dbeafe;
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}
.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:bg-blue-400\/80:disabled {
  background-color: rgb(96 165 250 / 0.8);
}
.disabled\:bg-gray-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-gray-600:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.disabled\:from-gray-300:disabled {
  --tw-gradient-from: #d1d5db var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(209 213 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.disabled\:to-gray-400:disabled {
  --tw-gradient-to: #9ca3af var(--tw-gradient-to-position);
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.disabled\:opacity-60:disabled {
  opacity: 0.6;
}
.disabled\:opacity-80:disabled {
  opacity: 0.8;
}
.disabled\:hover\:scale-100:hover:disabled {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.disabled\:hover\:shadow-\[0_4px_15px_rgb\(71\2c 102\2c 194\2c 0\.2\)\]:hover:disabled {
  --tw-shadow: 0 4px 15px rgb(71,102,194,0.2);
  --tw-shadow-colored: 0 4px 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:hover\:shadow-none:hover:disabled {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:visible {
  visibility: visible;
}
.group:hover .group-hover\:-translate-x-\[60\%\] {
  --tw-translate-x: -60%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-0\.5 {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-y-\[110\%\] {
  --tw-translate-y: 110%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-\[2\] {
  --tw-scale-x: 2;
  --tw-scale-y: 2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-opacity-5 {
  --tw-bg-opacity: 0.05;
}
.group:hover .group-hover\:bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.group:hover .group-hover\:from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.group:hover .group-hover\:to-indigo-200 {
  --tw-gradient-to: #c7d2fe var(--tw-gradient-to-position);
}
.group:hover .group-hover\:to-indigo-500 {
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.group:hover .group-hover\:bg-clip-text {
  background-clip: text;
}
.group:hover .group-hover\:text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-transparent {
  color: transparent;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:opacity-15 {
  opacity: 0.15;
}
.group:hover .group-hover\:opacity-20 {
  opacity: 0.2;
}
.group:hover .group-hover\:ring-blue-100 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(219 234 254 / var(--tw-ring-opacity, 1));
}
.group:hover .group-hover\:drop-shadow-\[0_0_12px_rgba\(0\2c 0\2c 0\2c 0\.5\)\] {
  --tw-drop-shadow: drop-shadow(0 0 12px rgba(0,0,0,0.5));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:left-4 {
  left: 1rem;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:left-6 {
  left: 1.5rem;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:top-1\/2 {
  top: 50%;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:bg-transparent {
  background-color: transparent;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:text-blue-500\/80 {
  color: rgb(59 130 246 / 0.8);
}
.peer:focus ~ .peer-focus\:left-4 {
  left: 1rem;
}
.peer:focus ~ .peer-focus\:top-0 {
  top: 0px;
}
.peer:focus ~ .peer-focus\:-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:focus ~ .peer-focus\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.peer:focus ~ .peer-focus\:px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.peer:focus ~ .peer-focus\:text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}
.dark\:\!border-gray-700:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1)) !important;
}
.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-800\/30:is(.dark *) {
  background-color: rgb(31 41 55 / 0.3);
}
.dark\:bg-gray-800\/50:is(.dark *) {
  background-color: rgb(31 41 55 / 0.5);
}
.dark\:from-gray-800:is(.dark *) {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-900:is(.dark *) {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:via-gray-800:is(.dark *) {
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:to-gray-700:is(.dark *) {
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}
.dark\:to-gray-900:is(.dark *) {
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}
.dark\:to-gray-900\/80:is(.dark *) {
  --tw-gradient-to: rgb(17 24 39 / 0.8) var(--tw-gradient-to-position);
}
.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-600:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px) {

  .sm\:order-1 {
    order: 1;
  }

  .sm\:order-2 {
    order: 2;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .sm\:mb-4 {
    margin-bottom: 1rem;
  }

  .sm\:mr-1\.5 {
    margin-right: 0.375rem;
  }

  .sm\:mr-3 {
    margin-right: 0.75rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-28 {
    height: 7rem;
  }

  .sm\:h-\[300px\] {
    height: 300px;
  }

  .sm\:min-h-\[250px\] {
    min-height: 250px;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-\[260px\] {
    width: 260px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:max-w-\[500px\] {
    max-width: 500px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-0 {
    gap: 0px;
  }

  .sm\:space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
  }

  .sm\:p-3\.5 {
    padding: 0.875rem;
  }

  .sm\:p-4 {
    padding: 1rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .sm\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .sm\:pb-2 {
    padding-bottom: 0.5rem;
  }

  .sm\:pb-3 {
    padding-bottom: 0.75rem;
  }

  .sm\:pb-4 {
    padding-bottom: 1rem;
  }

  .sm\:pr-2 {
    padding-right: 0.5rem;
  }

  .sm\:pt-4 {
    padding-top: 1rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 768px) {

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-20 {
    margin-top: 5rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-16 {
    height: 4rem;
  }

  .md\:h-24 {
    height: 6rem;
  }

  .md\:h-\[400px\] {
    height: 400px;
  }

  .md\:min-h-\[300px\] {
    min-height: 300px;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-16 {
    width: 4rem;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-24 {
    width: 6rem;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-3\/5 {
    width: 60%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-t-0 {
    border-top-width: 0px;
  }

  .md\:p-3 {
    padding: 0.75rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-1\.5 {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 1024px) {

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .lg\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:h-7 {
    height: 1.75rem;
  }

  .lg\:h-\[450px\] {
    height: 450px;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-\[45\%\] {
    width: 45%;
  }

  .lg\:w-\[50\%\] {
    width: 50%;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:gap-2 {
    gap: 0.5rem;
  }

  .lg\:gap-3 {
    gap: 0.75rem;
  }

  .lg\:space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .lg\:border-l {
    border-left-width: 1px;
  }

  .lg\:border-t-0 {
    border-top-width: 0px;
  }

  .lg\:p-3 {
    padding: 0.75rem;
  }

  .lg\:p-4 {
    padding: 1rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pl-6 {
    padding-left: 1.5rem;
  }

  .lg\:pr-6 {
    padding-right: 1.5rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 1280px) {

  .xl\:block {
    display: block;
  }

  .xl\:h-\[500px\] {
    height: 500px;
  }

  .xl\:w-\[40\%\] {
    width: 40%;
  }

  .xl\:w-\[55\%\] {
    width: 55%;
  }

  .xl\:w-auto {
    width: auto;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:justify-start {
    justify-content: flex-start;
  }

  .xl\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .xl\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}
@media (min-width: 1536px) {

  .\32xl\:block {
    display: block;
  }

  .\32xl\:w-auto {
    width: auto;
  }

  .\32xl\:justify-start {
    justify-content: flex-start;
  }

  .\32xl\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}
.\[\&_\.ant-select-selection-item\]\:text-white .ant-select-selection-item {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}



/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/antd/dist/reset.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* stylelint-disable */
html,
body {
  width: 100%;
  height: 100%;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
  width: device-width;
}
body {
  margin: 0;
}
[tabindex='-1']:focus {
  outline: none;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
}
p {
  margin-top: 0;
  margin-bottom: 1em;
}
abbr[title],
abbr[data-original-title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline;
  text-decoration: underline dotted;
  border-bottom: 0;
  cursor: help;
}
address {
  margin-bottom: 1em;
  font-style: normal;
  line-height: inherit;
}
input[type='text'],
input[type='password'],
input[type='number'],
textarea {
  -webkit-appearance: none;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1em;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 500;
}
dd {
  margin-bottom: 0.5em;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1em;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
pre,
code,
kbd,
samp {
  font-size: 1em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}
pre {
  margin-top: 0;
  margin-bottom: 1em;
  overflow: auto;
}
figure {
  margin: 0 0 1em;
}
img {
  vertical-align: middle;
  border-style: none;
}
a,
area,
button,
[role='button'],
input:not([type='range']),
label,
select,
summary,
textarea {
  touch-action: manipulation;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75em;
  padding-bottom: 0.3em;
  text-align: left;
  caption-side: bottom;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type='button']::-moz-focus-inner,
[type='reset']::-moz-focus-inner,
[type='submit']::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type='radio'],
input[type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}
input[type='date'],
input[type='time'],
input[type='datetime-local'],
input[type='month'] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  margin: 0;
  padding: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  margin-bottom: 0.5em;
  padding: 0;
  color: inherit;
  font-size: 1.5em;
  line-height: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type='number']::-webkit-inner-spin-button,
[type='number']::-webkit-outer-spin-button {
  height: auto;
}
[type='search'] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type='search']::-webkit-search-cancel-button,
[type='search']::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
mark {
  padding: 0.2em;
  background-color: #feffe6;
}

