export declare class ValidationUtils {
    static validateTitle(title: string, fieldName?: string): void;
    static validatePermission(resourceCreatorId: number, currentUserId: number, action?: string): void;
    static validateRequiredId(id: any, fieldName: string): void;
    static validateCourseData(courseData: any): void;
    static validateSeriesData(seriesData: any): void;
    static validateTaskTemplateData(templateData: any): void;
    static validateAndNormalizePagination(page?: number, pageSize?: number): {
        page: number;
        pageSize: number;
    };
}
