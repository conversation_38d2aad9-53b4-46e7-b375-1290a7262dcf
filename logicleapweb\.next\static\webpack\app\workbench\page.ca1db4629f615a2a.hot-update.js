"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx":
/*!*************************************************************!*\
  !*** ./app/workbench/components/TemplateSelectionModal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _TemplatePickerModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplatePickerModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_task__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 导入API\n\n\n\n// 导入工具函数\n\nconst TemplateSelectionModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, actionType, selectedSchool, selectedClass } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredOption, setHoveredOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [focusedInput, setFocusedInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // 跟踪哪个输入框有焦点\n    // 输入框引用\n    const assignInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const distributeInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 移除教师能量相关状态，因为不需要检查教师能量池\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 学生相关状态\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentPointsMap, setStudentPointsMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [loadingStudentPoints, setLoadingStudentPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 存储模态框数据的状态\n    const [modalData, setModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"none\",\n        assignEnergyAmount: \"\",\n        distributeEnergyAmount: \"\",\n        selectedTemplate: null,\n        selectedStudents: []\n    });\n    // 输入验证错误状态\n    const [inputErrors, setInputErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        assignEnergyError: \"\",\n        distributeEnergyError: \"\"\n    });\n    // 当前步骤状态\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"template\");\n    // 发布任务相关状态\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        duration: \"1小时\",\n        startTime: \"\",\n        endTime: \"\"\n    });\n    // 持续时间选择器状态\n    const [showDurationSelector, setShowDurationSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 点击外部关闭持续时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (showDurationSelector && !target.closest(\".duration-field-container\")) {\n                setShowDurationSelector(false);\n            }\n        };\n        if (showDurationSelector) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationSelector\n    ]);\n    // 持续时间选择函数\n    const handleDurationSelect = (duration)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                duration\n            }));\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布任务加载状态\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const pageSize = 10;\n    // 阻止背景页面滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            // 保存原始的 overflow 样式\n            const originalStyle = window.getComputedStyle(document.body).overflow;\n            // 阻止背景滚动\n            document.body.style.overflow = \"hidden\";\n            return ()=>{\n                // 恢复原始样式\n                document.body.style.overflow = originalStyle;\n            };\n        }\n    }, [\n        isOpen\n    ]);\n    // 获取作品列表（分页懒加载）\n    const fetchWorksData = async function() {\n        let pageNum = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, isLoadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const currentState = {\n            works,\n            selectedWorkIds,\n            page,\n            hasMore,\n            loadingWorks,\n            loadingMore\n        };\n        const setState = (newState)=>{\n            if (newState.works !== undefined) setWorks(newState.works);\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n            if (newState.page !== undefined) setPage(newState.page);\n            if (newState.hasMore !== undefined) setHasMore(newState.hasMore);\n            if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);\n            if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);\n        };\n        await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchWorks)(pageNum, pageSize, isLoadMore, currentState, setState);\n    };\n    // 加载更多作品\n    const loadMoreWorksData = ()=>{\n        if (!loadingMore && hasMore) {\n            const nextPage = page + 1;\n            fetchWorksData(nextPage, true);\n        }\n    };\n    // 选择作品（支持多选）\n    const handleSelectWorkData = (workId)=>{\n        const setState = (newState)=>{\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleSelectWork)(workId, selectedWorkIds, setState);\n    };\n    // 创建鼠标处理函数的包装器\n    const handleMouseDownWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseDown)(e, setDragState);\n    };\n    const handleMouseMoveWrapper = (e)=>{\n        const dragState = {\n            isDragging,\n            dragStart\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseMove)(e, dragState);\n    };\n    const handleMouseUpWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseUp)(e, setDragState);\n    };\n    const handleMouseLeaveWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseLeave)(e, setDragState);\n    };\n    // 当切换到发布任务步骤且选择资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStep === \"publish\" && activeTab === \"resources\" && mounted) {\n            // 重置作品状态\n            setWorks([]);\n            setPage(1);\n            setHasMore(true);\n            setSelectedWorkIds([]);\n            setLoadingMore(false);\n            fetchWorksData(1, false);\n        }\n    }, [\n        currentStep,\n        activeTab,\n        mounted\n    ]);\n    // 清空所有数据的函数\n    const clearAllData = ()=>{\n        // 重置步骤状态\n        setCurrentStep(\"template\");\n        // 重置模态框数据\n        setModalData({\n            selectedDistribution: \"\",\n            assignEnergyAmount: \"\",\n            distributeEnergyAmount: \"\",\n            selectedTemplate: null,\n            selectedStudents: []\n        });\n        // 重置错误状态\n        setInputErrors({\n            assignEnergyError: \"\",\n            distributeEnergyError: \"\"\n        });\n        // 重置任务数据\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            duration: \"1小时\",\n            startTime: \"\",\n            endTime: \"\"\n        });\n        // 重置其他状态\n        setActiveTab(\"task\");\n        setWorks([]);\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setHoveredOption(\"\");\n        setIsTemplatePickerOpen(false);\n        setIsBatchUseKeyPackageModalOpen(false);\n        setPage(1);\n        setHasMore(true);\n        setLoadingMore(false);\n        // 重置学生相关数据\n        setStudents([]);\n        setStudentPointsMap(new Map());\n    };\n    // 监听模态框关闭，清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            clearAllData();\n        }\n    }, [\n        isOpen\n    ]);\n    // 组件卸载时清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearAllData();\n        };\n    }, []);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 移除获取教师可分配能量的函数，因为不需要检查教师能量池\n    // 获取班级学生列表和能量信息（并行处理）\n    const fetchClassStudentsData = async ()=>{\n        if (!(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || !mounted) return;\n        setLoadingStudentPoints(true); // 提前设置能量加载状态\n        try {\n            const studentsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchClassStudentsWithNotification)(selectedClass.id, notification);\n            setStudents(studentsData);\n            // 立即并行获取学生能量信息，不等待学生列表完全处理完\n            if (studentsData.length > 0) {\n                // 不等待，立即开始获取能量信息\n                const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(studentsData.map((s)=>s.userId), notification);\n                setStudentPointsMap(pointsMap);\n            }\n            setLoadingStudentPoints(false);\n        } catch (error) {\n            console.error(\"获取学生列表失败:\", error);\n            setStudents([]);\n            setLoadingStudentPoints(false);\n        }\n    };\n    // 移除获取教师能量的 useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedClass) {\n            fetchClassStudentsData();\n            // 重置模态框数据\n            setModalData({\n                selectedDistribution: isBlocksOnlyMode ? \"none\" : \"none\",\n                assignEnergyAmount: \"\",\n                distributeEnergyAmount: \"\",\n                selectedTemplate: null,\n                selectedStudents: []\n            });\n            // 重置错误状态\n            setInputErrors({\n                assignEnergyError: \"\",\n                distributeEnergyError: \"\"\n            });\n            // 禁用body滚动\n            document.body.style.overflow = \"hidden\";\n        } else {\n            // 恢复body滚动\n            document.body.style.overflow = \"\";\n        }\n        // 清理函数：组件卸载时恢复滚动\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen,\n        selectedClass,\n        mounted\n    ]);\n    // 当学生列表加载完成后，自动选择所有学生\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (students.length > 0) {\n            setModalData((prev)=>({\n                    ...prev,\n                    selectedStudents: students.map((s)=>s.userId)\n                }));\n        }\n    }, [\n        students\n    ]);\n    const distributionOptions = [\n        {\n            id: \"none\",\n            label: \"不分配\",\n            description: \"保持原有设置\",\n            hasInput: false\n        },\n        {\n            id: \"assign\",\n            label: \"分配\",\n            description: \"分配给学生\",\n            hasInput: true\n        },\n        {\n            id: \"distribute\",\n            label: \"分配至\",\n            description: \"分配到指定位置\",\n            hasInput: true\n        }\n    ];\n    // 判断是否为纯积木分配模式（不涉及能量）\n    const isBlocksOnlyMode = actionType === \"分配积木\";\n    // 判断是否为纯能量分配模式（不涉及积木）\n    const isEnergyOnlyMode = actionType === \"分配能量\";\n    const handleDistributionSelect = (optionId)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedDistribution: optionId\n            }));\n        // 自动聚焦到对应的输入框\n        setTimeout(()=>{\n            if (optionId === \"assign\" && assignInputRef.current) {\n                assignInputRef.current.focus();\n            } else if (optionId === \"distribute\" && distributeInputRef.current) {\n                distributeInputRef.current.focus();\n            }\n        }, 100); // 延迟一点确保输入框已经渲染\n    };\n    const handleTemplateSelect = (template)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: template\n            }));\n    };\n    const handleCancelTemplate = ()=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: null\n            }));\n    };\n    const handleTemplatePickerOpen = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    const handleTemplatePickerClose = ()=>{\n        setIsTemplatePickerOpen(false);\n    };\n    // 处理批量兑换密钥模态框\n    const handleBatchUseKeyPackageModalOpen = ()=>{\n        setIsBatchUseKeyPackageModalOpen(true);\n    };\n    const handleBatchUseKeyPackageModalClose = ()=>{\n        setIsBatchUseKeyPackageModalOpen(false);\n    };\n    const handleBatchUseKeyPackageSuccess = async ()=>{\n        // 兑换成功后重新获取学生能量信息\n        if (students.length > 0) {\n            const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(students.map((s)=>s.userId), notification);\n            setStudentPointsMap(pointsMap);\n        }\n        notification.success(\"密钥兑换成功！\");\n    };\n    // 获取当前选中分配方式对应的能量数值\n    const getCurrentEnergyAmount = ()=>{\n        if (modalData.selectedDistribution === \"assign\") {\n            return modalData.assignEnergyAmount;\n        } else if (modalData.selectedDistribution === \"distribute\") {\n            return modalData.distributeEnergyAmount;\n        }\n        return \"\";\n    };\n    // 计算所有学生的最低可分配能量\n    const getMinAvailablePoints = ()=>{\n        if (modalData.selectedStudents.length === 0) return 0;\n        const selectedStudentPoints = modalData.selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n        return Math.min(...selectedStudentPoints);\n    };\n    // 获取当前分配方式的提示信息\n    const getEnergyDisplayInfo = ()=>{\n        if (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") {\n            return {\n                label: \"最低可分配能量\",\n                value: getMinAvailablePoints()\n            };\n        }\n        return {\n            label: \"\",\n            value: 0\n        };\n    };\n    const handleNext = ()=>{\n        console.log(\"选择的分配方式:\", modalData.selectedDistribution);\n        console.log(\"选择的模板:\", modalData.selectedTemplate);\n        // 在纯积木分配模式时，跳过能量验证\n        if (!isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\")) {\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            console.log(\"分配能量数量:\", currentEnergyAmount);\n            // 检查是否有输入错误\n            const errorKey = modalData.selectedDistribution === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n            if (inputErrors[errorKey]) {\n                notification.error(\"请修正输入错误后再继续\");\n                return;\n            }\n            // 检查能量数量是否有效\n            const energyAmountNum = Number(currentEnergyAmount);\n            if (!currentEnergyAmount || energyAmountNum <= 0) {\n                notification.error(\"请输入有效的分配能量数量\");\n                return;\n            }\n            if (modalData.selectedDistribution === \"assign\") {\n                // \"分配\"按钮：检查选中学生的可分配能量是否足够\n                const insufficientStudents = modalData.selectedStudents.filter((studentId)=>{\n                    const studentAvailablePoints = studentPointsMap.get(studentId);\n                    return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;\n                });\n                if (insufficientStudents.length > 0) {\n                    const insufficientNames = insufficientStudents.map((studentId)=>{\n                        const student = students.find((s)=>s.userId === studentId);\n                        const availablePoints = studentPointsMap.get(studentId) || 0;\n                        return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(可分配: \").concat(availablePoints, \")\");\n                    }).join(\"、\");\n                    notification.error(\"积分不足：以下学生的可分配能量不足 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                    return;\n                }\n            } else if (modalData.selectedDistribution === \"distribute\") {\n                // \"分配至\"按钮：检查需要补充能量的学生\n                const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                    const currentPoints = studentPointsMap.get(studentId) || 0;\n                    return currentPoints < energyAmountNum;\n                });\n                if (studentsNeedingEnergy.length > 0) {\n                    // 检查这些学生是否有足够的可分配能量来达到目标值\n                    const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = energyAmountNum - currentPoints;\n                        const studentAvailablePoints = studentPointsMap.get(studentId);\n                        return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                    });\n                    if (insufficientStudents.length > 0) {\n                        const insufficientNames = insufficientStudents.map((studentId)=>{\n                            const student = students.find((s)=>s.userId === studentId);\n                            const currentPoints = studentPointsMap.get(studentId) || 0;\n                            const neededPoints = energyAmountNum - currentPoints;\n                            const availablePoints = studentPointsMap.get(studentId) || 0;\n                            return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(需要: \").concat(neededPoints, \", 可分配: \").concat(availablePoints, \")\");\n                        }).join(\"、\");\n                        notification.error(\"积分不足：以下学生无法达到目标能量值 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                        return;\n                    }\n                }\n            }\n        }\n        // 根据操作类型决定下一步\n        if (actionType === \"快速上课\") {\n            // 快速上课：切换到发布任务步骤\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n            setActiveTab(\"task\"); // 默认显示任务信息标签页\n        } else if (actionType === \"发布任务\") {\n            // 发布任务：直接切换到发布任务步骤（跳过能量和积木配置）\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n            setActiveTab(\"task\"); // 默认显示任务信息标签页\n        } else {\n            // 分配积木或分配能量：直接执行分配操作并关闭弹窗\n            console.log(\"\".concat(actionType, \"操作执行\"), {\n                selectedDistribution: modalData.selectedDistribution,\n                selectedTemplate: modalData.selectedTemplate,\n                selectedStudents: modalData.selectedStudents,\n                energyAmount: getCurrentEnergyAmount()\n            });\n            // 这里可以调用相应的API来执行分配操作\n            // TODO: 实现分配积木和分配能量的API调用\n            notification.success(\"\".concat(actionType, \"成功！\"));\n            clearAllData();\n            onClose();\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep === \"publish\") {\n            // 从发布任务步骤返回时重置持续时间\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\"\n                }));\n            setCurrentStep(\"template\");\n        } else {\n            onBack();\n        }\n    };\n    // 确认发布（与NewPublishTaskModal保持一致）\n    const handleConfirm = ()=>{\n        if (!taskData.taskName.trim()) {\n            notification.error(\"请输入任务名称\");\n            return;\n        }\n        const times = (0,_utils__WEBPACK_IMPORTED_MODULE_11__.getTaskTimes)(taskData.duration);\n        const finalTaskData = {\n            ...taskData,\n            startTime: times.startTime,\n            endTime: times.endTime,\n            selectedWorkIds,\n            attachments,\n            modalData\n        };\n        // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑\n        if (onConfirm) {\n            onConfirm(finalTaskData);\n        } else {\n            // 保留原有的发布逻辑作为后备\n            handlePublishTaskOk();\n        }\n    };\n    const handlePublishTaskOk = async ()=>{\n        // 防止重复点击\n        if (isPublishing) {\n            return;\n        }\n        try {\n            var _modalData_selectedTemplate;\n            setIsPublishing(true);\n            // 验证必填字段\n            if (!taskData.taskName.trim()) {\n                notification.error(\"请输入任务名称\");\n                setIsPublishing(false);\n                return;\n            }\n            console.log(\"发布任务:\", taskData);\n            console.log(\"选中的作品ID:\", selectedWorkIds);\n            console.log(\"模态框数据:\", modalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const user = userData ? JSON.parse(userData) : null;\n            const teacherId = user === null || user === void 0 ? void 0 : user.userId;\n            if (!teacherId) {\n                notification.error(\"未找到用户信息\");\n                setIsPublishing(false);\n                return;\n            }\n            // 处理时间\n            const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();\n            const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n            // 构建任务发布参数\n            const taskParams = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskType: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.TaskType.GRAPHIC,\n                priority: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.Priority.NORMAL,\n                startDate: startDate,\n                endDate: endDate,\n                taskContent: taskData.taskDescription || \"\",\n                attachments: attachments.map((file)=>file.name) || [],\n                isPublic: 1,\n                allowLateSubmission: false,\n                studentIds: modalData.selectedStudents,\n                classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                templateId: (_modalData_selectedTemplate = modalData.selectedTemplate) === null || _modalData_selectedTemplate === void 0 ? void 0 : _modalData_selectedTemplate.id,\n                workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim() !== \"\") || [] // 过滤空的自评项\n            };\n            console.log(\"任务发布参数:\", taskParams);\n            console.log(\"作品ID数组:\", taskParams.workIds);\n            // 准备并行请求数组\n            const requests = [];\n            // 1. 任务发布请求（必须执行）\n            requests.push(_lib_api_task__WEBPACK_IMPORTED_MODULE_9__[\"default\"].publishTask(taskParams));\n            // 2. 能量分配请求（如果需要）\n            let energyRequest = null;\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            if ((modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && currentEnergyAmount) {\n                const targetAmount = Number(currentEnergyAmount);\n                const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n                if (modalData.selectedDistribution === \"assign\") {\n                    // \"分配\"按钮：给每个学生分配固定数量的能量\n                    const studentExpiries = {};\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        studentExpiries[studentId] = defaultExpireTime;\n                    });\n                    energyRequest = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                        availablePoints: targetAmount,\n                        studentExpiries,\n                        remark: \"任务发布 - \".concat(taskData.taskName)\n                    });\n                    requests.push(energyRequest);\n                } else if (modalData.selectedDistribution === \"distribute\") {\n                    // \"分配至\"按钮：将学生能量补充到目标值\n                    const energyRequests = [];\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = targetAmount - currentPoints;\n                        // 只有当学生当前能量小于目标值时才分配\n                        if (neededPoints > 0) {\n                            const studentExpiries = {};\n                            studentExpiries[studentId] = defaultExpireTime;\n                            const request = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                                availablePoints: neededPoints,\n                                studentExpiries,\n                                remark: \"任务发布 - \".concat(taskData.taskName, \" (补充至\").concat(targetAmount, \")\")\n                            });\n                            energyRequests.push(request);\n                        }\n                    });\n                    // 将所有能量分配请求添加到主请求列表\n                    requests.push(...energyRequests);\n                }\n            }\n            // 3. 模板分配请求（如果需要）\n            let templateRequest = null;\n            if (modalData.selectedTemplate) {\n                const users = modalData.selectedStudents.map((studentId)=>({\n                        userId: studentId,\n                        roleId: 1,\n                        templateId: modalData.selectedTemplate.id,\n                        originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id\n                    }));\n                templateRequest = (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_10__.batchAddUserJoinRole)({\n                    users\n                });\n                requests.push(templateRequest);\n            }\n            // 并行执行所有请求\n            const results = await Promise.allSettled(requests);\n            // 处理任务发布结果\n            const taskResult = results[0];\n            if (taskResult.status === \"fulfilled\" && taskResult.value.data.code === 200) {\n                // 显示成功发布任务的提示\n                notification.success(\"\\uD83C\\uDF89 任务发布成功！学生可以开始学习了\");\n            } else {\n                const errorMsg = taskResult.status === \"fulfilled\" ? taskResult.value.data.message || \"任务发布失败\" : \"任务发布失败\";\n                notification.error(errorMsg);\n                setIsPublishing(false);\n                return; // 任务发布失败则直接返回\n            }\n            // 处理能量分配结果\n            let resultIndex = 1;\n            if (energyRequest) {\n                const energyResult = results[resultIndex];\n                if (energyResult.status === \"fulfilled\" && energyResult.value.data.code === 200) {\n                    notification.success(\"能量分配完成！\");\n                } else {\n                    console.error(\"能量分配失败:\", energyResult);\n                    notification.warning(\"能量分配失败\");\n                }\n                resultIndex++;\n            }\n            // 处理模板分配结果\n            if (templateRequest) {\n                const templateResult = results[resultIndex];\n                if (templateResult.status === \"fulfilled\" && templateResult.value.data.code === 200) {\n                    notification.success(\"模板分配完成！\");\n                } else {\n                    console.error(\"模板分配失败:\", templateResult);\n                    notification.warning(\"模板分配失败\");\n                }\n            }\n            // 延迟关闭弹窗，让用户能看到成功提示\n            setTimeout(()=>{\n                setIsPublishing(false);\n                clearAllData();\n                onClose();\n            }, 800);\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n            setIsPublishing(false);\n        }\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted || !isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        onWheel: (e)=>{\n            // 只阻止事件传播，依赖CSS控制滚动行为\n            e.stopPropagation();\n        },\n        onTouchMove: (e)=>{\n            // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: ()=>{\n                            clearAllData();\n                            onClose();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content template-selection-modal\",\n                        \"data-step\": currentStep,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step completed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"选择班级\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"template\" ? \"active\" : \"completed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: actionType === \"分配积木\" ? \"分配积木\" : actionType === \"分配能量\" ? \"分配能量\" : \"能量和积木\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    actionType === \"快速上课\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    actionType === \"发布任务\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content-body\",\n                                children: [\n                                    currentStep === \"template\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-title\",\n                                                    children: \"为学生分配能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: [\n                                                    !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"distribution-options\",\n                                                        children: distributionOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"distribution-card \".concat(modalData.selectedDistribution === option.id ? \"selected\" : \"\"),\n                                                                onClick: ()=>handleDistributionSelect(option.id),\n                                                                onMouseEnter: ()=>setHoveredOption(option.id),\n                                                                onMouseLeave: ()=>setHoveredOption(\"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"distribution-label\",\n                                                                        children: (()=>{\n                                                                            const currentAmount = option.id === \"assign\" ? modalData.assignEnergyAmount : option.id === \"distribute\" ? modalData.distributeEnergyAmount : \"\";\n                                                                            return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id ? \"\".concat(option.label, \" \").concat(currentAmount, \"能量\") : option.label;\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-container\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ref: option.id === \"assign\" ? assignInputRef : distributeInputRef,\n                                                                                type: \"number\",\n                                                                                className: \"energy-input\",\n                                                                                placeholder: option.id === \"assign\" ? \"输入能量\" : \"输入目标值\",\n                                                                                value: option.id === \"assign\" ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount,\n                                                                                min: \"1\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    const updateKey = option.id === \"assign\" ? \"assignEnergyAmount\" : \"distributeEnergyAmount\";\n                                                                                    const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                    // 清除之前的错误\n                                                                                    setInputErrors((prev)=>({\n                                                                                            ...prev,\n                                                                                            [errorKey]: \"\"\n                                                                                        }));\n                                                                                    // 允许空值或正整数\n                                                                                    if (value === \"\") {\n                                                                                        setModalData((prev)=>({\n                                                                                                ...prev,\n                                                                                                [updateKey]: value\n                                                                                            }));\n                                                                                    } else {\n                                                                                        const numValue = Number(value);\n                                                                                        if (Number.isInteger(numValue)) {\n                                                                                            if (numValue < 1) {\n                                                                                                // 设置错误提示\n                                                                                                setInputErrors((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [errorKey]: \"输入能量不能低于1\"\n                                                                                                    }));\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                            } else {\n                                                                                                // 有效输入\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                                // 输入数字时自动选中当前悬停的分配按钮\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        selectedDistribution: option.id\n                                                                                                    }));\n                                                                                            }\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    // 点击输入框时自动选中当前悬停的分配按钮\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onFocus: ()=>{\n                                                                                    setFocusedInput(option.id);\n                                                                                    // 聚焦时也自动选中分配选项\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    setFocusedInput(\"\");\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 904,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            (()=>{\n                                                                                const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                const errorMessage = inputErrors[errorKey];\n                                                                                return errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: \"#ef4444\",\n                                                                                        fontSize: \"12px\",\n                                                                                        marginTop: \"4px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: errorMessage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 29\n                                                                                }, undefined);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, option.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 19\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && modalData.selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-available-energy\",\n                                                        children: loadingStudentPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#6b7280\",\n                                                                fontStyle: \"italic\"\n                                                            },\n                                                            children: \"⏳ 正在获取能量信息...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, undefined) : (()=>{\n                                                            const displayInfo = getEnergyDisplayInfo();\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#64748b\",\n                                                                            fontSize: \"12px\"\n                                                                        },\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 986,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            displayInfo.label,\n                                                                            \": \",\n                                                                            displayInfo.value\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && getCurrentEnergyAmount() && !loadingStudentPoints && (()=>{\n                                                        const energyAmountNum = Number(getCurrentEnergyAmount());\n                                                        // 根据分配方式进行不同的验证\n                                                        let shouldShowError = false;\n                                                        let errorMessage = \"\";\n                                                        if (modalData.selectedDistribution === \"assign\") {\n                                                            const minAvailable = getMinAvailablePoints();\n                                                            if (energyAmountNum > minAvailable) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"可分配积分不足\";\n                                                            }\n                                                        } else if (modalData.selectedDistribution === \"distribute\") {\n                                                            // 对于\"分配至\"，检查是否有学生无法达到目标值\n                                                            const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                return currentPoints < energyAmountNum;\n                                                            });\n                                                            const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                const neededPoints = energyAmountNum - currentPoints;\n                                                                const studentAvailablePoints = studentPointsMap.get(studentId);\n                                                                return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                                                            });\n                                                            if (insufficientStudents.length > 0) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"部分学生积分不足以达到目标值\";\n                                                            }\n                                                        }\n                                                        if (shouldShowError) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: \"#fef2f2\",\n                                                                    border: \"1px solid #fecaca\",\n                                                                    borderRadius: \"8px\",\n                                                                    padding: \"12px 16px\",\n                                                                    marginTop: \"8px\",\n                                                                    marginBottom: \"12px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"space-between\",\n                                                                    gap: \"12px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: \"8px\",\n                                                                            flex: 1\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: \"500\"\n                                                                            },\n                                                                            children: [\n                                                                                \"⚠️ \",\n                                                                                errorMessage\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1050,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        style: {\n                                                                            background: \"#f97316\",\n                                                                            color: \"white\",\n                                                                            border: \"none\",\n                                                                            borderRadius: \"6px\",\n                                                                            padding: \"6px 12px\",\n                                                                            fontSize: \"12px\",\n                                                                            fontWeight: \"500\",\n                                                                            cursor: \"pointer\",\n                                                                            transition: \"all 0.2s ease\",\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.background = \"#ea580c\";\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.background = \"#f97316\";\n                                                                        },\n                                                                        onClick: ()=>{\n                                                                            handleBatchUseKeyPackageModalOpen();\n                                                                        },\n                                                                        children: \"兑换密钥\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        }\n                                                        return null;\n                                                    })(),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"section-title\",\n                                                        children: \"为学生分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-selection-area\",\n                                                        children: modalData.selectedTemplate ? // 已选择模板时显示模板信息\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-selected\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-icon\",\n                                                                    children: \"⭐\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-name\",\n                                                                            children: modalData.selectedTemplate.templateName || modalData.selectedTemplate.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1105,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-label\",\n                                                                            children: \"已选择模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1108,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1104,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"change-template-btn\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: \"更换\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1110,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"cancel-template-btn\",\n                                                                    onClick: handleCancelTemplate,\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 21\n                                                        }, undefined) : // 未选择模板时显示选择选项\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-options\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-none selected\",\n                                                                    onClick: ()=>{},\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"不分配模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1132,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"保持原有设置\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1133,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-select\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"\\uD83E\\uDDE9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1140,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"选择模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1142,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"为用户提供积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1143,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : // 发布任务步骤的内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"publish-task-content\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"tab-switcher\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"task\"),\n                                                        children: \"任务信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"resources\"),\n                                                        children: \"资源与附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1163,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"task-info-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"任务名称\",\n                                                                value: taskData.taskName,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskName: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                className: \"form-textarea\",\n                                                                placeholder: \"任务描述\",\n                                                                value: taskData.taskDescription,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskDescription: e.target.value\n                                                                        })),\n                                                                rows: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"self-assessment-section\",\n                                                                children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"add-self-assessment-btn\",\n                                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                                ...prev,\n                                                                                selfAssessmentItems: [\n                                                                                    \"\"\n                                                                                ]\n                                                                            })),\n                                                                    children: \"添加自评项\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1197,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label\",\n                                                                            children: \"自评项\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1206,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"self-assessment-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        className: \"form-input\",\n                                                                                        placeholder: \"自评项 \".concat(index + 1),\n                                                                                        value: item,\n                                                                                        onChange: (e)=>{\n                                                                                            const newItems = [\n                                                                                                ...taskData.selfAssessmentItems\n                                                                                            ];\n                                                                                            newItems[index] = e.target.value;\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"remove-btn\",\n                                                                                        onClick: ()=>{\n                                                                                            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        },\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1220,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1208,\n                                                                                columnNumber: 31\n                                                                            }, undefined)),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"add-btn\",\n                                                                            onClick: ()=>setTaskData((prev)=>({\n                                                                                        ...prev,\n                                                                                        selfAssessmentItems: [\n                                                                                            ...prev.selfAssessmentItems,\n                                                                                            \"\"\n                                                                                        ]\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"14px\"\n                                                                                    },\n                                                                                    children: \"➕\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"添加自评项\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1232,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"time-settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"form-label\",\n                                                                        children: \"任务持续时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"duration-options\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1小时\"),\n                                                                                children: \"1小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1250,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"3小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"3小时\"),\n                                                                                children: \"3小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1256,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1天\"),\n                                                                                children: \"1天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1262,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"7天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"7天\"),\n                                                                                children: \"7天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1268,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1个月\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1个月\"),\n                                                                                children: \"1个月\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"duration-hint\",\n                                                                        children: \"任务将从创建时开始，持续所选时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1281,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"resources-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"works-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"选择作品\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"help-text\",\n                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1289,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative works-scroll-wrapper\",\n                                                                    onWheel: _utils__WEBPACK_IMPORTED_MODULE_11__.handleWheelScroll,\n                                                                    onMouseDown: handleMouseDownWrapper,\n                                                                    onMouseMove: handleMouseMoveWrapper,\n                                                                    onMouseUp: handleMouseUpWrapper,\n                                                                    onMouseLeave: handleMouseLeaveWrapper,\n                                                                    style: {\n                                                                        minHeight: \"200px\",\n                                                                        cursor: \"grab\",\n                                                                        userSelect: \"none\"\n                                                                    },\n                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"loading-container\",\n                                                                        style: {\n                                                                            minHeight: \"200px\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"loading-spinner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1308,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"加载中...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1309,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"works-horizontal-scroll\",\n                                                                        children: [\n                                                                            works.sort((a, b)=>{\n                                                                                const aSelected = selectedWorkIds.includes(a.id);\n                                                                                const bSelected = selectedWorkIds.includes(b.id);\n                                                                                // 已选中的排在前面\n                                                                                if (aSelected && !bSelected) return -1;\n                                                                                if (!aSelected && bSelected) return 1;\n                                                                                return 0;\n                                                                            }).map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                    onClick: ()=>handleSelectWorkData(work.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-preview\",\n                                                                                            children: [\n                                                                                                work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fixImageUrl)(work.coverImage || work.screenShotImage),\n                                                                                                    alt: work.title,\n                                                                                                    className: \"work-image\",\n                                                                                                    onError: (e)=>{\n                                                                                                        e.currentTarget.style.display = \"none\";\n                                                                                                        const nextElement = e.currentTarget.nextElementSibling;\n                                                                                                        if (nextElement) {\n                                                                                                            nextElement.style.display = \"flex\";\n                                                                                                        }\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1332,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined) : null,\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"work-placeholder\",\n                                                                                                    style: {\n                                                                                                        display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"24\",\n                                                                                                        height: \"24\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1347,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z\",\n                                                                                                                fill: \"currentColor\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1348,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M21 15L16 10L11 15H21Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\",\n                                                                                                                strokeLinejoin: \"round\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1349,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1346,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1345,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"selection-indicator \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"12\",\n                                                                                                        height: \"12\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M9 12L11 14L15 10\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                            lineNumber: 1355,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1354,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1353,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1330,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-info\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"work-title\",\n                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1361,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1360,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, work.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1324,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)),\n                                                                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"load-more-container\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"load-more-btn\",\n                                                                                    onClick: loadMoreWorksData,\n                                                                                    disabled: loadingMore,\n                                                                                    children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"loading-spinner-small\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1375,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载中...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1376,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载更多\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1380,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                width: \"16\",\n                                                                                                height: \"16\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                fill: \"none\",\n                                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M12 5V19M5 12L12 19L19 12\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    strokeWidth: \"2\",\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1382,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1381,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1368,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1367,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-text\",\n                                                                            children: \"作品列表\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1392,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1290,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"attachments-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"附件上传\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            multiple: true,\n                                                                            accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                                            onChange: (e)=>{\n                                                                                if (e.target.files) {\n                                                                                    const files = Array.from(e.target.files);\n                                                                                    const validFiles = [];\n                                                                                    const invalidFiles = [];\n                                                                                    // 支持的文件格式\n                                                                                    const allowedTypes = [\n                                                                                        \"image/jpeg\",\n                                                                                        \"image/jpg\",\n                                                                                        \"image/png\",\n                                                                                        \"image/gif\",\n                                                                                        \"application/pdf\",\n                                                                                        \"application/msword\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                                                                                        \"application/vnd.ms-excel\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                                                                                        \"application/vnd.ms-powerpoint\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                                                                                        \"text/plain\"\n                                                                                    ];\n                                                                                    // 文件扩展名检查（作为备用验证）\n                                                                                    const allowedExtensions = [\n                                                                                        \".jpg\",\n                                                                                        \".jpeg\",\n                                                                                        \".png\",\n                                                                                        \".gif\",\n                                                                                        \".pdf\",\n                                                                                        \".doc\",\n                                                                                        \".docx\",\n                                                                                        \".xls\",\n                                                                                        \".xlsx\",\n                                                                                        \".ppt\",\n                                                                                        \".pptx\",\n                                                                                        \".txt\"\n                                                                                    ];\n                                                                                    files.forEach((file)=>{\n                                                                                        var _file_name_split_pop;\n                                                                                        // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）\n                                                                                        if (file.size > 10 * 1024 * 1024) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：文件大小超过10MB\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        // 检查文件类型\n                                                                                        const fileExtension = \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n                                                                                        const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);\n                                                                                        if (!isValidType) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：不支持的文件格式\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        validFiles.push(file);\n                                                                                    });\n                                                                                    // 添加有效文件\n                                                                                    if (validFiles.length > 0) {\n                                                                                        setAttachments((prev)=>[\n                                                                                                ...prev,\n                                                                                                ...validFiles\n                                                                                            ]);\n                                                                                    }\n                                                                                    // 显示错误信息\n                                                                                    if (invalidFiles.length > 0) {\n                                                                                        alert(\"以下文件无法上传：\\n\".concat(invalidFiles.join(\"\\n\")));\n                                                                                    }\n                                                                                    // 重置input的value，确保可以重复选择相同文件\n                                                                                    e.target.value = \"\";\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                display: \"none\"\n                                                                            },\n                                                                            id: \"file-upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1401,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"file-upload\",\n                                                                            className: \"upload-btn\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1460,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"file-format-info\",\n                                                                            children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1463,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachments-list\",\n                                                                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"file-name\",\n                                                                                    children: file.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1471,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setAttachments((prev)=>prev.filter((_, i)=>i !== index)),\n                                                                                    className: \"remove-attachment-btn\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1472,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1470,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1468,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1398,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1286,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1171,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"prev-btn\",\n                                                onClick: handlePrevious,\n                                                children: \"上一步\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1491,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"next-btn \".concat(currentStep === \"template\" && (actionType === \"发布任务\" ? true // 发布任务模式下直接启用按钮\n                                                 : isEnergyOnlyMode ? modalData.selectedDistribution : isBlocksOnlyMode ? modalData.selectedTemplate : modalData.selectedTemplate || modalData.selectedDistribution) || currentStep === \"publish\" && taskData.taskName.trim() && !isPublishing ? \"enabled\" : \"disabled\", \" \").concat(isPublishing ? \"publishing\" : \"\"),\n                                                onClick: currentStep === \"template\" ? handleNext : handleConfirm,\n                                                disabled: currentStep === \"template\" ? isEnergyOnlyMode ? !modalData.selectedDistribution : isBlocksOnlyMode ? !modalData.selectedTemplate : !modalData.selectedTemplate && !modalData.selectedDistribution : !taskData.taskName.trim() || isPublishing,\n                                                children: currentStep === \"template\" ? actionType === \"快速上课\" ? \"下一步\" : actionType === \"发布任务\" ? \"发布任务\" : \"分配\" : isPublishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"publishing-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1525,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"发布中...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1524,\n                                                    columnNumber: 19\n                                                }, undefined) : \"开始上课\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1494,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1490,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 833,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: handleTemplatePickerClose,\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1537,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalOpen,\n                selectedStudentIds: modalData.selectedStudents,\n                students: students,\n                onClose: handleBatchUseKeyPackageModalClose,\n                onSuccess: handleBatchUseKeyPackageSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1544,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n        lineNumber: 822,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateSelectionModal, \"3fkWfUC8OKkJ1JgoGu0y/qNfEA0=\");\n_c = TemplateSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"TemplateSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\n"));

/***/ })

});