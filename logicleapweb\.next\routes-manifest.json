{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/:path*", "headers": [{"key": "Authorization", "value": ":authorization"}, {"key": "cookie", "value": ":cookie"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/CommonTest", "regex": "^/CommonTest(?:/)?$", "routeKeys": {}, "namedRegex": "^/CommonTest(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/activity", "regex": "^/activity(?:/)?$", "routeKeys": {}, "namedRegex": "^/activity(?:/)?$"}, {"page": "/activity/festival", "regex": "^/activity/festival(?:/)?$", "routeKeys": {}, "namedRegex": "^/activity/festival(?:/)?$"}, {"page": "/admin-space", "regex": "^/admin\\-space(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin\\-space(?:/)?$"}, {"page": "/admin-space/activity-works", "regex": "^/admin\\-space/activity\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin\\-space/activity\\-works(?:/)?$"}, {"page": "/ai-drawing", "regex": "^/ai\\-drawing(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-drawing(?:/)?$"}, {"page": "/buy", "regex": "^/buy(?:/)?$", "routeKeys": {}, "namedRegex": "^/buy(?:/)?$"}, {"page": "/class-space", "regex": "^/class\\-space(?:/)?$", "routeKeys": {}, "namedRegex": "^/class\\-space(?:/)?$"}, {"page": "/create", "regex": "^/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/create(?:/)?$"}, {"page": "/demo/class-projects", "regex": "^/demo/class\\-projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo/class\\-projects(?:/)?$"}, {"page": "/discover", "regex": "^/discover(?:/)?$", "routeKeys": {}, "namedRegex": "^/discover(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/learn", "regex": "^/learn(?:/)?$", "routeKeys": {}, "namedRegex": "^/learn(?:/)?$"}, {"page": "/loading", "regex": "^/loading(?:/)?$", "routeKeys": {}, "namedRegex": "^/loading(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/manifest.json", "regex": "^/manifest\\.json(?:/)?$", "routeKeys": {}, "namedRegex": "^/manifest\\.json(?:/)?$"}, {"page": "/my-works", "regex": "^/my\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-works(?:/)?$"}, {"page": "/operation-space", "regex": "^/operation\\-space(?:/)?$", "routeKeys": {}, "namedRegex": "^/operation\\-space(?:/)?$"}, {"page": "/recharge", "regex": "^/recharge(?:/)?$", "routeKeys": {}, "namedRegex": "^/recharge(?:/)?$"}, {"page": "/resources", "regex": "^/resources(?:/)?$", "routeKeys": {}, "namedRegex": "^/resources(?:/)?$"}, {"page": "/square", "regex": "^/square(?:/)?$", "routeKeys": {}, "namedRegex": "^/square(?:/)?$"}, {"page": "/teacher-space", "regex": "^/teacher\\-space(?:/)?$", "routeKeys": {}, "namedRegex": "^/teacher\\-space(?:/)?$"}, {"page": "/test-token-refresh", "regex": "^/test\\-token\\-refresh(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-token\\-refresh(?:/)?$"}, {"page": "/welcome", "regex": "^/welcome(?:/)?$", "routeKeys": {}, "namedRegex": "^/welcome(?:/)?$"}, {"page": "/workbench", "regex": "^/workbench(?:/)?$", "routeKeys": {}, "namedRegex": "^/workbench(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/api/:path*", "destination": "http://127.0.0.1:8003/api/:path*", "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/editor", "destination": "http://localhost:9100/", "regex": "^/editor(?:/)?$"}, {"source": "/editor/:path*", "destination": "http://localhost:9100/:path*", "regex": "^/editor(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/socket.io/:path*", "destination": "http://localhost:9100/socket.io/:path*", "regex": "^/socket\\.io(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/libs/:path*", "destination": "http://localhost:9100/libs/:path*", "regex": "^/libs(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/token/:path*", "destination": "http://localhost:9100/token/:path*", "regex": "^/token(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/js/:path*", "destination": "http://localhost:9100/js/:path*", "regex": "^/js(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/logicleap", "destination": "https://www.logicleapai.cn/", "regex": "^/logicleap(?:/)?$"}, {"source": "/static/:path*", "destination": "https://www.logicleapai.cn/static/:path*", "regex": "^/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/js/:path*", "destination": "https://www.logicleapai.cn/js/:path*", "regex": "^/js(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/gui.js", "destination": "https://www.logicleapai.cn/gui.js", "regex": "^/gui\\.js(?:/)?$"}, {"source": "/assets/:path*", "destination": "https://www.logicleapai.cn/assets/:path*", "regex": "^/assets(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/logicleap/:path*", "destination": "https://www.logicleapai.cn/:path*", "regex": "^/logicleap(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}