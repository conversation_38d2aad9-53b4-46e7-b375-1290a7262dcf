"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx":
/*!*************************************************************!*\
  !*** ./app/workbench/components/TemplateSelectionModal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _TemplatePickerModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplatePickerModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_task__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 导入API\n\n\n\n// 导入工具函数\n\nconst TemplateSelectionModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, actionType, selectedSchool, selectedClass } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredOption, setHoveredOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [focusedInput, setFocusedInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // 跟踪哪个输入框有焦点\n    // 输入框引用\n    const assignInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const distributeInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 移除教师能量相关状态，因为不需要检查教师能量池\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 学生相关状态\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentPointsMap, setStudentPointsMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [loadingStudentPoints, setLoadingStudentPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 存储模态框数据的状态\n    const [modalData, setModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"none\",\n        assignEnergyAmount: \"\",\n        distributeEnergyAmount: \"\",\n        selectedTemplate: null,\n        selectedStudents: []\n    });\n    // 输入验证错误状态\n    const [inputErrors, setInputErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        assignEnergyError: \"\",\n        distributeEnergyError: \"\"\n    });\n    // 当前步骤状态\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"template\");\n    // 发布任务相关状态\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        duration: \"1小时\",\n        startTime: \"\",\n        endTime: \"\"\n    });\n    // 持续时间选择器状态\n    const [showDurationSelector, setShowDurationSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 点击外部关闭持续时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (showDurationSelector && !target.closest(\".duration-field-container\")) {\n                setShowDurationSelector(false);\n            }\n        };\n        if (showDurationSelector) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationSelector\n    ]);\n    // 持续时间选择函数\n    const handleDurationSelect = (duration)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                duration\n            }));\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布任务加载状态\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const pageSize = 10;\n    // 阻止背景页面滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            // 保存原始的 overflow 样式\n            const originalStyle = window.getComputedStyle(document.body).overflow;\n            // 阻止背景滚动\n            document.body.style.overflow = \"hidden\";\n            return ()=>{\n                // 恢复原始样式\n                document.body.style.overflow = originalStyle;\n            };\n        }\n    }, [\n        isOpen\n    ]);\n    // 获取作品列表（分页懒加载）\n    const fetchWorksData = async function() {\n        let pageNum = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, isLoadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const currentState = {\n            works,\n            selectedWorkIds,\n            page,\n            hasMore,\n            loadingWorks,\n            loadingMore\n        };\n        const setState = (newState)=>{\n            if (newState.works !== undefined) setWorks(newState.works);\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n            if (newState.page !== undefined) setPage(newState.page);\n            if (newState.hasMore !== undefined) setHasMore(newState.hasMore);\n            if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);\n            if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);\n        };\n        await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchWorks)(pageNum, pageSize, isLoadMore, currentState, setState);\n    };\n    // 加载更多作品\n    const loadMoreWorksData = ()=>{\n        if (!loadingMore && hasMore) {\n            const nextPage = page + 1;\n            fetchWorksData(nextPage, true);\n        }\n    };\n    // 选择作品（支持多选）\n    const handleSelectWorkData = (workId)=>{\n        const setState = (newState)=>{\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleSelectWork)(workId, selectedWorkIds, setState);\n    };\n    // 创建鼠标处理函数的包装器\n    const handleMouseDownWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseDown)(e, setDragState);\n    };\n    const handleMouseMoveWrapper = (e)=>{\n        const dragState = {\n            isDragging,\n            dragStart\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseMove)(e, dragState);\n    };\n    const handleMouseUpWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseUp)(e, setDragState);\n    };\n    const handleMouseLeaveWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseLeave)(e, setDragState);\n    };\n    // 当切换到发布任务步骤且选择资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStep === \"publish\" && activeTab === \"resources\" && mounted) {\n            // 重置作品状态\n            setWorks([]);\n            setPage(1);\n            setHasMore(true);\n            setSelectedWorkIds([]);\n            setLoadingMore(false);\n            fetchWorksData(1, false);\n        }\n    }, [\n        currentStep,\n        activeTab,\n        mounted\n    ]);\n    // 清空所有数据的函数\n    const clearAllData = ()=>{\n        // 重置步骤状态\n        setCurrentStep(\"template\");\n        // 重置模态框数据\n        setModalData({\n            selectedDistribution: \"\",\n            assignEnergyAmount: \"\",\n            distributeEnergyAmount: \"\",\n            selectedTemplate: null,\n            selectedStudents: []\n        });\n        // 重置错误状态\n        setInputErrors({\n            assignEnergyError: \"\",\n            distributeEnergyError: \"\"\n        });\n        // 重置任务数据\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            duration: \"1小时\",\n            startTime: \"\",\n            endTime: \"\"\n        });\n        // 重置其他状态\n        setActiveTab(\"task\");\n        setWorks([]);\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setHoveredOption(\"\");\n        setIsTemplatePickerOpen(false);\n        setIsBatchUseKeyPackageModalOpen(false);\n        setPage(1);\n        setHasMore(true);\n        setLoadingMore(false);\n        // 重置学生相关数据\n        setStudents([]);\n        setStudentPointsMap(new Map());\n    };\n    // 监听模态框关闭，清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            clearAllData();\n        }\n    }, [\n        isOpen\n    ]);\n    // 组件卸载时清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearAllData();\n        };\n    }, []);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 移除获取教师可分配能量的函数，因为不需要检查教师能量池\n    // 获取班级学生列表和能量信息（并行处理）\n    const fetchClassStudentsData = async ()=>{\n        if (!(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || !mounted) return;\n        setLoadingStudentPoints(true); // 提前设置能量加载状态\n        try {\n            const studentsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchClassStudentsWithNotification)(selectedClass.id, notification);\n            setStudents(studentsData);\n            // 立即并行获取学生能量信息，不等待学生列表完全处理完\n            if (studentsData.length > 0) {\n                // 不等待，立即开始获取能量信息\n                const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(studentsData.map((s)=>s.userId), notification);\n                setStudentPointsMap(pointsMap);\n            }\n            setLoadingStudentPoints(false);\n        } catch (error) {\n            console.error(\"获取学生列表失败:\", error);\n            setStudents([]);\n            setLoadingStudentPoints(false);\n        }\n    };\n    // 移除获取教师能量的 useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedClass) {\n            fetchClassStudentsData();\n            // 重置模态框数据\n            setModalData({\n                selectedDistribution: isBlocksOnlyMode ? \"none\" : \"none\",\n                assignEnergyAmount: \"\",\n                distributeEnergyAmount: \"\",\n                selectedTemplate: null,\n                selectedStudents: []\n            });\n            // 重置错误状态\n            setInputErrors({\n                assignEnergyError: \"\",\n                distributeEnergyError: \"\"\n            });\n            // 禁用body滚动\n            document.body.style.overflow = \"hidden\";\n        } else {\n            // 恢复body滚动\n            document.body.style.overflow = \"\";\n        }\n        // 清理函数：组件卸载时恢复滚动\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen,\n        selectedClass,\n        mounted\n    ]);\n    // 当学生列表加载完成后，自动选择所有学生\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (students.length > 0) {\n            setModalData((prev)=>({\n                    ...prev,\n                    selectedStudents: students.map((s)=>s.userId)\n                }));\n        }\n    }, [\n        students\n    ]);\n    const distributionOptions = [\n        {\n            id: \"none\",\n            label: \"不分配\",\n            description: \"保持原有设置\",\n            hasInput: false\n        },\n        {\n            id: \"assign\",\n            label: \"分配\",\n            description: \"分配给学生\",\n            hasInput: true\n        },\n        {\n            id: \"distribute\",\n            label: \"分配至\",\n            description: \"分配到指定位置\",\n            hasInput: true\n        }\n    ];\n    // 判断是否为纯积木分配模式（不涉及能量）\n    const isBlocksOnlyMode = actionType === \"分配积木\";\n    // 判断是否为纯能量分配模式（不涉及积木）\n    const isEnergyOnlyMode = actionType === \"分配能量\";\n    const handleDistributionSelect = (optionId)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedDistribution: optionId\n            }));\n        // 自动聚焦到对应的输入框\n        setTimeout(()=>{\n            if (optionId === \"assign\" && assignInputRef.current) {\n                assignInputRef.current.focus();\n            } else if (optionId === \"distribute\" && distributeInputRef.current) {\n                distributeInputRef.current.focus();\n            }\n        }, 100); // 延迟一点确保输入框已经渲染\n    };\n    const handleTemplateSelect = (template)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: template\n            }));\n    };\n    const handleCancelTemplate = ()=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: null\n            }));\n    };\n    const handleTemplatePickerOpen = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    const handleTemplatePickerClose = ()=>{\n        setIsTemplatePickerOpen(false);\n    };\n    // 处理批量兑换密钥模态框\n    const handleBatchUseKeyPackageModalOpen = ()=>{\n        setIsBatchUseKeyPackageModalOpen(true);\n    };\n    const handleBatchUseKeyPackageModalClose = ()=>{\n        setIsBatchUseKeyPackageModalOpen(false);\n    };\n    const handleBatchUseKeyPackageSuccess = async ()=>{\n        // 兑换成功后重新获取学生能量信息\n        if (students.length > 0) {\n            const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(students.map((s)=>s.userId), notification);\n            setStudentPointsMap(pointsMap);\n        }\n        notification.success(\"密钥兑换成功！\");\n    };\n    // 获取当前选中分配方式对应的能量数值\n    const getCurrentEnergyAmount = ()=>{\n        if (modalData.selectedDistribution === \"assign\") {\n            return modalData.assignEnergyAmount;\n        } else if (modalData.selectedDistribution === \"distribute\") {\n            return modalData.distributeEnergyAmount;\n        }\n        return \"\";\n    };\n    // 计算所有学生的最低可分配能量\n    const getMinAvailablePoints = ()=>{\n        if (modalData.selectedStudents.length === 0) return 0;\n        const selectedStudentPoints = modalData.selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n        return Math.min(...selectedStudentPoints);\n    };\n    // 获取当前分配方式的提示信息\n    const getEnergyDisplayInfo = ()=>{\n        if (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") {\n            return {\n                label: \"最低可分配能量\",\n                value: getMinAvailablePoints()\n            };\n        }\n        return {\n            label: \"\",\n            value: 0\n        };\n    };\n    const handleNext = ()=>{\n        console.log(\"选择的分配方式:\", modalData.selectedDistribution);\n        console.log(\"选择的模板:\", modalData.selectedTemplate);\n        // 在纯积木分配模式时，跳过能量验证\n        if (!isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\")) {\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            console.log(\"分配能量数量:\", currentEnergyAmount);\n            // 检查是否有输入错误\n            const errorKey = modalData.selectedDistribution === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n            if (inputErrors[errorKey]) {\n                notification.error(\"请修正输入错误后再继续\");\n                return;\n            }\n            // 检查能量数量是否有效\n            const energyAmountNum = Number(currentEnergyAmount);\n            if (!currentEnergyAmount || energyAmountNum <= 0) {\n                notification.error(\"请输入有效的分配能量数量\");\n                return;\n            }\n            if (modalData.selectedDistribution === \"assign\") {\n                // \"分配\"按钮：检查选中学生的可分配能量是否足够\n                const insufficientStudents = modalData.selectedStudents.filter((studentId)=>{\n                    const studentAvailablePoints = studentPointsMap.get(studentId);\n                    return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;\n                });\n                if (insufficientStudents.length > 0) {\n                    const insufficientNames = insufficientStudents.map((studentId)=>{\n                        const student = students.find((s)=>s.userId === studentId);\n                        const availablePoints = studentPointsMap.get(studentId) || 0;\n                        return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(可分配: \").concat(availablePoints, \")\");\n                    }).join(\"、\");\n                    notification.error(\"积分不足：以下学生的可分配能量不足 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                    return;\n                }\n            } else if (modalData.selectedDistribution === \"distribute\") {\n                // \"分配至\"按钮：检查需要补充能量的学生\n                const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                    const currentPoints = studentPointsMap.get(studentId) || 0;\n                    return currentPoints < energyAmountNum;\n                });\n                if (studentsNeedingEnergy.length > 0) {\n                    // 检查这些学生是否有足够的可分配能量来达到目标值\n                    const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = energyAmountNum - currentPoints;\n                        const studentAvailablePoints = studentPointsMap.get(studentId);\n                        return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                    });\n                    if (insufficientStudents.length > 0) {\n                        const insufficientNames = insufficientStudents.map((studentId)=>{\n                            const student = students.find((s)=>s.userId === studentId);\n                            const currentPoints = studentPointsMap.get(studentId) || 0;\n                            const neededPoints = energyAmountNum - currentPoints;\n                            const availablePoints = studentPointsMap.get(studentId) || 0;\n                            return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(需要: \").concat(neededPoints, \", 可分配: \").concat(availablePoints, \")\");\n                        }).join(\"、\");\n                        notification.error(\"积分不足：以下学生无法达到目标能量值 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                        return;\n                    }\n                }\n            }\n        }\n        // 根据操作类型决定下一步\n        if (actionType === \"发布任务\" || actionType === \"快速上课\") {\n            // 发布任务或快速上课：切换到发布任务步骤\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n        } else {\n            // 分配积木或分配能量：直接完成操作并关闭弹窗\n            console.log(\"\".concat(actionType, \"操作完成\"), {\n                selectedDistribution: modalData.selectedDistribution,\n                selectedTemplate: modalData.selectedTemplate,\n                selectedStudents: modalData.selectedStudents,\n                energyAmount: getCurrentEnergyAmount()\n            });\n            // 这里可以调用相应的API来执行分配操作\n            // TODO: 实现分配积木和分配能量的API调用\n            notification.success(\"\".concat(actionType, \"成功！\"));\n            clearAllData();\n            onClose();\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep === \"publish\") {\n            // 从发布任务步骤返回时重置持续时间\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\"\n                }));\n            setCurrentStep(\"template\");\n        } else {\n            onBack();\n        }\n    };\n    // 确认发布（与NewPublishTaskModal保持一致）\n    const handleConfirm = ()=>{\n        if (!taskData.taskName.trim()) {\n            notification.error(\"请输入任务名称\");\n            return;\n        }\n        const times = (0,_utils__WEBPACK_IMPORTED_MODULE_11__.getTaskTimes)(taskData.duration);\n        const finalTaskData = {\n            ...taskData,\n            startTime: times.startTime,\n            endTime: times.endTime,\n            selectedWorkIds,\n            attachments,\n            modalData\n        };\n        // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑\n        if (onConfirm) {\n            onConfirm(finalTaskData);\n        } else {\n            // 保留原有的发布逻辑作为后备\n            handlePublishTaskOk();\n        }\n    };\n    const handlePublishTaskOk = async ()=>{\n        // 防止重复点击\n        if (isPublishing) {\n            return;\n        }\n        try {\n            var _modalData_selectedTemplate;\n            setIsPublishing(true);\n            // 验证必填字段\n            if (!taskData.taskName.trim()) {\n                notification.error(\"请输入任务名称\");\n                setIsPublishing(false);\n                return;\n            }\n            console.log(\"发布任务:\", taskData);\n            console.log(\"选中的作品ID:\", selectedWorkIds);\n            console.log(\"模态框数据:\", modalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const user = userData ? JSON.parse(userData) : null;\n            const teacherId = user === null || user === void 0 ? void 0 : user.userId;\n            if (!teacherId) {\n                notification.error(\"未找到用户信息\");\n                setIsPublishing(false);\n                return;\n            }\n            // 处理时间\n            const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();\n            const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n            // 构建任务发布参数\n            const taskParams = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskType: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.TaskType.GRAPHIC,\n                priority: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.Priority.NORMAL,\n                startDate: startDate,\n                endDate: endDate,\n                taskContent: taskData.taskDescription || \"\",\n                attachments: attachments.map((file)=>file.name) || [],\n                isPublic: 1,\n                allowLateSubmission: false,\n                studentIds: modalData.selectedStudents,\n                classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                templateId: (_modalData_selectedTemplate = modalData.selectedTemplate) === null || _modalData_selectedTemplate === void 0 ? void 0 : _modalData_selectedTemplate.id,\n                workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim() !== \"\") || [] // 过滤空的自评项\n            };\n            console.log(\"任务发布参数:\", taskParams);\n            console.log(\"作品ID数组:\", taskParams.workIds);\n            // 准备并行请求数组\n            const requests = [];\n            // 1. 任务发布请求（必须执行）\n            requests.push(_lib_api_task__WEBPACK_IMPORTED_MODULE_9__[\"default\"].publishTask(taskParams));\n            // 2. 能量分配请求（如果需要）\n            let energyRequest = null;\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            if ((modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && currentEnergyAmount) {\n                const targetAmount = Number(currentEnergyAmount);\n                const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n                if (modalData.selectedDistribution === \"assign\") {\n                    // \"分配\"按钮：给每个学生分配固定数量的能量\n                    const studentExpiries = {};\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        studentExpiries[studentId] = defaultExpireTime;\n                    });\n                    energyRequest = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                        availablePoints: targetAmount,\n                        studentExpiries,\n                        remark: \"任务发布 - \".concat(taskData.taskName)\n                    });\n                    requests.push(energyRequest);\n                } else if (modalData.selectedDistribution === \"distribute\") {\n                    // \"分配至\"按钮：将学生能量补充到目标值\n                    const energyRequests = [];\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = targetAmount - currentPoints;\n                        // 只有当学生当前能量小于目标值时才分配\n                        if (neededPoints > 0) {\n                            const studentExpiries = {};\n                            studentExpiries[studentId] = defaultExpireTime;\n                            const request = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                                availablePoints: neededPoints,\n                                studentExpiries,\n                                remark: \"任务发布 - \".concat(taskData.taskName, \" (补充至\").concat(targetAmount, \")\")\n                            });\n                            energyRequests.push(request);\n                        }\n                    });\n                    // 将所有能量分配请求添加到主请求列表\n                    requests.push(...energyRequests);\n                }\n            }\n            // 3. 模板分配请求（如果需要）\n            let templateRequest = null;\n            if (modalData.selectedTemplate) {\n                const users = modalData.selectedStudents.map((studentId)=>({\n                        userId: studentId,\n                        roleId: 1,\n                        templateId: modalData.selectedTemplate.id,\n                        originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id\n                    }));\n                templateRequest = (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_10__.batchAddUserJoinRole)({\n                    users\n                });\n                requests.push(templateRequest);\n            }\n            // 并行执行所有请求\n            const results = await Promise.allSettled(requests);\n            // 处理任务发布结果\n            const taskResult = results[0];\n            if (taskResult.status === \"fulfilled\" && taskResult.value.data.code === 200) {\n                // 显示成功发布任务的提示\n                notification.success(\"\\uD83C\\uDF89 任务发布成功！学生可以开始学习了\");\n            } else {\n                const errorMsg = taskResult.status === \"fulfilled\" ? taskResult.value.data.message || \"任务发布失败\" : \"任务发布失败\";\n                notification.error(errorMsg);\n                setIsPublishing(false);\n                return; // 任务发布失败则直接返回\n            }\n            // 处理能量分配结果\n            let resultIndex = 1;\n            if (energyRequest) {\n                const energyResult = results[resultIndex];\n                if (energyResult.status === \"fulfilled\" && energyResult.value.data.code === 200) {\n                    notification.success(\"能量分配完成！\");\n                } else {\n                    console.error(\"能量分配失败:\", energyResult);\n                    notification.warning(\"能量分配失败\");\n                }\n                resultIndex++;\n            }\n            // 处理模板分配结果\n            if (templateRequest) {\n                const templateResult = results[resultIndex];\n                if (templateResult.status === \"fulfilled\" && templateResult.value.data.code === 200) {\n                    notification.success(\"模板分配完成！\");\n                } else {\n                    console.error(\"模板分配失败:\", templateResult);\n                    notification.warning(\"模板分配失败\");\n                }\n            }\n            // 延迟关闭弹窗，让用户能看到成功提示\n            setTimeout(()=>{\n                setIsPublishing(false);\n                clearAllData();\n                onClose();\n            }, 800);\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n            setIsPublishing(false);\n        }\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted || !isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        onWheel: (e)=>{\n            // 只阻止事件传播，依赖CSS控制滚动行为\n            e.stopPropagation();\n        },\n        onTouchMove: (e)=>{\n            // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: ()=>{\n                            clearAllData();\n                            onClose();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content template-selection-modal\",\n                        \"data-step\": currentStep,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step completed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"选择班级\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"template\" ? \"active\" : \"completed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: actionType === \"分配积木\" ? \"分配积木\" : actionType === \"分配能量\" ? \"分配能量\" : \"能量和积木\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    (actionType === \"发布任务\" || actionType === \"快速上课\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content-body\",\n                                children: [\n                                    currentStep === \"template\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-title\",\n                                                    children: \"为学生分配能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: [\n                                                    !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"distribution-options\",\n                                                        children: distributionOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"distribution-card \".concat(modalData.selectedDistribution === option.id ? \"selected\" : \"\"),\n                                                                onClick: ()=>handleDistributionSelect(option.id),\n                                                                onMouseEnter: ()=>setHoveredOption(option.id),\n                                                                onMouseLeave: ()=>setHoveredOption(\"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"distribution-label\",\n                                                                        children: (()=>{\n                                                                            const currentAmount = option.id === \"assign\" ? modalData.assignEnergyAmount : option.id === \"distribute\" ? modalData.distributeEnergyAmount : \"\";\n                                                                            return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id ? \"\".concat(option.label, \" \").concat(currentAmount, \"能量\") : option.label;\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 878,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-container\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ref: option.id === \"assign\" ? assignInputRef : distributeInputRef,\n                                                                                type: \"number\",\n                                                                                className: \"energy-input\",\n                                                                                placeholder: option.id === \"assign\" ? \"输入能量\" : \"输入目标值\",\n                                                                                value: option.id === \"assign\" ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount,\n                                                                                min: \"1\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    const updateKey = option.id === \"assign\" ? \"assignEnergyAmount\" : \"distributeEnergyAmount\";\n                                                                                    const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                    // 清除之前的错误\n                                                                                    setInputErrors((prev)=>({\n                                                                                            ...prev,\n                                                                                            [errorKey]: \"\"\n                                                                                        }));\n                                                                                    // 允许空值或正整数\n                                                                                    if (value === \"\") {\n                                                                                        setModalData((prev)=>({\n                                                                                                ...prev,\n                                                                                                [updateKey]: value\n                                                                                            }));\n                                                                                    } else {\n                                                                                        const numValue = Number(value);\n                                                                                        if (Number.isInteger(numValue)) {\n                                                                                            if (numValue < 1) {\n                                                                                                // 设置错误提示\n                                                                                                setInputErrors((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [errorKey]: \"输入能量不能低于1\"\n                                                                                                    }));\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                            } else {\n                                                                                                // 有效输入\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                                // 输入数字时自动选中当前悬停的分配按钮\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        selectedDistribution: option.id\n                                                                                                    }));\n                                                                                            }\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    // 点击输入框时自动选中当前悬停的分配按钮\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onFocus: ()=>{\n                                                                                    setFocusedInput(option.id);\n                                                                                    // 聚焦时也自动选中分配选项\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    setFocusedInput(\"\");\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 889,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            (()=>{\n                                                                                const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                const errorMessage = inputErrors[errorKey];\n                                                                                return errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: \"#ef4444\",\n                                                                                        fontSize: \"12px\",\n                                                                                        marginTop: \"4px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: errorMessage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 942,\n                                                                                    columnNumber: 29\n                                                                                }, undefined);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 888,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, option.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 19\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && modalData.selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-available-energy\",\n                                                        children: loadingStudentPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#6b7280\",\n                                                                fontStyle: \"italic\"\n                                                            },\n                                                            children: \"⏳ 正在获取能量信息...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 21\n                                                        }, undefined) : (()=>{\n                                                            const displayInfo = getEnergyDisplayInfo();\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#64748b\",\n                                                                            fontSize: \"12px\"\n                                                                        },\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 971,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            displayInfo.label,\n                                                                            \": \",\n                                                                            displayInfo.value\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 972,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && getCurrentEnergyAmount() && !loadingStudentPoints && (()=>{\n                                                        const energyAmountNum = Number(getCurrentEnergyAmount());\n                                                        // 根据分配方式进行不同的验证\n                                                        let shouldShowError = false;\n                                                        let errorMessage = \"\";\n                                                        if (modalData.selectedDistribution === \"assign\") {\n                                                            const minAvailable = getMinAvailablePoints();\n                                                            if (energyAmountNum > minAvailable) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"可分配积分不足\";\n                                                            }\n                                                        } else if (modalData.selectedDistribution === \"distribute\") {\n                                                            // 对于\"分配至\"，检查是否有学生无法达到目标值\n                                                            const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                return currentPoints < energyAmountNum;\n                                                            });\n                                                            const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                const neededPoints = energyAmountNum - currentPoints;\n                                                                const studentAvailablePoints = studentPointsMap.get(studentId);\n                                                                return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                                                            });\n                                                            if (insufficientStudents.length > 0) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"部分学生积分不足以达到目标值\";\n                                                            }\n                                                        }\n                                                        if (shouldShowError) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: \"#fef2f2\",\n                                                                    border: \"1px solid #fecaca\",\n                                                                    borderRadius: \"8px\",\n                                                                    padding: \"12px 16px\",\n                                                                    marginTop: \"8px\",\n                                                                    marginBottom: \"12px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"space-between\",\n                                                                    gap: \"12px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: \"8px\",\n                                                                            flex: 1\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: \"500\"\n                                                                            },\n                                                                            children: [\n                                                                                \"⚠️ \",\n                                                                                errorMessage\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1029,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        style: {\n                                                                            background: \"#f97316\",\n                                                                            color: \"white\",\n                                                                            border: \"none\",\n                                                                            borderRadius: \"6px\",\n                                                                            padding: \"6px 12px\",\n                                                                            fontSize: \"12px\",\n                                                                            fontWeight: \"500\",\n                                                                            cursor: \"pointer\",\n                                                                            transition: \"all 0.2s ease\",\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.background = \"#ea580c\";\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.background = \"#f97316\";\n                                                                        },\n                                                                        onClick: ()=>{\n                                                                            handleBatchUseKeyPackageModalOpen();\n                                                                        },\n                                                                        children: \"兑换密钥\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1043,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        }\n                                                        return null;\n                                                    })(),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"section-title\",\n                                                        children: \"为学生分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-selection-area\",\n                                                        children: modalData.selectedTemplate ? // 已选择模板时显示模板信息\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-selected\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-icon\",\n                                                                    children: \"⭐\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1086,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-name\",\n                                                                            children: modalData.selectedTemplate.templateName || modalData.selectedTemplate.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1090,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-label\",\n                                                                            children: \"已选择模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1093,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"change-template-btn\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: \"更换\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"cancel-template-btn\",\n                                                                    onClick: handleCancelTemplate,\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 21\n                                                        }, undefined) : // 未选择模板时显示选择选项\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-options\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-none selected\",\n                                                                    onClick: ()=>{},\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1115,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"不分配模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1117,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"保持原有设置\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1118,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1116,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1111,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-select\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"\\uD83E\\uDDE9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1125,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"选择模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1127,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"为用户提供积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1128,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1126,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : // 发布任务步骤的内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"publish-task-content\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"tab-switcher\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"task\"),\n                                                        children: \"任务信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"resources\"),\n                                                        children: \"资源与附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"task-info-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"任务名称\",\n                                                                value: taskData.taskName,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskName: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1160,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                className: \"form-textarea\",\n                                                                placeholder: \"任务描述\",\n                                                                value: taskData.taskDescription,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskDescription: e.target.value\n                                                                        })),\n                                                                rows: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1170,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1169,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"self-assessment-section\",\n                                                                children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"add-self-assessment-btn\",\n                                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                                ...prev,\n                                                                                selfAssessmentItems: [\n                                                                                    \"\"\n                                                                                ]\n                                                                            })),\n                                                                    children: \"添加自评项\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label\",\n                                                                            children: \"自评项\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1191,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"self-assessment-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        className: \"form-input\",\n                                                                                        placeholder: \"自评项 \".concat(index + 1),\n                                                                                        value: item,\n                                                                                        onChange: (e)=>{\n                                                                                            const newItems = [\n                                                                                                ...taskData.selfAssessmentItems\n                                                                                            ];\n                                                                                            newItems[index] = e.target.value;\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1194,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"remove-btn\",\n                                                                                        onClick: ()=>{\n                                                                                            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        },\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1205,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1193,\n                                                                                columnNumber: 31\n                                                                            }, undefined)),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"add-btn\",\n                                                                            onClick: ()=>setTaskData((prev)=>({\n                                                                                        ...prev,\n                                                                                        selfAssessmentItems: [\n                                                                                            ...prev.selfAssessmentItems,\n                                                                                            \"\"\n                                                                                        ]\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"14px\"\n                                                                                    },\n                                                                                    children: \"➕\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1222,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"添加自评项\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1217,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"time-settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"form-label\",\n                                                                        children: \"任务持续时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1233,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"duration-options\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1小时\"),\n                                                                                children: \"1小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1235,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"3小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"3小时\"),\n                                                                                children: \"3小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1241,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1天\"),\n                                                                                children: \"1天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1247,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"7天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"7天\"),\n                                                                                children: \"7天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1253,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1个月\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1个月\"),\n                                                                                children: \"1个月\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1259,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1234,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"duration-hint\",\n                                                                        children: \"任务将从创建时开始，持续所选时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1266,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1158,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"resources-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"works-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"选择作品\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"help-text\",\n                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1274,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative works-scroll-wrapper\",\n                                                                    onWheel: _utils__WEBPACK_IMPORTED_MODULE_11__.handleWheelScroll,\n                                                                    onMouseDown: handleMouseDownWrapper,\n                                                                    onMouseMove: handleMouseMoveWrapper,\n                                                                    onMouseUp: handleMouseUpWrapper,\n                                                                    onMouseLeave: handleMouseLeaveWrapper,\n                                                                    style: {\n                                                                        minHeight: \"200px\",\n                                                                        cursor: \"grab\",\n                                                                        userSelect: \"none\"\n                                                                    },\n                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"loading-container\",\n                                                                        style: {\n                                                                            minHeight: \"200px\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"loading-spinner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1293,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"加载中...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1294,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1289,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"works-horizontal-scroll\",\n                                                                        children: [\n                                                                            works.sort((a, b)=>{\n                                                                                const aSelected = selectedWorkIds.includes(a.id);\n                                                                                const bSelected = selectedWorkIds.includes(b.id);\n                                                                                // 已选中的排在前面\n                                                                                if (aSelected && !bSelected) return -1;\n                                                                                if (!aSelected && bSelected) return 1;\n                                                                                return 0;\n                                                                            }).map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                    onClick: ()=>handleSelectWorkData(work.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-preview\",\n                                                                                            children: [\n                                                                                                work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fixImageUrl)(work.coverImage || work.screenShotImage),\n                                                                                                    alt: work.title,\n                                                                                                    className: \"work-image\",\n                                                                                                    onError: (e)=>{\n                                                                                                        e.currentTarget.style.display = \"none\";\n                                                                                                        const nextElement = e.currentTarget.nextElementSibling;\n                                                                                                        if (nextElement) {\n                                                                                                            nextElement.style.display = \"flex\";\n                                                                                                        }\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1317,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined) : null,\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"work-placeholder\",\n                                                                                                    style: {\n                                                                                                        display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"24\",\n                                                                                                        height: \"24\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1332,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z\",\n                                                                                                                fill: \"currentColor\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1333,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M21 15L16 10L11 15H21Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\",\n                                                                                                                strokeLinejoin: \"round\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1334,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1331,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1330,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"selection-indicator \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"12\",\n                                                                                                        height: \"12\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M9 12L11 14L15 10\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                            lineNumber: 1340,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1339,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1338,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1315,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-info\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"work-title\",\n                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1346,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1345,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, work.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1309,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)),\n                                                                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"load-more-container\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"load-more-btn\",\n                                                                                    onClick: loadMoreWorksData,\n                                                                                    disabled: loadingMore,\n                                                                                    children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"loading-spinner-small\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1360,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载中...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1361,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载更多\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1365,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                width: \"16\",\n                                                                                                height: \"16\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                fill: \"none\",\n                                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M12 5V19M5 12L12 19L19 12\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    strokeWidth: \"2\",\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1367,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1366,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1353,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1352,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1297,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-text\",\n                                                                            children: \"作品列表\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1377,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1376,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1275,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1272,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"attachments-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"附件上传\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1384,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            multiple: true,\n                                                                            accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                                            onChange: (e)=>{\n                                                                                if (e.target.files) {\n                                                                                    const files = Array.from(e.target.files);\n                                                                                    const validFiles = [];\n                                                                                    const invalidFiles = [];\n                                                                                    // 支持的文件格式\n                                                                                    const allowedTypes = [\n                                                                                        \"image/jpeg\",\n                                                                                        \"image/jpg\",\n                                                                                        \"image/png\",\n                                                                                        \"image/gif\",\n                                                                                        \"application/pdf\",\n                                                                                        \"application/msword\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                                                                                        \"application/vnd.ms-excel\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                                                                                        \"application/vnd.ms-powerpoint\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                                                                                        \"text/plain\"\n                                                                                    ];\n                                                                                    // 文件扩展名检查（作为备用验证）\n                                                                                    const allowedExtensions = [\n                                                                                        \".jpg\",\n                                                                                        \".jpeg\",\n                                                                                        \".png\",\n                                                                                        \".gif\",\n                                                                                        \".pdf\",\n                                                                                        \".doc\",\n                                                                                        \".docx\",\n                                                                                        \".xls\",\n                                                                                        \".xlsx\",\n                                                                                        \".ppt\",\n                                                                                        \".pptx\",\n                                                                                        \".txt\"\n                                                                                    ];\n                                                                                    files.forEach((file)=>{\n                                                                                        var _file_name_split_pop;\n                                                                                        // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）\n                                                                                        if (file.size > 10 * 1024 * 1024) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：文件大小超过10MB\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        // 检查文件类型\n                                                                                        const fileExtension = \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n                                                                                        const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);\n                                                                                        if (!isValidType) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：不支持的文件格式\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        validFiles.push(file);\n                                                                                    });\n                                                                                    // 添加有效文件\n                                                                                    if (validFiles.length > 0) {\n                                                                                        setAttachments((prev)=>[\n                                                                                                ...prev,\n                                                                                                ...validFiles\n                                                                                            ]);\n                                                                                    }\n                                                                                    // 显示错误信息\n                                                                                    if (invalidFiles.length > 0) {\n                                                                                        alert(\"以下文件无法上传：\\n\".concat(invalidFiles.join(\"\\n\")));\n                                                                                    }\n                                                                                    // 重置input的value，确保可以重复选择相同文件\n                                                                                    e.target.value = \"\";\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                display: \"none\"\n                                                                            },\n                                                                            id: \"file-upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1386,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"file-upload\",\n                                                                            className: \"upload-btn\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1445,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"file-format-info\",\n                                                                            children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1448,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1385,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachments-list\",\n                                                                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"file-name\",\n                                                                                    children: file.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1456,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setAttachments((prev)=>prev.filter((_, i)=>i !== index)),\n                                                                                    className: \"remove-attachment-btn\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1457,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1455,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1453,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1383,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"prev-btn\",\n                                                onClick: handlePrevious,\n                                                children: \"上一步\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1476,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"next-btn \".concat(currentStep === \"template\" && (isEnergyOnlyMode ? modalData.selectedDistribution : modalData.selectedTemplate || modalData.selectedDistribution) || currentStep === \"publish\" && taskData.taskName.trim() && !isPublishing ? \"enabled\" : \"disabled\", \" \").concat(isPublishing ? \"publishing\" : \"\"),\n                                                onClick: currentStep === \"template\" ? handleNext : handleConfirm,\n                                                disabled: currentStep === \"template\" ? isEnergyOnlyMode ? !modalData.selectedDistribution : !modalData.selectedTemplate && !modalData.selectedDistribution : !taskData.taskName.trim() || isPublishing,\n                                                children: currentStep === \"template\" ? actionType === \"发布任务\" || actionType === \"快速上课\" ? \"下一步\" : \"分配\" : isPublishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"publishing-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1503,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"发布中...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1502,\n                                                    columnNumber: 19\n                                                }, undefined) : \"开始上课\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1479,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1475,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 824,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: handleTemplatePickerClose,\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1515,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalOpen,\n                selectedStudentIds: modalData.selectedStudents,\n                students: students,\n                onClose: handleBatchUseKeyPackageModalClose,\n                onSuccess: handleBatchUseKeyPackageSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1522,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n        lineNumber: 813,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateSelectionModal, \"3fkWfUC8OKkJ1JgoGu0y/qNfEA0=\");\n_c = TemplateSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"TemplateSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\n"));

/***/ })

});