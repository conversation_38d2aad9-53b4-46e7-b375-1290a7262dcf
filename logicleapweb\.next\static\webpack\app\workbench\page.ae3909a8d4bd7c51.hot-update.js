"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx":
/*!*************************************************************!*\
  !*** ./app/workbench/components/TemplateSelectionModal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BatchUseKeyPackageModal */ \"(app-pages-browser)/./app/workbench/components/BatchUseKeyPackageModal.tsx\");\n/* harmony import */ var _TemplatePickerModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TemplatePickerModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* harmony import */ var _lib_api_points__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/points */ \"(app-pages-browser)/./lib/api/points.ts\");\n/* harmony import */ var _lib_api_task__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api/task */ \"(app-pages-browser)/./lib/api/task.ts\");\n/* harmony import */ var _lib_api_role__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api/role */ \"(app-pages-browser)/./lib/api/role.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 导入API\n\n\n\n// 导入工具函数\n\nconst TemplateSelectionModal = (param)=>{\n    let { isOpen, onClose, onBack, onConfirm, actionType, selectedSchool, selectedClass } = param;\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredOption, setHoveredOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [focusedInput, setFocusedInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // 跟踪哪个输入框有焦点\n    // 输入框引用\n    const assignInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const distributeInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 移除教师能量相关状态，因为不需要检查教师能量池\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 学生相关状态\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [studentPointsMap, setStudentPointsMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [loadingStudentPoints, setLoadingStudentPoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 存储模态框数据的状态\n    const [modalData, setModalData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        selectedDistribution: \"none\",\n        assignEnergyAmount: \"\",\n        distributeEnergyAmount: \"\",\n        selectedTemplate: null,\n        selectedStudents: []\n    });\n    // 输入验证错误状态\n    const [inputErrors, setInputErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        assignEnergyError: \"\",\n        distributeEnergyError: \"\"\n    });\n    // 当前步骤状态\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"template\");\n    // 发布任务相关状态\n    const [taskData, setTaskData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        taskName: \"\",\n        taskDescription: \"\",\n        selfAssessmentItems: [],\n        duration: \"1小时\",\n        startTime: \"\",\n        endTime: \"\"\n    });\n    // 持续时间选择器状态\n    const [showDurationSelector, setShowDurationSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 点击外部关闭持续时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (showDurationSelector && !target.closest(\".duration-field-container\")) {\n                setShowDurationSelector(false);\n            }\n        };\n        if (showDurationSelector) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationSelector\n    ]);\n    // 持续时间选择函数\n    const handleDurationSelect = (duration)=>{\n        setTaskData((prev)=>({\n                ...prev,\n                duration\n            }));\n    };\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"task\");\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布任务加载状态\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 鼠标拖拽滚动状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        scrollLeft: 0\n    });\n    const pageSize = 10;\n    // 阻止背景页面滚动\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            // 保存原始的 overflow 样式\n            const originalStyle = window.getComputedStyle(document.body).overflow;\n            // 阻止背景滚动\n            document.body.style.overflow = \"hidden\";\n            return ()=>{\n                // 恢复原始样式\n                document.body.style.overflow = originalStyle;\n            };\n        }\n    }, [\n        isOpen\n    ]);\n    // 获取作品列表（分页懒加载）\n    const fetchWorksData = async function() {\n        let pageNum = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, isLoadMore = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const currentState = {\n            works,\n            selectedWorkIds,\n            page,\n            hasMore,\n            loadingWorks,\n            loadingMore\n        };\n        const setState = (newState)=>{\n            if (newState.works !== undefined) setWorks(newState.works);\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n            if (newState.page !== undefined) setPage(newState.page);\n            if (newState.hasMore !== undefined) setHasMore(newState.hasMore);\n            if (newState.loadingWorks !== undefined) setLoadingWorks(newState.loadingWorks);\n            if (newState.loadingMore !== undefined) setLoadingMore(newState.loadingMore);\n        };\n        await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchWorks)(pageNum, pageSize, isLoadMore, currentState, setState);\n    };\n    // 加载更多作品\n    const loadMoreWorksData = ()=>{\n        if (!loadingMore && hasMore) {\n            const nextPage = page + 1;\n            fetchWorksData(nextPage, true);\n        }\n    };\n    // 选择作品（支持多选）\n    const handleSelectWorkData = (workId)=>{\n        const setState = (newState)=>{\n            if (newState.selectedWorkIds !== undefined) setSelectedWorkIds(newState.selectedWorkIds);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleSelectWork)(workId, selectedWorkIds, setState);\n    };\n    // 创建鼠标处理函数的包装器\n    const handleMouseDownWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseDown)(e, setDragState);\n    };\n    const handleMouseMoveWrapper = (e)=>{\n        const dragState = {\n            isDragging,\n            dragStart\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseMove)(e, dragState);\n    };\n    const handleMouseUpWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseUp)(e, setDragState);\n    };\n    const handleMouseLeaveWrapper = (e)=>{\n        const setDragState = (newState)=>{\n            if (newState.isDragging !== undefined) setIsDragging(newState.isDragging);\n            if (newState.dragStart !== undefined) setDragStart(newState.dragStart);\n        };\n        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.handleMouseLeave)(e, setDragState);\n    };\n    // 当切换到发布任务步骤且选择资源标签页时获取作品列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentStep === \"publish\" && activeTab === \"resources\" && mounted) {\n            // 重置作品状态\n            setWorks([]);\n            setPage(1);\n            setHasMore(true);\n            setSelectedWorkIds([]);\n            setLoadingMore(false);\n            fetchWorksData(1, false);\n        }\n    }, [\n        currentStep,\n        activeTab,\n        mounted\n    ]);\n    // 清空所有数据的函数\n    const clearAllData = ()=>{\n        // 重置步骤状态\n        setCurrentStep(\"template\");\n        // 重置模态框数据\n        setModalData({\n            selectedDistribution: \"\",\n            assignEnergyAmount: \"\",\n            distributeEnergyAmount: \"\",\n            selectedTemplate: null,\n            selectedStudents: []\n        });\n        // 重置错误状态\n        setInputErrors({\n            assignEnergyError: \"\",\n            distributeEnergyError: \"\"\n        });\n        // 重置任务数据\n        setTaskData({\n            taskName: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [],\n            duration: \"1小时\",\n            startTime: \"\",\n            endTime: \"\"\n        });\n        // 重置其他状态\n        setActiveTab(\"task\");\n        setWorks([]);\n        setSelectedWorkIds([]);\n        setAttachments([]);\n        setHoveredOption(\"\");\n        setIsTemplatePickerOpen(false);\n        setIsBatchUseKeyPackageModalOpen(false);\n        setPage(1);\n        setHasMore(true);\n        setLoadingMore(false);\n        // 重置学生相关数据\n        setStudents([]);\n        setStudentPointsMap(new Map());\n    };\n    // 监听模态框关闭，清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            clearAllData();\n        }\n    }, [\n        isOpen\n    ]);\n    // 组件卸载时清空数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearAllData();\n        };\n    }, []);\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 移除获取教师可分配能量的函数，因为不需要检查教师能量池\n    // 获取班级学生列表和能量信息（并行处理）\n    const fetchClassStudentsData = async ()=>{\n        if (!(selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id) || !mounted) return;\n        setLoadingStudentPoints(true); // 提前设置能量加载状态\n        try {\n            const studentsData = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchClassStudentsWithNotification)(selectedClass.id, notification);\n            setStudents(studentsData);\n            // 立即并行获取学生能量信息，不等待学生列表完全处理完\n            if (studentsData.length > 0) {\n                // 不等待，立即开始获取能量信息\n                const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(studentsData.map((s)=>s.userId), notification);\n                setStudentPointsMap(pointsMap);\n            }\n            setLoadingStudentPoints(false);\n        } catch (error) {\n            console.error(\"获取学生列表失败:\", error);\n            setStudents([]);\n            setLoadingStudentPoints(false);\n        }\n    };\n    // 移除获取教师能量的 useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedClass) {\n            fetchClassStudentsData();\n            // 重置模态框数据\n            setModalData({\n                selectedDistribution: isBlocksOnlyMode ? \"none\" : \"none\",\n                assignEnergyAmount: \"\",\n                distributeEnergyAmount: \"\",\n                selectedTemplate: null,\n                selectedStudents: []\n            });\n            // 重置错误状态\n            setInputErrors({\n                assignEnergyError: \"\",\n                distributeEnergyError: \"\"\n            });\n            // 禁用body滚动\n            document.body.style.overflow = \"hidden\";\n        } else {\n            // 恢复body滚动\n            document.body.style.overflow = \"\";\n        }\n        // 清理函数：组件卸载时恢复滚动\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen,\n        selectedClass,\n        mounted\n    ]);\n    // 当学生列表加载完成后，自动选择所有学生\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (students.length > 0) {\n            setModalData((prev)=>({\n                    ...prev,\n                    selectedStudents: students.map((s)=>s.userId)\n                }));\n        }\n    }, [\n        students\n    ]);\n    const distributionOptions = [\n        {\n            id: \"none\",\n            label: \"不分配\",\n            description: \"保持原有设置\",\n            hasInput: false\n        },\n        {\n            id: \"assign\",\n            label: \"分配\",\n            description: \"分配给学生\",\n            hasInput: true\n        },\n        {\n            id: \"distribute\",\n            label: \"分配至\",\n            description: \"分配到指定位置\",\n            hasInput: true\n        }\n    ];\n    // 判断是否为纯积木分配模式（不涉及能量）\n    const isBlocksOnlyMode = actionType === \"分配积木\";\n    // 判断是否为纯能量分配模式（不涉及积木）\n    const isEnergyOnlyMode = actionType === \"分配能量\";\n    const handleDistributionSelect = (optionId)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedDistribution: optionId\n            }));\n        // 自动聚焦到对应的输入框\n        setTimeout(()=>{\n            if (optionId === \"assign\" && assignInputRef.current) {\n                assignInputRef.current.focus();\n            } else if (optionId === \"distribute\" && distributeInputRef.current) {\n                distributeInputRef.current.focus();\n            }\n        }, 100); // 延迟一点确保输入框已经渲染\n    };\n    const handleTemplateSelect = (template)=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: template\n            }));\n    };\n    const handleCancelTemplate = ()=>{\n        setModalData((prev)=>({\n                ...prev,\n                selectedTemplate: null\n            }));\n    };\n    const handleTemplatePickerOpen = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    const handleTemplatePickerClose = ()=>{\n        setIsTemplatePickerOpen(false);\n    };\n    // 处理批量兑换密钥模态框\n    const handleBatchUseKeyPackageModalOpen = ()=>{\n        setIsBatchUseKeyPackageModalOpen(true);\n    };\n    const handleBatchUseKeyPackageModalClose = ()=>{\n        setIsBatchUseKeyPackageModalOpen(false);\n    };\n    const handleBatchUseKeyPackageSuccess = async ()=>{\n        // 兑换成功后重新获取学生能量信息\n        if (students.length > 0) {\n            const pointsMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fetchStudentPoints)(students.map((s)=>s.userId), notification);\n            setStudentPointsMap(pointsMap);\n        }\n        notification.success(\"密钥兑换成功！\");\n    };\n    // 获取当前选中分配方式对应的能量数值\n    const getCurrentEnergyAmount = ()=>{\n        if (modalData.selectedDistribution === \"assign\") {\n            return modalData.assignEnergyAmount;\n        } else if (modalData.selectedDistribution === \"distribute\") {\n            return modalData.distributeEnergyAmount;\n        }\n        return \"\";\n    };\n    // 计算所有学生的最低可分配能量\n    const getMinAvailablePoints = ()=>{\n        if (modalData.selectedStudents.length === 0) return 0;\n        const selectedStudentPoints = modalData.selectedStudents.map((studentId)=>studentPointsMap.get(studentId) || 0);\n        return Math.min(...selectedStudentPoints);\n    };\n    // 获取当前分配方式的提示信息\n    const getEnergyDisplayInfo = ()=>{\n        if (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") {\n            return {\n                label: \"最低可分配能量\",\n                value: getMinAvailablePoints()\n            };\n        }\n        return {\n            label: \"\",\n            value: 0\n        };\n    };\n    const handleNext = ()=>{\n        console.log(\"选择的分配方式:\", modalData.selectedDistribution);\n        console.log(\"选择的模板:\", modalData.selectedTemplate);\n        // 在纯积木分配模式时，跳过能量验证\n        if (!isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\")) {\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            console.log(\"分配能量数量:\", currentEnergyAmount);\n            // 检查是否有输入错误\n            const errorKey = modalData.selectedDistribution === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n            if (inputErrors[errorKey]) {\n                notification.error(\"请修正输入错误后再继续\");\n                return;\n            }\n            // 检查能量数量是否有效\n            const energyAmountNum = Number(currentEnergyAmount);\n            if (!currentEnergyAmount || energyAmountNum <= 0) {\n                notification.error(\"请输入有效的分配能量数量\");\n                return;\n            }\n            if (modalData.selectedDistribution === \"assign\") {\n                // \"分配\"按钮：检查选中学生的可分配能量是否足够\n                const insufficientStudents = modalData.selectedStudents.filter((studentId)=>{\n                    const studentAvailablePoints = studentPointsMap.get(studentId);\n                    return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;\n                });\n                if (insufficientStudents.length > 0) {\n                    const insufficientNames = insufficientStudents.map((studentId)=>{\n                        const student = students.find((s)=>s.userId === studentId);\n                        const availablePoints = studentPointsMap.get(studentId) || 0;\n                        return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(可分配: \").concat(availablePoints, \")\");\n                    }).join(\"、\");\n                    notification.error(\"积分不足：以下学生的可分配能量不足 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                    return;\n                }\n            } else if (modalData.selectedDistribution === \"distribute\") {\n                // \"分配至\"按钮：检查需要补充能量的学生\n                const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                    const currentPoints = studentPointsMap.get(studentId) || 0;\n                    return currentPoints < energyAmountNum;\n                });\n                if (studentsNeedingEnergy.length > 0) {\n                    // 检查这些学生是否有足够的可分配能量来达到目标值\n                    const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = energyAmountNum - currentPoints;\n                        const studentAvailablePoints = studentPointsMap.get(studentId);\n                        return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                    });\n                    if (insufficientStudents.length > 0) {\n                        const insufficientNames = insufficientStudents.map((studentId)=>{\n                            const student = students.find((s)=>s.userId === studentId);\n                            const currentPoints = studentPointsMap.get(studentId) || 0;\n                            const neededPoints = energyAmountNum - currentPoints;\n                            const availablePoints = studentPointsMap.get(studentId) || 0;\n                            return \"\".concat((student === null || student === void 0 ? void 0 : student.nickName) || \"学生\".concat(studentId), \"(需要: \").concat(neededPoints, \", 可分配: \").concat(availablePoints, \")\");\n                        }).join(\"、\");\n                        notification.error(\"积分不足：以下学生无法达到目标能量值 \".concat(energyAmountNum, \"：\").concat(insufficientNames));\n                        return;\n                    }\n                }\n            }\n        }\n        // 根据操作类型决定下一步\n        if (actionType === \"快速上课\") {\n            // 快速上课：切换到发布任务步骤\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n            setActiveTab(\"task\"); // 默认显示任务信息标签页\n        } else if (actionType === \"发布任务\") {\n            // 发布任务：直接切换到发布任务步骤（跳过能量和积木配置）\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\" // 重置为默认持续时间\n                }));\n            setCurrentStep(\"publish\");\n            setActiveTab(\"task\"); // 默认显示任务信息标签页\n        } else {\n            // 分配积木或分配能量：直接执行分配操作并关闭弹窗\n            console.log(\"\".concat(actionType, \"操作执行\"), {\n                selectedDistribution: modalData.selectedDistribution,\n                selectedTemplate: modalData.selectedTemplate,\n                selectedStudents: modalData.selectedStudents,\n                energyAmount: getCurrentEnergyAmount()\n            });\n            // 这里可以调用相应的API来执行分配操作\n            // TODO: 实现分配积木和分配能量的API调用\n            notification.success(\"\".concat(actionType, \"成功！\"));\n            clearAllData();\n            onClose();\n        }\n    };\n    const handlePrevious = ()=>{\n        if (currentStep === \"publish\") {\n            // 从发布任务步骤返回时重置持续时间\n            setTaskData((prev)=>({\n                    ...prev,\n                    duration: \"1小时\"\n                }));\n            setCurrentStep(\"template\");\n        } else {\n            onBack();\n        }\n    };\n    // 确认发布（与NewPublishTaskModal保持一致）\n    const handleConfirm = ()=>{\n        if (!taskData.taskName.trim()) {\n            notification.error(\"请输入任务名称\");\n            return;\n        }\n        const times = (0,_utils__WEBPACK_IMPORTED_MODULE_11__.getTaskTimes)(taskData.duration);\n        const finalTaskData = {\n            ...taskData,\n            startTime: times.startTime,\n            endTime: times.endTime,\n            selectedWorkIds,\n            attachments,\n            modalData\n        };\n        // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑\n        if (onConfirm) {\n            onConfirm(finalTaskData);\n        } else {\n            // 保留原有的发布逻辑作为后备\n            handlePublishTaskOk();\n        }\n    };\n    const handlePublishTaskOk = async ()=>{\n        // 防止重复点击\n        if (isPublishing) {\n            return;\n        }\n        try {\n            var _modalData_selectedTemplate;\n            setIsPublishing(true);\n            // 验证必填字段\n            if (!taskData.taskName.trim()) {\n                notification.error(\"请输入任务名称\");\n                setIsPublishing(false);\n                return;\n            }\n            console.log(\"发布任务:\", taskData);\n            console.log(\"选中的作品ID:\", selectedWorkIds);\n            console.log(\"模态框数据:\", modalData);\n            // 获取当前用户信息\n            const userData = localStorage.getItem(\"user\");\n            const user = userData ? JSON.parse(userData) : null;\n            const teacherId = user === null || user === void 0 ? void 0 : user.userId;\n            if (!teacherId) {\n                notification.error(\"未找到用户信息\");\n                setIsPublishing(false);\n                return;\n            }\n            // 处理时间\n            const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();\n            const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);\n            // 构建任务发布参数\n            const taskParams = {\n                taskName: taskData.taskName,\n                taskDescription: taskData.taskDescription || \"\",\n                taskType: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.TaskType.GRAPHIC,\n                priority: _lib_api_task__WEBPACK_IMPORTED_MODULE_9__.Priority.NORMAL,\n                startDate: startDate,\n                endDate: endDate,\n                taskContent: taskData.taskDescription || \"\",\n                attachments: attachments.map((file)=>file.name) || [],\n                isPublic: 1,\n                allowLateSubmission: false,\n                studentIds: modalData.selectedStudents,\n                classId: selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.id,\n                templateId: (_modalData_selectedTemplate = modalData.selectedTemplate) === null || _modalData_selectedTemplate === void 0 ? void 0 : _modalData_selectedTemplate.id,\n                workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined,\n                selfAssessmentItems: taskData.selfAssessmentItems.filter((item)=>item.trim() !== \"\") || [] // 过滤空的自评项\n            };\n            console.log(\"任务发布参数:\", taskParams);\n            console.log(\"作品ID数组:\", taskParams.workIds);\n            // 准备并行请求数组\n            const requests = [];\n            // 1. 任务发布请求（必须执行）\n            requests.push(_lib_api_task__WEBPACK_IMPORTED_MODULE_9__[\"default\"].publishTask(taskParams));\n            // 2. 能量分配请求（如果需要）\n            let energyRequest = null;\n            const currentEnergyAmount = getCurrentEnergyAmount();\n            if ((modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && currentEnergyAmount) {\n                const targetAmount = Number(currentEnergyAmount);\n                const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();\n                if (modalData.selectedDistribution === \"assign\") {\n                    // \"分配\"按钮：给每个学生分配固定数量的能量\n                    const studentExpiries = {};\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        studentExpiries[studentId] = defaultExpireTime;\n                    });\n                    energyRequest = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                        availablePoints: targetAmount,\n                        studentExpiries,\n                        remark: \"任务发布 - \".concat(taskData.taskName)\n                    });\n                    requests.push(energyRequest);\n                } else if (modalData.selectedDistribution === \"distribute\") {\n                    // \"分配至\"按钮：将学生能量补充到目标值\n                    const energyRequests = [];\n                    modalData.selectedStudents.forEach((studentId)=>{\n                        const currentPoints = studentPointsMap.get(studentId) || 0;\n                        const neededPoints = targetAmount - currentPoints;\n                        // 只有当学生当前能量小于目标值时才分配\n                        if (neededPoints > 0) {\n                            const studentExpiries = {};\n                            studentExpiries[studentId] = defaultExpireTime;\n                            const request = _lib_api_points__WEBPACK_IMPORTED_MODULE_8__.pointsApi.batchAssignPermission({\n                                availablePoints: neededPoints,\n                                studentExpiries,\n                                remark: \"任务发布 - \".concat(taskData.taskName, \" (补充至\").concat(targetAmount, \")\")\n                            });\n                            energyRequests.push(request);\n                        }\n                    });\n                    // 将所有能量分配请求添加到主请求列表\n                    requests.push(...energyRequests);\n                }\n            }\n            // 3. 模板分配请求（如果需要）\n            let templateRequest = null;\n            if (modalData.selectedTemplate) {\n                const users = modalData.selectedStudents.map((studentId)=>({\n                        userId: studentId,\n                        roleId: 1,\n                        templateId: modalData.selectedTemplate.id,\n                        originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id\n                    }));\n                templateRequest = (0,_lib_api_role__WEBPACK_IMPORTED_MODULE_10__.batchAddUserJoinRole)({\n                    users\n                });\n                requests.push(templateRequest);\n            }\n            // 并行执行所有请求\n            const results = await Promise.allSettled(requests);\n            // 处理任务发布结果\n            const taskResult = results[0];\n            if (taskResult.status === \"fulfilled\" && taskResult.value.data.code === 200) {\n                // 显示成功发布任务的提示\n                notification.success(\"\\uD83C\\uDF89 任务发布成功！学生可以开始学习了\");\n            } else {\n                const errorMsg = taskResult.status === \"fulfilled\" ? taskResult.value.data.message || \"任务发布失败\" : \"任务发布失败\";\n                notification.error(errorMsg);\n                setIsPublishing(false);\n                return; // 任务发布失败则直接返回\n            }\n            // 处理能量分配结果\n            let resultIndex = 1;\n            if (energyRequest) {\n                const energyResult = results[resultIndex];\n                if (energyResult.status === \"fulfilled\" && energyResult.value.data.code === 200) {\n                    notification.success(\"能量分配完成！\");\n                } else {\n                    console.error(\"能量分配失败:\", energyResult);\n                    notification.warning(\"能量分配失败\");\n                }\n                resultIndex++;\n            }\n            // 处理模板分配结果\n            if (templateRequest) {\n                const templateResult = results[resultIndex];\n                if (templateResult.status === \"fulfilled\" && templateResult.value.data.code === 200) {\n                    notification.success(\"模板分配完成！\");\n                } else {\n                    console.error(\"模板分配失败:\", templateResult);\n                    notification.warning(\"模板分配失败\");\n                }\n            }\n            // 延迟关闭弹窗，让用户能看到成功提示\n            setTimeout(()=>{\n                setIsPublishing(false);\n                clearAllData();\n                onClose();\n            }, 800);\n        } catch (error) {\n            console.error(\"发布任务失败:\", error);\n            notification.error(\"任务发布失败，请重试\");\n            setIsPublishing(false);\n        }\n    };\n    // 防止水合错误，在客户端挂载前不渲染\n    if (!mounted || !isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modal-overlay\",\n        onWheel: (e)=>{\n            // 只阻止事件传播，依赖CSS控制滚动行为\n            e.stopPropagation();\n        },\n        onTouchMove: (e)=>{\n            // 只阻止事件传播，不调用preventDefault避免被动监听器警告\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"modal-close-btn-outside\",\n                        onClick: ()=>{\n                            clearAllData();\n                            onClose();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"modal-content template-selection-modal\",\n                        \"data-step\": currentStep,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step completed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"选择班级\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"template\" ? \"active\" : \"completed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: actionType === \"分配积木\" ? \"分配积木\" : actionType === \"分配能量\" ? \"分配能量\" : \"能量和积木\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    actionType === \"快速上课\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    actionType === \"发布任务\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step \".concat(currentStep === \"publish\" ? \"active\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-number\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"step-label\",\n                                                children: \"发布任务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modal-content-body\",\n                                children: [\n                                    currentStep === \"template\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"section-title\",\n                                                    children: \"为学生分配能量\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: [\n                                                    !isBlocksOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"distribution-options\",\n                                                        children: distributionOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"distribution-card \".concat(modalData.selectedDistribution === option.id ? \"selected\" : \"\"),\n                                                                onClick: ()=>handleDistributionSelect(option.id),\n                                                                onMouseEnter: ()=>setHoveredOption(option.id),\n                                                                onMouseLeave: ()=>setHoveredOption(\"\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"distribution-label\",\n                                                                        children: (()=>{\n                                                                            const currentAmount = option.id === \"assign\" ? modalData.assignEnergyAmount : option.id === \"distribute\" ? modalData.distributeEnergyAmount : \"\";\n                                                                            return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id ? \"\".concat(option.label, \" \").concat(currentAmount, \"能量\") : option.label;\n                                                                        })()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 893,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    option.hasInput && (modalData.selectedDistribution === option.id || focusedInput === option.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-container\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                ref: option.id === \"assign\" ? assignInputRef : distributeInputRef,\n                                                                                type: \"number\",\n                                                                                className: \"energy-input\",\n                                                                                placeholder: option.id === \"assign\" ? \"输入能量\" : \"输入目标值\",\n                                                                                value: option.id === \"assign\" ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount,\n                                                                                min: \"1\",\n                                                                                onChange: (e)=>{\n                                                                                    const value = e.target.value;\n                                                                                    const updateKey = option.id === \"assign\" ? \"assignEnergyAmount\" : \"distributeEnergyAmount\";\n                                                                                    const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                    // 清除之前的错误\n                                                                                    setInputErrors((prev)=>({\n                                                                                            ...prev,\n                                                                                            [errorKey]: \"\"\n                                                                                        }));\n                                                                                    // 允许空值或正整数\n                                                                                    if (value === \"\") {\n                                                                                        setModalData((prev)=>({\n                                                                                                ...prev,\n                                                                                                [updateKey]: value\n                                                                                            }));\n                                                                                    } else {\n                                                                                        const numValue = Number(value);\n                                                                                        if (Number.isInteger(numValue)) {\n                                                                                            if (numValue < 1) {\n                                                                                                // 设置错误提示\n                                                                                                setInputErrors((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [errorKey]: \"输入能量不能低于1\"\n                                                                                                    }));\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                            } else {\n                                                                                                // 有效输入\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        [updateKey]: value\n                                                                                                    }));\n                                                                                                // 输入数字时自动选中当前悬停的分配按钮\n                                                                                                setModalData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        selectedDistribution: option.id\n                                                                                                    }));\n                                                                                            }\n                                                                                        }\n                                                                                    }\n                                                                                },\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    // 点击输入框时自动选中当前悬停的分配按钮\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onFocus: ()=>{\n                                                                                    setFocusedInput(option.id);\n                                                                                    // 聚焦时也自动选中分配选项\n                                                                                    setModalData((prev)=>({\n                                                                                            ...prev,\n                                                                                            selectedDistribution: option.id\n                                                                                        }));\n                                                                                },\n                                                                                onBlur: ()=>{\n                                                                                    setFocusedInput(\"\");\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 904,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            (()=>{\n                                                                                const errorKey = option.id === \"assign\" ? \"assignEnergyError\" : \"distributeEnergyError\";\n                                                                                const errorMessage = inputErrors[errorKey];\n                                                                                return errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        color: \"#ef4444\",\n                                                                                        fontSize: \"12px\",\n                                                                                        marginTop: \"4px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: errorMessage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 29\n                                                                                }, undefined);\n                                                                            })()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, option.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 19\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && modalData.selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-available-energy\",\n                                                        children: loadingStudentPoints ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#6b7280\",\n                                                                fontStyle: \"italic\"\n                                                            },\n                                                            children: \"⏳ 正在获取能量信息...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 21\n                                                        }, undefined) : (()=>{\n                                                            const displayInfo = getEnergyDisplayInfo();\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: \"#64748b\",\n                                                                            fontSize: \"12px\"\n                                                                        },\n                                                                        children: \"\\uD83D\\uDCA1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 986,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            displayInfo.label,\n                                                                            \": \",\n                                                                            displayInfo.value\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isBlocksOnlyMode && (modalData.selectedDistribution === \"assign\" || modalData.selectedDistribution === \"distribute\") && getCurrentEnergyAmount() && !loadingStudentPoints && (()=>{\n                                                        const energyAmountNum = Number(getCurrentEnergyAmount());\n                                                        // 根据分配方式进行不同的验证\n                                                        let shouldShowError = false;\n                                                        let errorMessage = \"\";\n                                                        if (modalData.selectedDistribution === \"assign\") {\n                                                            const minAvailable = getMinAvailablePoints();\n                                                            if (energyAmountNum > minAvailable) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"可分配积分不足\";\n                                                            }\n                                                        } else if (modalData.selectedDistribution === \"distribute\") {\n                                                            // 对于\"分配至\"，检查是否有学生无法达到目标值\n                                                            const studentsNeedingEnergy = modalData.selectedStudents.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                return currentPoints < energyAmountNum;\n                                                            });\n                                                            const insufficientStudents = studentsNeedingEnergy.filter((studentId)=>{\n                                                                const currentPoints = studentPointsMap.get(studentId) || 0;\n                                                                const neededPoints = energyAmountNum - currentPoints;\n                                                                const studentAvailablePoints = studentPointsMap.get(studentId);\n                                                                return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;\n                                                            });\n                                                            if (insufficientStudents.length > 0) {\n                                                                shouldShowError = true;\n                                                                errorMessage = \"部分学生积分不足以达到目标值\";\n                                                            }\n                                                        }\n                                                        if (shouldShowError) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: \"#fef2f2\",\n                                                                    border: \"1px solid #fecaca\",\n                                                                    borderRadius: \"8px\",\n                                                                    padding: \"12px 16px\",\n                                                                    marginTop: \"8px\",\n                                                                    marginBottom: \"12px\",\n                                                                    display: \"flex\",\n                                                                    alignItems: \"center\",\n                                                                    justifyContent: \"space-between\",\n                                                                    gap: \"12px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: \"flex\",\n                                                                            alignItems: \"center\",\n                                                                            gap: \"8px\",\n                                                                            flex: 1\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: \"13px\",\n                                                                                fontWeight: \"500\"\n                                                                            },\n                                                                            children: [\n                                                                                \"⚠️ \",\n                                                                                errorMessage\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1050,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1044,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        style: {\n                                                                            background: \"#f97316\",\n                                                                            color: \"white\",\n                                                                            border: \"none\",\n                                                                            borderRadius: \"6px\",\n                                                                            padding: \"6px 12px\",\n                                                                            fontSize: \"12px\",\n                                                                            fontWeight: \"500\",\n                                                                            cursor: \"pointer\",\n                                                                            transition: \"all 0.2s ease\",\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.background = \"#ea580c\";\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.background = \"#f97316\";\n                                                                        },\n                                                                        onClick: ()=>{\n                                                                            handleBatchUseKeyPackageModalOpen();\n                                                                        },\n                                                                        children: \"兑换密钥\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1058,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 23\n                                                            }, undefined);\n                                                        }\n                                                        return null;\n                                                    })(),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"section-title\",\n                                                        children: \"为学生分配积木\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    !isEnergyOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-selection-area\",\n                                                        children: modalData.selectedTemplate ? // 已选择模板时显示模板信息\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-selected\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-icon\",\n                                                                    children: \"⭐\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-info\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-name\",\n                                                                            children: modalData.selectedTemplate.templateName || modalData.selectedTemplate.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1105,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"template-label\",\n                                                                            children: \"已选择模板\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1108,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1104,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"change-template-btn\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: \"更换\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1110,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"cancel-template-btn\",\n                                                                    onClick: handleCancelTemplate,\n                                                                    children: \"\\xd7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 21\n                                                        }, undefined) : // 未选择模板时显示选择选项\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"template-options\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-none selected\",\n                                                                    onClick: ()=>{},\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"不分配模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1132,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"保持原有设置\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1133,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1131,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"template-option template-select\",\n                                                                    onClick: handleTemplatePickerOpen,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-icon\",\n                                                                            children: \"\\uD83E\\uDDE9\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1140,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"option-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-title\",\n                                                                                    children: \"选择模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1142,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"option-desc\",\n                                                                                    children: \"为用户提供积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1143,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1141,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1136,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : // 发布任务步骤的内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"publish-task-content\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"tab-switcher\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"task\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"task\"),\n                                                        children: \"任务信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"tab-btn \".concat(activeTab === \"resources\" ? \"active\" : \"\"),\n                                                        onClick: ()=>setActiveTab(\"resources\"),\n                                                        children: \"资源与附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                        lineNumber: 1163,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"modal-content-scrollable\",\n                                                children: activeTab === \"task\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"task-info-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"form-input\",\n                                                                placeholder: \"任务名称\",\n                                                                value: taskData.taskName,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskName: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                className: \"form-textarea\",\n                                                                placeholder: \"任务描述\",\n                                                                value: taskData.taskDescription,\n                                                                onChange: (e)=>setTaskData((prev)=>({\n                                                                            ...prev,\n                                                                            taskDescription: e.target.value\n                                                                        })),\n                                                                rows: 4\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"self-assessment-section\",\n                                                                children: taskData.selfAssessmentItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"add-self-assessment-btn\",\n                                                                    onClick: ()=>setTaskData((prev)=>({\n                                                                                ...prev,\n                                                                                selfAssessmentItems: [\n                                                                                    \"\"\n                                                                                ]\n                                                                            })),\n                                                                    children: \"添加自评项\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1197,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"form-label\",\n                                                                            children: \"自评项\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1206,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        taskData.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"self-assessment-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        className: \"form-input\",\n                                                                                        placeholder: \"自评项 \".concat(index + 1),\n                                                                                        value: item,\n                                                                                        onChange: (e)=>{\n                                                                                            const newItems = [\n                                                                                                ...taskData.selfAssessmentItems\n                                                                                            ];\n                                                                                            newItems[index] = e.target.value;\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"remove-btn\",\n                                                                                        onClick: ()=>{\n                                                                                            const newItems = taskData.selfAssessmentItems.filter((_, i)=>i !== index);\n                                                                                            setTaskData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    selfAssessmentItems: newItems\n                                                                                                }));\n                                                                                        },\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                        lineNumber: 1220,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1208,\n                                                                                columnNumber: 31\n                                                                            }, undefined)),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            className: \"add-btn\",\n                                                                            onClick: ()=>setTaskData((prev)=>({\n                                                                                        ...prev,\n                                                                                        selfAssessmentItems: [\n                                                                                            ...prev.selfAssessmentItems,\n                                                                                            \"\"\n                                                                                        ]\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"14px\"\n                                                                                    },\n                                                                                    children: \"➕\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                \"添加自评项\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1232,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"form-group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"time-settings\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"form-label\",\n                                                                        children: \"任务持续时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"duration-options\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1小时\"),\n                                                                                children: \"1小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1250,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"3小时\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"3小时\"),\n                                                                                children: \"3小时\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1256,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1天\"),\n                                                                                children: \"1天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1262,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"7天\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"7天\"),\n                                                                                children: \"7天\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1268,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"duration-option \".concat(taskData.duration === \"1个月\" ? \"active\" : \"\"),\n                                                                                onClick: ()=>handleDurationSelect(\"1个月\"),\n                                                                                children: \"1个月\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1274,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"duration-hint\",\n                                                                        children: \"任务将从创建时开始，持续所选时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1281,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1246,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"resources-tab\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"works-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"选择作品\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"help-text\",\n                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1289,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative works-scroll-wrapper\",\n                                                                    onWheel: _utils__WEBPACK_IMPORTED_MODULE_11__.handleWheelScroll,\n                                                                    onMouseDown: handleMouseDownWrapper,\n                                                                    onMouseMove: handleMouseMoveWrapper,\n                                                                    onMouseUp: handleMouseUpWrapper,\n                                                                    onMouseLeave: handleMouseLeaveWrapper,\n                                                                    style: {\n                                                                        minHeight: \"200px\",\n                                                                        cursor: \"grab\",\n                                                                        userSelect: \"none\"\n                                                                    },\n                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"loading-container\",\n                                                                        style: {\n                                                                            minHeight: \"200px\"\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"loading-spinner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1308,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"加载中...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1309,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"works-horizontal-scroll\",\n                                                                        children: [\n                                                                            works.sort((a, b)=>{\n                                                                                const aSelected = selectedWorkIds.includes(a.id);\n                                                                                const bSelected = selectedWorkIds.includes(b.id);\n                                                                                // 已选中的排在前面\n                                                                                if (aSelected && !bSelected) return -1;\n                                                                                if (!aSelected && bSelected) return 1;\n                                                                                return 0;\n                                                                            }).map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                    onClick: ()=>handleSelectWorkData(work.id),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-preview\",\n                                                                                            children: [\n                                                                                                work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                    src: (0,_utils__WEBPACK_IMPORTED_MODULE_11__.fixImageUrl)(work.coverImage || work.screenShotImage),\n                                                                                                    alt: work.title,\n                                                                                                    className: \"work-image\",\n                                                                                                    onError: (e)=>{\n                                                                                                        e.currentTarget.style.display = \"none\";\n                                                                                                        const nextElement = e.currentTarget.nextElementSibling;\n                                                                                                        if (nextElement) {\n                                                                                                            nextElement.style.display = \"flex\";\n                                                                                                        }\n                                                                                                    }\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1332,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined) : null,\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"work-placeholder\",\n                                                                                                    style: {\n                                                                                                        display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                    },\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"24\",\n                                                                                                        height: \"24\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1347,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z\",\n                                                                                                                fill: \"currentColor\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1348,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                                d: \"M21 15L16 10L11 15H21Z\",\n                                                                                                                stroke: \"currentColor\",\n                                                                                                                strokeWidth: \"2\",\n                                                                                                                strokeLinejoin: \"round\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                                lineNumber: 1349,\n                                                                                                                columnNumber: 39\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1346,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1345,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"selection-indicator \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                        width: \"12\",\n                                                                                                        height: \"12\",\n                                                                                                        viewBox: \"0 0 24 24\",\n                                                                                                        fill: \"none\",\n                                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                            d: \"M9 12L11 14L15 10\",\n                                                                                                            stroke: \"currentColor\",\n                                                                                                            strokeWidth: \"2\",\n                                                                                                            strokeLinecap: \"round\",\n                                                                                                            strokeLinejoin: \"round\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                            lineNumber: 1355,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                        lineNumber: 1354,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1353,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1330,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"work-info\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"work-title\",\n                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1361,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                            lineNumber: 1360,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, work.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1324,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)),\n                                                                            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"load-more-container\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"load-more-btn\",\n                                                                                    onClick: loadMoreWorksData,\n                                                                                    disabled: loadingMore,\n                                                                                    children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"loading-spinner-small\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1375,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载中...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1376,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                children: \"加载更多\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1380,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                                width: \"16\",\n                                                                                                height: \"16\",\n                                                                                                viewBox: \"0 0 24 24\",\n                                                                                                fill: \"none\",\n                                                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                    d: \"M12 5V19M5 12L12 19L19 12\",\n                                                                                                    stroke: \"currentColor\",\n                                                                                                    strokeWidth: \"2\",\n                                                                                                    strokeLinecap: \"round\",\n                                                                                                    strokeLinejoin: \"round\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                    lineNumber: 1382,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                                lineNumber: 1381,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1368,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                lineNumber: 1367,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-text\",\n                                                                            children: \"作品列表\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1392,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1290,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"attachments-section\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    children: \"附件上传\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"upload-area\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"file\",\n                                                                            multiple: true,\n                                                                            accept: \".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt\",\n                                                                            onChange: (e)=>{\n                                                                                if (e.target.files) {\n                                                                                    const files = Array.from(e.target.files);\n                                                                                    const validFiles = [];\n                                                                                    const invalidFiles = [];\n                                                                                    // 支持的文件格式\n                                                                                    const allowedTypes = [\n                                                                                        \"image/jpeg\",\n                                                                                        \"image/jpg\",\n                                                                                        \"image/png\",\n                                                                                        \"image/gif\",\n                                                                                        \"application/pdf\",\n                                                                                        \"application/msword\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                                                                                        \"application/vnd.ms-excel\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                                                                                        \"application/vnd.ms-powerpoint\",\n                                                                                        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                                                                                        \"text/plain\"\n                                                                                    ];\n                                                                                    // 文件扩展名检查（作为备用验证）\n                                                                                    const allowedExtensions = [\n                                                                                        \".jpg\",\n                                                                                        \".jpeg\",\n                                                                                        \".png\",\n                                                                                        \".gif\",\n                                                                                        \".pdf\",\n                                                                                        \".doc\",\n                                                                                        \".docx\",\n                                                                                        \".xls\",\n                                                                                        \".xlsx\",\n                                                                                        \".ppt\",\n                                                                                        \".pptx\",\n                                                                                        \".txt\"\n                                                                                    ];\n                                                                                    files.forEach((file)=>{\n                                                                                        var _file_name_split_pop;\n                                                                                        // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）\n                                                                                        if (file.size > 10 * 1024 * 1024) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：文件大小超过10MB\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        // 检查文件类型\n                                                                                        const fileExtension = \".\" + ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n                                                                                        const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);\n                                                                                        if (!isValidType) {\n                                                                                            invalidFiles.push(\"\".concat(file.name, \"：不支持的文件格式\"));\n                                                                                            return;\n                                                                                        }\n                                                                                        validFiles.push(file);\n                                                                                    });\n                                                                                    // 添加有效文件\n                                                                                    if (validFiles.length > 0) {\n                                                                                        setAttachments((prev)=>[\n                                                                                                ...prev,\n                                                                                                ...validFiles\n                                                                                            ]);\n                                                                                    }\n                                                                                    // 显示错误信息\n                                                                                    if (invalidFiles.length > 0) {\n                                                                                        alert(\"以下文件无法上传：\\n\".concat(invalidFiles.join(\"\\n\")));\n                                                                                    }\n                                                                                    // 重置input的value，确保可以重复选择相同文件\n                                                                                    e.target.value = \"\";\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                display: \"none\"\n                                                                            },\n                                                                            id: \"file-upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1401,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"file-upload\",\n                                                                            className: \"upload-btn\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1460,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"file-format-info\",\n                                                                            children: \"支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1463,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachments-list\",\n                                                                    children: attachments.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"file-name\",\n                                                                                    children: file.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1471,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setAttachments((prev)=>prev.filter((_, i)=>i !== index)),\n                                                                                    className: \"remove-attachment-btn\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                                    lineNumber: 1472,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                            lineNumber: 1470,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                                    lineNumber: 1468,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1398,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1286,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1171,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modal-footer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"prev-btn\",\n                                                onClick: handlePrevious,\n                                                children: \"上一步\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1491,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"next-btn \".concat(currentStep === \"template\" && (actionType === \"发布任务\" ? true // 发布任务模式下直接启用按钮\n                                                 : isEnergyOnlyMode ? modalData.selectedDistribution : isBlocksOnlyMode ? modalData.selectedTemplate : modalData.selectedTemplate || modalData.selectedDistribution) || currentStep === \"publish\" && taskData.taskName.trim() && !isPublishing ? \"enabled\" : \"disabled\", \" \").concat(isPublishing ? \"publishing\" : \"\"),\n                                                onClick: currentStep === \"template\" ? handleNext : handleConfirm,\n                                                disabled: currentStep === \"template\" ? actionType === \"发布任务\" ? false // 发布任务模式下直接启用按钮\n                                                 : isEnergyOnlyMode ? !modalData.selectedDistribution : isBlocksOnlyMode ? !modalData.selectedTemplate : !modalData.selectedTemplate && !modalData.selectedDistribution : !taskData.taskName.trim() || isPublishing,\n                                                children: currentStep === \"template\" ? actionType === \"快速上课\" ? \"下一步\" : actionType === \"发布任务\" ? \"发布任务\" : \"分配\" : isPublishing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"publishing-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"spinner\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                            lineNumber: 1527,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"发布中...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                    lineNumber: 1526,\n                                                    columnNumber: 19\n                                                }, undefined) : \"开始上课\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                                lineNumber: 1494,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                        lineNumber: 1490,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 833,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: handleTemplatePickerClose,\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1539,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BatchUseKeyPackageModal__WEBPACK_IMPORTED_MODULE_4__.BatchUseKeyPackageModal, {\n                open: isBatchUseKeyPackageModalOpen,\n                selectedStudentIds: modalData.selectedStudents,\n                students: students,\n                onClose: handleBatchUseKeyPackageModalClose,\n                onSuccess: handleBatchUseKeyPackageSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n                lineNumber: 1546,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateSelectionModal.tsx\",\n        lineNumber: 822,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateSelectionModal, \"3fkWfUC8OKkJ1JgoGu0y/qNfEA0=\");\n_c = TemplateSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"TemplateSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\n"));

/***/ })

});