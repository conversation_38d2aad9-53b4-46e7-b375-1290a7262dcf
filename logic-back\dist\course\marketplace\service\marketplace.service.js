"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MarketplaceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketplaceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const course_series_entity_1 = require("../../entities/course-series.entity");
const course_entity_1 = require("../../entities/course.entity");
const course_tag_entity_1 = require("../../entities/course-tag.entity");
const course_series_tag_entity_1 = require("../../entities/course-series-tag.entity");
let MarketplaceService = MarketplaceService_1 = class MarketplaceService {
    courseSeriesRepository;
    courseRepository;
    courseTagRepository;
    courseSeriesTagRepository;
    logger = new common_1.Logger(MarketplaceService_1.name);
    constructor(courseSeriesRepository, courseRepository, courseTagRepository, courseSeriesTagRepository) {
        this.courseSeriesRepository = courseSeriesRepository;
        this.courseRepository = courseRepository;
        this.courseTagRepository = courseTagRepository;
        this.courseSeriesTagRepository = courseSeriesTagRepository;
    }
    async getSeriesList(query) {
        console.log('获取系列课程列表的请求DTO', query);
        const { page = 1, pageSize = 10 } = query;
        const queryBuilder = this.courseSeriesRepository
            .createQueryBuilder('series');
        this.applyFilters(queryBuilder, query);
        this.applySorting(queryBuilder, query.sortBy);
        const total = await queryBuilder.getCount();
        console.log('获取系列课程列表的总数', total);
        const totalPages = Math.ceil(total / pageSize);
        const pagination = {
            page,
            pageSize,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        };
        queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize);
        const seriesList = await queryBuilder.getMany();
        console.log('获取系列课程列表的总数', total);
        const seriesIds = seriesList.map(series => series.id);
        const seriesTagsMap = await this.getSeriesTagsMap(seriesIds);
        const list = await Promise.all(seriesList.map(async (series) => {
            const contentSummary = await this.calculateContentSummary(series);
            const tags = seriesTagsMap.get(series.id) || [];
            return {
                id: series.id,
                title: series.title,
                description: series.description,
                coverImage: series.coverImage,
                category: series.category,
                categoryLabel: this.getCategoryLabel(series.category),
                status: series.status,
                statusLabel: this.getStatusLabel(series.status),
                projectMembers: series.projectMembers,
                totalCourses: series.totalCourses,
                totalStudents: series.totalStudents,
                contentSummary,
                createdAt: series.createdAt.toISOString(),
                tags
            };
        }));
        const filterStats = await this.getFilterStats();
        return {
            list,
            pagination,
            filterStats
        };
    }
    async getSeriesDetail(seriesId) {
        const series = await this.courseSeriesRepository.findOne({
            where: { id: seriesId }
        });
        if (!series) {
            throw new Error(`系列课程不存在，ID: ${seriesId}`);
        }
        const seriesTagsMap = await this.getSeriesTagsMap([seriesId]);
        const tags = seriesTagsMap.get(seriesId) || [];
        const courses = await this.courseRepository.find({
            where: {
                seriesId: seriesId,
                status: 1
            },
            order: { orderIndex: 'ASC' }
        });
        const courseList = courses.map(course => ({
            id: course.id,
            title: course.title,
            description: course.description,
            coverImage: course.coverImage,
            orderIndex: course.orderIndex,
            status: course.status,
            statusLabel: this.getStatusLabel(course.status),
            hasVideo: course.hasVideo,
            hasDocument: course.hasDocument,
            hasAudio: course.hasAudio,
            videoDuration: course.videoDuration || 0,
            videoDurationLabel: this.formatDuration(course.videoDuration || 0),
            videoName: course.videoName || '',
            firstTeachingTitle: course.firstTeachingTitle || '',
            resourcesCount: course.resourcesCount || 0
        }));
        let defaultCourse = null;
        let currentCourseId = 0;
        if (courses.length > 0) {
            const firstCourse = courses[0];
            currentCourseId = firstCourse.id;
            const fullCourseInfo = await this.courseRepository.findOne({
                where: { id: firstCourse.id },
                select: [
                    'id', 'title', 'description', 'coverImage', 'orderIndex', 'status',
                    'hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'videoName',
                    'firstTeachingTitle', 'resourcesCount', 'contentConfig', 'teachingInfo', 'additionalResources'
                ]
            });
            if (fullCourseInfo) {
                defaultCourse = {
                    id: fullCourseInfo.id,
                    title: fullCourseInfo.title,
                    description: fullCourseInfo.description,
                    coverImage: fullCourseInfo.coverImage,
                    orderIndex: fullCourseInfo.orderIndex,
                    status: fullCourseInfo.status,
                    statusLabel: this.getStatusLabel(fullCourseInfo.status),
                    hasVideo: fullCourseInfo.hasVideo,
                    hasDocument: fullCourseInfo.hasDocument,
                    hasAudio: fullCourseInfo.hasAudio,
                    videoDuration: fullCourseInfo.videoDuration || 0,
                    videoDurationLabel: this.formatDuration(fullCourseInfo.videoDuration || 0),
                    videoName: fullCourseInfo.videoName || '',
                    firstTeachingTitle: fullCourseInfo.firstTeachingTitle || '',
                    resourcesCount: fullCourseInfo.resourcesCount || 0,
                    contentConfig: fullCourseInfo.contentConfig || {},
                    teachingInfo: Array.isArray(fullCourseInfo.teachingInfo) ? fullCourseInfo.teachingInfo : [],
                    additionalResources: Array.isArray(fullCourseInfo.additionalResources) ? fullCourseInfo.additionalResources : []
                };
            }
        }
        return {
            id: series.id,
            title: series.title,
            description: series.description,
            coverImage: series.coverImage,
            category: series.category,
            categoryLabel: this.getCategoryLabel(series.category),
            status: series.status,
            statusLabel: this.getStatusLabel(series.status),
            projectMembers: series.projectMembers,
            totalCourses: series.totalCourses,
            totalStudents: series.totalStudents,
            creatorId: series.creatorId,
            createdAt: series.createdAt.toISOString(),
            updatedAt: series.updatedAt.toISOString(),
            tags,
            courses: courseList,
            defaultCourse: defaultCourse,
            currentCourseId
        };
    }
    async getCourseDetail(seriesId, courseId) {
        const series = await this.courseSeriesRepository.findOne({
            where: { id: seriesId }
        });
        if (!series) {
            throw new Error(`系列课程不存在，ID: ${seriesId}`);
        }
        const course = await this.courseRepository.findOne({
            where: {
                id: courseId,
                seriesId: seriesId,
                status: 1
            },
            select: [
                'id', 'title', 'description', 'coverImage', 'orderIndex', 'status',
                'hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'videoName',
                'firstTeachingTitle', 'resourcesCount', 'contentConfig', 'teachingInfo', 'additionalResources'
            ]
        });
        if (!course) {
            throw new Error(`课程不存在或未发布，系列ID: ${seriesId}，课程ID: ${courseId}`);
        }
        return {
            id: course.id,
            title: course.title,
            description: course.description,
            coverImage: course.coverImage,
            hasVideo: course.hasVideo,
            hasDocument: course.hasDocument,
            hasAudio: course.hasAudio,
            videoDuration: course.videoDuration || 0,
            videoDurationLabel: this.formatDuration(course.videoDuration || 0),
            videoName: course.videoName || '',
            resourcesCount: course.resourcesCount || 0,
            contentConfig: course.contentConfig || {},
            teachingInfo: Array.isArray(course.teachingInfo) ? course.teachingInfo : [],
            additionalResources: Array.isArray(course.additionalResources) ? course.additionalResources : [],
            orderIndex: course.orderIndex,
            status: course.status,
            statusLabel: this.getStatusLabel(course.status),
            currentCourseId: course.id
        };
    }
    async getTagsList(query) {
        const { page = 1, pageSize = 50 } = query;
        const queryBuilder = this.courseTagRepository
            .createQueryBuilder('tag');
        this.applyTagFilters(queryBuilder, query);
        queryBuilder
            .orderBy('tag.usage_count', 'DESC')
            .addOrderBy('tag.created_at', 'DESC');
        const total = await queryBuilder.getCount();
        const totalPages = Math.ceil(total / pageSize);
        const pagination = {
            page,
            pageSize,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
        };
        queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize);
        const tagsList = await queryBuilder.getMany();
        const list = tagsList.map(tag => ({
            id: tag.id,
            name: tag.name,
            color: tag.color,
            category: tag.category,
            categoryLabel: this.getTagCategoryLabel(tag.category),
            description: tag.description || '',
            usageCount: tag.usageCount,
            status: tag.status,
            statusLabel: this.getTagStatusLabel(tag.status),
            createdAt: tag.createdAt.toISOString()
        }));
        return {
            list,
            pagination
        };
    }
    async createTag(createTagDto) {
        const existingTag = await this.courseTagRepository.findOne({
            where: { name: createTagDto.name }
        });
        if (existingTag) {
            throw new Error(`标签名称 "${createTagDto.name}" 已存在`);
        }
        const newTag = this.courseTagRepository.create({
            name: createTagDto.name,
            color: createTagDto.color || '#007bff',
            category: createTagDto.category,
            description: createTagDto.description || '',
            status: createTagDto.status || 1,
            usageCount: 0
        });
        const savedTag = await this.courseTagRepository.save(newTag);
        return {
            id: savedTag.id,
            name: savedTag.name,
            color: savedTag.color,
            category: savedTag.category,
            categoryLabel: this.getTagCategoryLabel(savedTag.category),
            description: savedTag.description || '',
            usageCount: savedTag.usageCount,
            status: savedTag.status,
            statusLabel: this.getTagStatusLabel(savedTag.status),
            createdAt: savedTag.createdAt.toISOString(),
            updatedAt: savedTag.updatedAt.toISOString()
        };
    }
    async updateTag(id, updateTagDto) {
        const existingTag = await this.courseTagRepository.findOne({
            where: { id }
        });
        if (!existingTag) {
            throw new Error(`标签不存在，ID: ${id}`);
        }
        if (updateTagDto.name && updateTagDto.name !== existingTag.name) {
            const nameConflict = await this.courseTagRepository.findOne({
                where: { name: updateTagDto.name }
            });
            if (nameConflict) {
                throw new Error(`标签名称 "${updateTagDto.name}" 已存在`);
            }
        }
        await this.courseTagRepository.update(id, {
            ...(updateTagDto.name && { name: updateTagDto.name }),
            ...(updateTagDto.color && { color: updateTagDto.color }),
            ...(updateTagDto.category !== undefined && { category: updateTagDto.category }),
            ...(updateTagDto.description !== undefined && { description: updateTagDto.description }),
            ...(updateTagDto.status !== undefined && { status: updateTagDto.status })
        });
        const updatedTag = await this.courseTagRepository.findOne({
            where: { id }
        });
        return {
            id: updatedTag.id,
            name: updatedTag.name,
            color: updatedTag.color,
            category: updatedTag.category,
            categoryLabel: this.getTagCategoryLabel(updatedTag.category),
            description: updatedTag.description || '',
            usageCount: updatedTag.usageCount,
            status: updatedTag.status,
            statusLabel: this.getTagStatusLabel(updatedTag.status),
            createdAt: updatedTag.createdAt.toISOString(),
            updatedAt: updatedTag.updatedAt.toISOString()
        };
    }
    async deleteTag(id) {
        const existingTag = await this.courseTagRepository.findOne({
            where: { id }
        });
        if (!existingTag) {
            throw new Error(`标签不存在，ID: ${id}`);
        }
        const usageCount = await this.courseSeriesTagRepository.count({
            where: { tagId: id }
        });
        if (usageCount > 0) {
            throw new Error(`标签正在被 ${usageCount} 个系列课程使用，无法删除`);
        }
        await this.courseTagRepository.delete(id);
    }
    async getTagById(id) {
        const tag = await this.courseTagRepository.findOne({
            where: { id }
        });
        if (!tag) {
            throw new Error(`标签不存在，ID: ${id}`);
        }
        return {
            id: tag.id,
            name: tag.name,
            color: tag.color,
            category: tag.category,
            categoryLabel: this.getTagCategoryLabel(tag.category),
            description: tag.description || '',
            usageCount: tag.usageCount,
            status: tag.status,
            statusLabel: this.getTagStatusLabel(tag.status),
            createdAt: tag.createdAt.toISOString(),
            updatedAt: tag.updatedAt.toISOString()
        };
    }
    applyTagFilters(queryBuilder, query) {
        if (query.category !== undefined) {
            queryBuilder.andWhere('tag.category = :category', { category: query.category });
        }
        if (query.status !== undefined) {
            queryBuilder.andWhere('tag.status = :status', { status: query.status });
        }
        if (query.keyword) {
            queryBuilder.andWhere('tag.name LIKE :keyword', { keyword: `%${query.keyword}%` });
        }
    }
    applyFilters(queryBuilder, query) {
        if (query.category !== undefined) {
            queryBuilder.andWhere('series.category = :category', { category: query.category });
        }
        if (query.status !== undefined) {
            queryBuilder.andWhere('series.status = :status', { status: query.status });
        }
        if (query.keyword) {
            queryBuilder.andWhere('(series.title LIKE :keyword OR series.description LIKE :keyword)', { keyword: `%${query.keyword}%` });
        }
        if (query.tagIds) {
            const tagIds = query.tagIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            if (tagIds.length > 0) {
                queryBuilder.andWhere('EXISTS (SELECT 1 FROM course_series_tags cst WHERE cst.series_id = series.id AND cst.tag_id IN (:...tagIds))', { tagIds });
            }
        }
        if (query.hasVideo !== undefined) {
            queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_video = :hasVideo)', { hasVideo: query.hasVideo });
        }
        if (query.hasDocument !== undefined) {
            queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_document = :hasDocument)', { hasDocument: query.hasDocument });
        }
        if (query.hasAudio !== undefined) {
            queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_audio = :hasAudio)', { hasAudio: query.hasAudio });
        }
    }
    applySorting(queryBuilder, sortBy = 'latest') {
        switch (sortBy) {
            case 'popular':
                queryBuilder.orderBy('series.total_students', 'DESC');
                break;
            case 'duration':
                queryBuilder
                    .leftJoin('courses', 'durationCourse', 'durationCourse.series_id = series.id AND durationCourse.status = 1 AND durationCourse.has_video = 1')
                    .addSelect('COALESCE(AVG(durationCourse.video_duration), 0)', 'avgDuration')
                    .groupBy('series.id')
                    .orderBy('avgDuration', 'DESC');
                break;
            case 'latest':
            default:
                queryBuilder.orderBy('series.created_at', 'DESC');
                break;
        }
    }
    async getSeriesTagsMap(seriesIds) {
        if (seriesIds.length === 0) {
            return new Map();
        }
        const seriesTagsQuery = `
      SELECT
        cst.series_id,
        ct.id as tag_id,
        ct.name as tag_name,
        ct.color as tag_color,
        ct.category as tag_category
      FROM course_series_tags cst
      LEFT JOIN course_tags ct ON ct.id = cst.tag_id
      WHERE cst.series_id IN (${seriesIds.join(',')})
        AND ct.status = 1
      ORDER BY cst.series_id, ct.id
    `;
        const rawResults = await this.courseSeriesRepository.query(seriesTagsQuery);
        const tagsMap = new Map();
        rawResults.forEach((row) => {
            const seriesId = row.series_id;
            if (!tagsMap.has(seriesId)) {
                tagsMap.set(seriesId, []);
            }
            if (row.tag_id) {
                tagsMap.get(seriesId).push({
                    id: row.tag_id,
                    name: row.tag_name,
                    color: row.tag_color,
                    category: row.tag_category,
                    categoryLabel: this.getTagCategoryLabel(row.tag_category)
                });
            }
        });
        return tagsMap;
    }
    async calculateContentSummary(series) {
        const courses = await this.courseRepository.find({
            where: { seriesId: series.id, status: 1 },
            select: ['hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'additionalResources']
        });
        const hasVideo = courses.some(course => course.hasVideo === 1);
        const hasDocument = courses.some(course => course.hasDocument === 1);
        const hasAudio = courses.some(course => course.hasAudio === 1);
        const videoCourseCount = courses.filter(course => course.hasVideo === 1).length;
        const documentCourseCount = courses.filter(course => course.hasDocument === 1).length;
        const totalVideoDuration = courses
            .filter(course => course.hasVideo === 1)
            .reduce((sum, course) => sum + (course.videoDuration || 0), 0);
        const averageVideoDuration = videoCourseCount > 0 ? Math.round(totalVideoDuration / videoCourseCount) : 0;
        const totalResourcesCount = courses.reduce((sum, course) => {
            const resources = course.additionalResources || [];
            return sum + (Array.isArray(resources) ? resources.length : 0);
        }, 0);
        return {
            hasVideo,
            hasDocument,
            hasAudio,
            videoCourseCount,
            documentCourseCount,
            averageVideoDuration,
            totalResourcesCount
        };
    }
    async getFilterStats() {
        const totalSeries = await this.courseSeriesRepository.count();
        const officialCount = await this.courseSeriesRepository.count({ where: { category: 0 } });
        const communityCount = await this.courseSeriesRepository.count({ where: { category: 1 } });
        const videoSeriesResult = await this.courseSeriesRepository
            .createQueryBuilder('series')
            .leftJoin('courses', 'course', 'course.series_id = series.id')
            .where('course.has_video = 1')
            .andWhere('course.status = 1')
            .select('DISTINCT series.id')
            .getRawMany();
        const documentSeriesResult = await this.courseSeriesRepository
            .createQueryBuilder('series')
            .leftJoin('courses', 'course', 'course.series_id = series.id')
            .where('course.has_document = 1')
            .andWhere('course.status = 1')
            .select('DISTINCT series.id')
            .getRawMany();
        return {
            totalSeries,
            officialCount,
            communityCount,
            videoSeriesCount: videoSeriesResult.length,
            documentSeriesCount: documentSeriesResult.length
        };
    }
    getCategoryLabel(category) {
        const labels = {
            0: '官方',
            1: '社区'
        };
        return labels[category] || '未知';
    }
    getStatusLabel(status) {
        const labels = {
            0: '草稿',
            1: '已发布',
            2: '已归档'
        };
        return labels[status] || '未知';
    }
    getTagCategoryLabel(category) {
        const labels = {
            0: '难度',
            1: '类型',
            2: '特色',
            3: '其他'
        };
        return labels[category] || '其他';
    }
    getTagStatusLabel(status) {
        const labels = {
            0: '禁用',
            1: '启用'
        };
        return labels[status] || '未知';
    }
    formatDuration(seconds) {
        if (seconds <= 0) {
            return '0分钟';
        }
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        if (hours > 0) {
            return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
        }
        else {
            return `${minutes}分钟`;
        }
    }
};
exports.MarketplaceService = MarketplaceService;
exports.MarketplaceService = MarketplaceService = MarketplaceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(course_series_entity_1.CourseSeries)),
    __param(1, (0, typeorm_1.InjectRepository)(course_entity_1.Course)),
    __param(2, (0, typeorm_1.InjectRepository)(course_tag_entity_1.CourseTag)),
    __param(3, (0, typeorm_1.InjectRepository)(course_series_tag_entity_1.CourseSeriesTag)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], MarketplaceService);
//# sourceMappingURL=marketplace.service.js.map