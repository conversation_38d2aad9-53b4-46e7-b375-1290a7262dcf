{"version": 3, "file": "series-list.dto.js", "sourceRoot": "", "sources": ["../../../../src/course/marketplace/dto/series-list.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAAmE;AACnE,qDAAiF;AACjF,yDAAyC;AAGzC,MAAa,qBAAqB;IAMhC,IAAI,GAAY,CAAC,CAAC;IAQlB,QAAQ,GAAY,EAAE,CAAC;IAOvB,QAAQ,CAAU;IAOlB,MAAM,CAAU;IAKhB,MAAM,CAAU;IAKhB,OAAO,CAAU;IAOjB,QAAQ,CAAU;IAOlB,WAAW,CAAU;IAOrB,QAAQ,CAAU;IAMlB,MAAM,GAAY,QAAQ,CAAC;CAC5B;AAlED,sDAkEC;AA5DC;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;mDACW;AAQlB;IANC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5F,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;uDACe;AAOvB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;uDACK;AAOlB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1F,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;qDACA;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC9D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACM;AAOjB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;uDACK;AAOlB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;0DACQ;AAOrB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;uDACK;AAMlB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,uCAAuC,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC;IACzI,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;;qDACb;AAI7B,MAAa,UAAU;IAErB,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,KAAK,CAAS;IAGd,QAAQ,CAAS;IAGjB,aAAa,CAAS;CACvB;AAfD,gCAeC;AAbC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sCACtC;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;wCACvC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;yCAC3C;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;4CACpD;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iDAChC;AAIxB,MAAa,iBAAiB;IAE5B,QAAQ,CAAU;IAGlB,WAAW,CAAU;IAGrB,QAAQ,CAAU;IAGlB,gBAAgB,CAAS;IAGzB,mBAAmB,CAAS;IAG5B,oBAAoB,CAAS;IAG7B,mBAAmB,CAAS;CAC7B;AArBD,8CAqBC;AAnBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;mDACpC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sDACjC;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;mDACrC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2DAC1B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8DACvB;AAG5B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;+DAC5B;AAG7B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DACxB;AAI9B,MAAa,aAAa;IAExB,EAAE,CAAS;IAGX,KAAK,CAAS;IAGd,WAAW,CAAS;IAGpB,UAAU,CAAS;IAGnB,QAAQ,CAAS;IAGjB,aAAa,CAAS;IAGtB,MAAM,CAAS;IAGf,WAAW,CAAS;IAGpB,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,aAAa,CAAS;IAGtB,cAAc,CAAoB;IAGlC,SAAS,CAAS;IAGlB,IAAI,CAAe;CACpB;AA1CD,sCA0CC;AAxCC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;yCACtC;AAGX;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;4CACxC;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;kDAClC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;;iDAChE;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CACxC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;oDAC9B;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CACjD;AAGf;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDACjC;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;qDAClC;AAGvB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;mDAC5B;AAGrB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;oDAC9B;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;8BAChD,iBAAiB;qDAAC;AAGlC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;gDACpD;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;;2CACtC;AAIrB,MAAa,aAAa;IAExB,IAAI,CAAS;IAGb,QAAQ,CAAS;IAGjB,KAAK,CAAS;IAGd,UAAU,CAAS;IAGnB,OAAO,CAAU;IAGjB,OAAO,CAAU;CAClB;AAlBD,sCAkBC;AAhBC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2CACpC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;+CACjC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;4CACpC;AAGd;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;iDAC7B;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8CACrC;AAGjB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;8CACtC;AAInB,MAAa,cAAc;IAEzB,WAAW,CAAS;IAGpB,aAAa,CAAS;IAGtB,cAAc,CAAS;IAGvB,gBAAgB,CAAS;IAGzB,mBAAmB,CAAS;CAC7B;AAfD,wCAeC;AAbC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;mDAC9B;AAGpB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;qDAC9B;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDAC5B;AAGvB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;wDAC9B;AAGzB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;2DAC3B;AAI9B,MAAa,iBAAiB;IAE5B,IAAI,CAAkB;IAGtB,UAAU,CAAgB;IAG1B,WAAW,CAAiB;CAC7B;AATD,8CASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;;+CACxC;AAGtB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;8BAC9C,aAAa;qDAAC;AAG1B;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;8BAChD,cAAc;sDAAC;AAI9B,MAAa,qBAAqB;IAEhC,IAAI,CAAS;IAGb,OAAO,CAAS;IAGhB,IAAI,CAAoB;CACzB;AATD,sDASC;AAPC;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;mDACvC;AAGb;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;;sDACzC;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;8BAC9D,iBAAiB;mDAAC"}