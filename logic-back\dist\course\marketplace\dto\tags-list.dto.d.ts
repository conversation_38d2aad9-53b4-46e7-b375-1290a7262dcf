export declare class GetTagsListQueryDto {
    page?: number;
    pageSize?: number;
    category?: number;
    status?: number;
    keyword?: string;
}
export declare class TagDetailDto {
    id: number;
    name: string;
    color: string;
    category: number;
    categoryLabel: string;
    description: string;
    usageCount: number;
    status: number;
    statusLabel: string;
    createdAt: string;
}
export declare class TagsPaginationDto {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export declare class TagsListDataDto {
    list: TagDetailDto[];
    pagination: TagsPaginationDto;
}
export declare class TagsListResponseDto {
    code: number;
    message: string;
    data: TagsListDataDto;
}
