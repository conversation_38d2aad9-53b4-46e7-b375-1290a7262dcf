"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseSeries = void 0;
const typeorm_1 = require("typeorm");
const course_entity_1 = require("./course.entity");
let CourseSeries = class CourseSeries {
    id;
    title;
    description;
    coverImage;
    category;
    status;
    projectMembers;
    totalCourses;
    totalStudents;
    creatorId;
    createdAt;
    updatedAt;
    courses;
};
exports.CourseSeries = CourseSeries;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ comment: '系列ID' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, comment: '系列名称' }),
    __metadata("design:type", String)
], CourseSeries.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true, comment: '系列介绍' }),
    __metadata("design:type", String)
], CourseSeries.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, name: 'cover_image', nullable: true, comment: '系列封面图片URL' }),
    __metadata("design:type", String)
], CourseSeries.prototype, "coverImage", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_category'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 0, comment: '分类：0=官方，1=社区' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_status'),
    (0, typeorm_1.Column)({ type: 'tinyint', default: 0, comment: '状态：0=草稿，1=已发布，2=已归档' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, name: 'project_members', nullable: true, comment: '项目成员名单' }),
    __metadata("design:type", String)
], CourseSeries.prototype, "projectMembers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'total_courses', default: 0, comment: '包含课程数量' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "totalCourses", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', name: 'total_students', default: 0, comment: '总学习人数' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "totalStudents", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_creator'),
    (0, typeorm_1.Column)({ type: 'int', name: 'creator_id', nullable: true, comment: '创建者ID' }),
    __metadata("design:type", Number)
], CourseSeries.prototype, "creatorId", void 0);
__decorate([
    (0, typeorm_1.Index)('idx_created_at'),
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at', comment: '创建时间' }),
    __metadata("design:type", Date)
], CourseSeries.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at', comment: '更新时间' }),
    __metadata("design:type", Date)
], CourseSeries.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => course_entity_1.Course, course => course.series),
    __metadata("design:type", Array)
], CourseSeries.prototype, "courses", void 0);
exports.CourseSeries = CourseSeries = __decorate([
    (0, typeorm_1.Entity)('course_series')
], CourseSeries);
//# sourceMappingURL=course-series.entity.js.map