<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="google" value="notranslate">
    <link rel="shortcut icon" href="static/favicon.ico">
    <title>LogicLeapingAI: Compatibility Testing</title>
    <style>
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #4766C2, #2a3d7c);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 99999999;
        opacity: 1;
        visibility: visible;
        transition: opacity 0.8s ease, visibility 0.8s ease;
      }

      #initial-loader.fade-out {
        opacity: 0;
        visibility: hidden;
      }

      .loader-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: 300px;
        height: 400px;
        transform: scale(1);
        transition: transform 0.8s ease;
      }

      #initial-loader.fade-out .loader-container {
        transform: scale(0.95);
      }

      .svg-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin-bottom: 120px;
      }

      .gegga {
        width: 0;
      }

      .snurra {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        filter: url(#gegga);
      }

      .skugga {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        filter: blur(5px);
        opacity: 0.3;
        transform: translate(3px, 3px);
      }

      .stopp1 {
        stop-color: #4766C2;
      }

      .stopp2 {
        stop-color: #96b3ff;
      }

      .halvan {
        animation: Snurra1 10s infinite linear;
        stroke-dasharray: 180 800;
        fill: none;
        stroke: url(#gradient);
        stroke-width: 23;
        stroke-linecap: round;
      }

      .strecken {
        animation: Snurra1 3s infinite linear;
        stroke-dasharray: 26 54;
        fill: none;
        stroke: url(#gradient);
        stroke-width: 23;
        stroke-linecap: round;
      }

      @keyframes Snurra1 {
        0% {
          stroke-dashoffset: 0;
        }
        100% {
          stroke-dashoffset: -403px;
        }
      }

      .text-container {
        position: absolute;
        bottom: 40px;
        left: 0;
        right: 0;
        text-align: center;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .progress-text {
        color: white;
        font-size: 48px;
        line-height: 1;
        font-weight: bold;
        text-shadow: 0 0 20px rgba(71, 102, 194, 0.5);
        margin-bottom: 1.5rem;
        min-width: 100px;
        text-align: center;
        font-family: Arial, sans-serif;
      }

      .message {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        line-height: 1.2;
        text-shadow: 0 0 10px rgba(71, 102, 194, 0.3);
        white-space: nowrap;
        font-family: Arial, sans-serif;
        min-height: 20px;
      }

      #app {
        opacity: 0;
        transition: opacity 0.8s ease;
      }

      #app.loaded {
        opacity: 1;
      }
    </style>
  <script defer src="/logicleap/chunks/tensorflow.2cd86211bdb26685885d.js"></script><script defer src="/logicleap/chunks/react.36dc9d8d8b793c84f2c4.js"></script><script defer src="/logicleap/chunks/vendors.e3e8a55fe48004c591a3.js"></script><script defer src="/logicleap/chunks/commons.91314337026827ea538d.js"></script><script defer src="/logicleap/static/js/compatibilitytesting.600cf2279c6658767e72.js"></script></head>
  <body>
    <div id="initial-loader">
      <div class="loader-container">
        <div class="svg-container">
          <svg class="gegga">
            <defs>
              <filter id="gegga">
                <feGaussianBlur in="SourceGraphic" stdDeviation="7" result="blur" />
                <feColorMatrix
                  in="blur"
                  mode="matrix"
                  values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 20 -10"
                  result="inreGegga"
                />
                <feComposite in="SourceGraphic" in2="inreGegga" operator="atop" />
              </filter>
            </defs>
          </svg>
          <svg class="snurra" width="200" height="200" viewBox="0 0 200 200">
            <defs>
              <linearGradient id="linjärGradient">
                <stop class="stopp1" offset="0" />
                <stop class="stopp2" offset="1" />
              </linearGradient>
              <linearGradient
                y2="160"
                x2="160"
                y1="40"
                x1="40"
                gradientUnits="userSpaceOnUse"
                id="gradient"
                xlink:href="#linjärGradient"
              />
            </defs>
            <path
              class="halvan"
              d="m 164,100 c 0,-35.346224 -28.65378,-64 -64,-64 -35.346224,0 -64,28.653776 -64,64 0,35.34622 28.653776,64 64,64 35.34622,0 64,-26.21502 64,-64 0,-37.784981 -26.92058,-64 -64,-64 -37.079421,0 -65.267479,26.922736 -64,64 1.267479,37.07726 26.703171,65.05317 64,64 37.29683,-1.05317 64,-64 64,-64"
            />
            <circle class="strecken" cx="100" cy="100" r="64" />
          </svg>
          <svg class="skugga" width="200" height="200" viewBox="0 0 200 200">
            <path
              class="halvan"
              d="m 164,100 c 0,-35.346224 -28.65378,-64 -64,-64 -35.346224,0 -64,28.653776 -64,64 0,35.34622 28.653776,64 64,64 35.34622,0 64,-26.21502 64,-64 0,-37.784981 -26.92058,-64 -64,-64 -37.079421,0 -65.267479,26.922736 -64,64 1.267479,37.07726 26.703171,65.05317 64,64 37.29683,-1.05317 64,-64 64,-64"
            />
            <circle class="strecken" cx="100" cy="100" r="64" />
          </svg>
        </div>
        <div class="text-container">
          <div class="progress-text">0%</div>
          <div class="message">正在加载中...</div>
        </div>
      </div>
    </div>

    <div id="app"></div>

    <script>
      // 初始化加载状态
      window.initialLoadTime = Date.now();
      window.loadedResources = 0;
      window.totalResources = 0;
      window.currentProgress = 0;
      window.isCompleted = false;
      window.guiLoaded = false;
      window.baseLoadingComplete = false;
      window.loadingProject = false;  // 添加项目加载标志

      // 基础资源加载进度控制
      function startBaseLoading() {
        const incrementInterval = 30; // 30ms
        const incrementValue = 1; // 每次增加1%
        const maxBaseProgress = 60; // 基础加载最大进度

        const timer = setInterval(() => {
          if (window.currentProgress >= maxBaseProgress || window.baseLoadingComplete) {
            clearInterval(timer);
            return;
          }
          window.updateLoaderProgress(window.currentProgress + incrementValue);
        }, incrementInterval);

        // 1.8s后检查是否需要等待资源加载
        setTimeout(() => {
          window.baseLoadingComplete = true;
          if (window.currentProgress < maxBaseProgress) {
            window.updateLoaderProgress(maxBaseProgress);
          }
        }, 1800);
      }

      // 计算实际进度
      function calculateProgress(loadedCount, totalCount) {
        if (!window.baseLoadingComplete) {
          return window.currentProgress;
        }
        
        // 如果正在加载项目，则使用传入的百分比
        if (window.loadingProject) {
          return window.currentProgress;
        }
        
        // 基础加载后的资源加载进度（60-95%）
        const remainingProgressRange = 35; // 95 - 60
        const resourceProgress = totalCount > 0 ? 
          Math.floor((loadedCount / totalCount) * remainingProgressRange) : 0;
        
        return Math.min(95, 60 + resourceProgress);
      }

      // 更新进度条
      window.updateLoaderProgress = function(percent, forceMessage) {
        if (window.isCompleted) return;
        
        requestAnimationFrame(() => {
          // 确保进度不会回退，并取整
          percent = Math.floor(Math.max(window.currentProgress, percent));
          window.currentProgress = percent;

          const progressText = document.querySelector('#initial-loader .progress-text');
          const message = document.querySelector('#initial-loader .message');
          
          if (progressText) progressText.textContent = percent + '%';
          
          if (message && !forceMessage) {
            if (percent < 30) {
              message.textContent = '正在加载中...';
            } else if (percent < 60) {
              message.textContent = '准备环境中...';
            } else if (percent < 85) {
              message.textContent = '加载编辑器...';
            } else {
              message.textContent = '即将完成...';
            }
          }
        });
      };

      // 更新加载消息
      window.updateLoaderMessage = function(text) {
        const message = document.querySelector('#initial-loader .message');
        if (message && text) {
          message.textContent = text;
        }
      };

      // 重新显示加载动画
      window.showLoader = function(initialProgress, message) {
        const loader = document.getElementById('initial-loader');
        if (loader) {
          window.isCompleted = false;
          window.loadingProject = true;
          window.currentProgress = initialProgress || 60;
          
          // 重新显示加载器
          loader.style.display = 'flex';
          loader.classList.remove('fade-out');
          
          // 更新百分比
          const progressText = document.querySelector('#initial-loader .progress-text');
          if (progressText) progressText.textContent = window.currentProgress + '%';
          
          // 更新消息
          if (message) {
            window.updateLoaderMessage(message);
          }
          
          // 隐藏应用
          const app = document.getElementById('app');
          if (app) {
            app.classList.remove('loaded');
          }
        }
      };

      // 完成加载
      window.completeLoading = function() {
        if (window.isCompleted) return;
        window.isCompleted = true;
        window.loadingProject = false;

        const loader = document.getElementById('initial-loader');
        const app = document.getElementById('app');
        
        if (loader && app) {
          // 平滑过渡到100%
          const finalAnimation = (progress) => {
            window.currentProgress = Math.floor(progress);
            const progressText = document.querySelector('#initial-loader .progress-text');
            if (progressText) progressText.textContent = Math.floor(progress) + '%';
            
            if (progress >= 100) {
              // 确保显示100%后再开始消失动画
              setTimeout(() => {
                app.classList.add('loaded');
                loader.classList.add('fade-out');
                setTimeout(() => {
                  loader.style.display = 'none';
                }, 800);
              }, 200); // 等待200ms让用户看到100%
              return;
            }
            
            requestAnimationFrame(() => {
              finalAnimation(progress + 0.5);
            });
          };

          finalAnimation(window.currentProgress);
        }
      };

      // 监控资源加载
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            window.loadedResources++;
            const progress = calculateProgress(window.loadedResources, window.totalResources);
            window.updateLoaderProgress(progress);
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });

      // 获取初始资源数量
      window.addEventListener('DOMContentLoaded', () => {
        const resources = performance.getEntriesByType('resource');
        window.totalResources = Math.max(resources.length, 1);
        window.loadedResources = resources.filter(r => r.duration > 0).length;
        startBaseLoading(); // 开始基础加载进度
      });

      // GUI加载完成时更新进度
      window.onGUILoaded = function() {
        window.guiLoaded = true;
        if (window.baseLoadingComplete) {
          const progress = calculateProgress(window.loadedResources, window.totalResources);
          window.updateLoaderProgress(progress);
        }
      };

      // 监听应用加载完成
      window.addEventListener('load', () => {
        window.onGUILoaded(); // 标记GUI加载完成
        setTimeout(() => {
          if (!window.loadingProject) { // 只有在没有进行项目加载时才完成
            window.completeLoading();
          }
        }, 500);
      });
    </script>

    <!-- 加载其他样式 -->
    <link rel="stylesheet" href="" />
  </body>
</html>
