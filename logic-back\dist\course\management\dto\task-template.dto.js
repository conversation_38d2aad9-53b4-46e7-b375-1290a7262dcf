"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskTemplateResponseDto = exports.CreateTaskTemplateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class AttachmentDto {
    title;
    url;
    type;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附件标题', example: '练习模板' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AttachmentDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附件URL', example: 'https://example.com/nodejs-template.zip' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AttachmentDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '附件类型', example: 'file' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AttachmentDto.prototype, "type", void 0);
class SelfAssessmentItemDto {
    content;
    sequence;
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '自评项内容', example: '是否正确使用了fs模块进行文件操作？' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SelfAssessmentItemDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '自评项序号', example: 1 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], SelfAssessmentItemDto.prototype, "sequence", void 0);
class CreateTaskTemplateDto {
    taskName;
    taskDescription;
    durationDays;
    attachments;
    workIdsStr;
    selfAssessmentItems;
}
exports.CreateTaskTemplateDto = CreateTaskTemplateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务名称',
        example: 'Node.js基础练习'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '任务名称不能为空' }),
    __metadata("design:type", String)
], CreateTaskTemplateDto.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务描述',
        example: '创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)({ message: '任务描述不能为空' }),
    __metadata("design:type", String)
], CreateTaskTemplateDto.prototype, "taskDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务持续天数',
        example: 7,
        default: 7
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsNotEmpty)({ message: '任务持续天数不能为空' }),
    __metadata("design:type", Number)
], CreateTaskTemplateDto.prototype, "durationDays", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '附件列表',
        type: [AttachmentDto],
        example: [
            {
                "title": "练习模板",
                "url": "https://example.com/nodejs-template.zip",
                "type": "file"
            },
            {
                "title": "参考文档",
                "url": "https://example.com/nodejs-reference.pdf",
                "type": "document"
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => AttachmentDto),
    __metadata("design:type", Array)
], CreateTaskTemplateDto.prototype, "attachments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '关联作品ID字符串',
        example: '201,202,203'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateTaskTemplateDto.prototype, "workIdsStr", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: '自评项列表',
        type: [SelfAssessmentItemDto],
        example: [
            {
                "content": "是否正确使用了fs模块进行文件操作？",
                "sequence": 1
            },
            {
                "content": "HTTP服务器是否能正常启动和响应请求？",
                "sequence": 2
            },
            {
                "content": "代码是否遵循了Node.js的最佳实践？",
                "sequence": 3
            }
        ]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => SelfAssessmentItemDto),
    __metadata("design:type", Array)
], CreateTaskTemplateDto.prototype, "selfAssessmentItems", void 0);
class TaskTemplateResponseDto {
    id;
    courseId;
    taskName;
    taskDescription;
    durationDays;
    attachments;
    workIdsStr;
    selfAssessmentItems;
    status;
    attachmentsCount;
    assessmentItemsCount;
    firstAttachmentType;
    createdAt;
    updatedAt;
}
exports.TaskTemplateResponseDto = TaskTemplateResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务模板ID',
        example: 1
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '所属课程ID',
        example: 25
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "courseId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务名称',
        example: 'Node.js基础练习'
    }),
    __metadata("design:type", String)
], TaskTemplateResponseDto.prototype, "taskName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务描述',
        nullable: true,
        example: '创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能'
    }),
    __metadata("design:type", String)
], TaskTemplateResponseDto.prototype, "taskDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '任务持续天数',
        example: 7
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "durationDays", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '附件/资源链接',
        nullable: true,
        example: [
            {
                "title": "练习模板",
                "url": "https://example.com/nodejs-template.zip",
                "type": "file"
            },
            {
                "title": "参考文档",
                "url": "https://example.com/nodejs-reference.pdf",
                "type": "document"
            }
        ]
    }),
    __metadata("design:type", Array)
], TaskTemplateResponseDto.prototype, "attachments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '关联作品ID字符串',
        nullable: true,
        example: '201,202,203'
    }),
    __metadata("design:type", String)
], TaskTemplateResponseDto.prototype, "workIdsStr", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '自评项列表',
        nullable: true,
        example: [
            {
                "content": "是否正确使用了fs模块进行文件操作？",
                "sequence": 1
            },
            {
                "content": "HTTP服务器是否能正常启动和响应请求？",
                "sequence": 2
            },
            {
                "content": "代码是否遵循了Node.js的最佳实践？",
                "sequence": 3
            }
        ]
    }),
    __metadata("design:type", Array)
], TaskTemplateResponseDto.prototype, "selfAssessmentItems", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '模板状态：0=禁用，1=启用',
        example: 1
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '附件数量',
        example: 2
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "attachmentsCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '自评项数量',
        example: 3
    }),
    __metadata("design:type", Number)
], TaskTemplateResponseDto.prototype, "assessmentItemsCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '第一个附件类型',
        nullable: true,
        example: 'file'
    }),
    __metadata("design:type", String)
], TaskTemplateResponseDto.prototype, "firstAttachmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '创建时间',
        example: '2023-07-15T10:30:00Z'
    }),
    __metadata("design:type", Date)
], TaskTemplateResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '更新时间',
        example: '2023-07-15T10:30:00Z'
    }),
    __metadata("design:type", Date)
], TaskTemplateResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=task-template.dto.js.map