import { TagInfoDto } from './series-list.dto';
export declare class CourseInfoDto {
    id: number;
    title: string;
    description: string;
    coverImage: string;
    orderIndex: number;
    status: number;
    statusLabel: string;
    hasVideo: number;
    hasDocument: number;
    hasAudio: number;
    videoDuration: number;
    videoDurationLabel: string;
    videoName: string;
    firstTeachingTitle: string;
    resourcesCount: number;
}
export declare class DefaultCourseDto extends CourseInfoDto {
    contentConfig: Record<string, any>;
    teachingInfo: any[];
    additionalResources: any[];
}
export declare class SeriesDetailDataDto {
    id: number;
    title: string;
    description: string;
    coverImage: string;
    category: number;
    categoryLabel: string;
    status: number;
    statusLabel: string;
    projectMembers: string;
    totalCourses: number;
    totalStudents: number;
    creatorId: number;
    createdAt: string;
    updatedAt: string;
    tags: TagInfoDto[];
    courses: CourseInfoDto[];
    defaultCourse: DefaultCourseDto;
    currentCourseId: number;
}
export declare class SeriesDetailResponseDto {
    code: number;
    message: string;
    data: SeriesDetailDataDto;
}
