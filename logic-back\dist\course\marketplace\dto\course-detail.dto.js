"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseDetailResponseDto = exports.CourseDetailDataDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CourseDetailDataDto {
    id;
    title;
    description;
    coverImage;
    hasVideo;
    hasDocument;
    hasAudio;
    videoDuration;
    videoDurationLabel;
    videoName;
    resourcesCount;
    contentConfig;
    teachingInfo;
    additionalResources;
    orderIndex;
    status;
    statusLabel;
    currentCourseId;
}
exports.CourseDetailDataDto = CourseDetailDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程ID', example: 2 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程标题', example: '第二课：组件与Props' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程描述', example: '学习React组件的创建和属性传递' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程封面', example: 'https://example.com/course2-cover.jpg' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "coverImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含视频：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "hasVideo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含文档：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "hasDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否包含音频：0=否，1=是', example: 1 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "hasAudio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长(秒)', example: 2400 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "videoDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频时长标签', example: '40分钟' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "videoDurationLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '视频文件名', example: 'React组件与Props.mp4' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "videoName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '资源数量', example: 3 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "resourcesCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '内容配置(完整JSON)',
        example: {
            hasVideo: 1,
            hasDocument: 1,
            hasAudio: 1,
            video: {
                url: 'https://example.com/videos/react-components.mp4',
                name: 'React组件与Props.mp4'
            },
            document: {
                url: 'https://example.com/documents/react-components-slides.pdf',
                name: 'React组件与Props课件.pdf'
            },
            audio: {
                url: 'https://example.com/audio/react-components.mp3',
                name: 'React组件与Props音频.mp3'
            }
        }
    }),
    __metadata("design:type", Object)
], CourseDetailDataDto.prototype, "contentConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '教学信息(完整JSON)',
        example: [
            {
                title: '教学目标',
                content: [
                    '理解React组件的概念和作用',
                    '掌握函数组件和类组件的创建方法',
                    '学会使用Props进行组件间数据传递',
                    '了解组件的组合和复用'
                ]
            },
            {
                title: '教学重难点',
                content: [
                    '组件的概念理解',
                    'Props的传递和接收',
                    '组件的嵌套和组合',
                    '组件的复用性设计'
                ]
            },
            {
                title: '课程安排',
                content: [
                    '第1部分：组件概念介绍（10分钟）',
                    '第2部分：函数组件创建（15分钟）',
                    '第3部分：Props使用详解（20分钟）',
                    '第4部分：组件组合实践（15分钟）'
                ]
            }
        ]
    }),
    __metadata("design:type", Array)
], CourseDetailDataDto.prototype, "teachingInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '附加资源(完整JSON)',
        example: [
            {
                title: 'React组件文档',
                url: 'https://react.dev/learn/your-first-component',
                description: 'React官方组件学习指南'
            },
            {
                title: 'Props传递练习',
                url: 'https://codesandbox.io/s/react-props-exercise',
                description: '在线练习Props的使用'
            },
            {
                title: '组件设计模式',
                url: 'https://example.com/articles/react-component-patterns',
                description: 'React组件设计的最佳实践'
            }
        ]
    }),
    __metadata("design:type", Array)
], CourseDetailDataDto.prototype, "additionalResources", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序索引', example: 2 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "orderIndex", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：0=草稿，1=已发布，2=已归档', example: 1 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态标签', example: '已发布' }),
    __metadata("design:type", String)
], CourseDetailDataDto.prototype, "statusLabel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前课程ID', example: 2 }),
    __metadata("design:type", Number)
], CourseDetailDataDto.prototype, "currentCourseId", void 0);
class CourseDetailResponseDto {
    code;
    message;
    data;
}
exports.CourseDetailResponseDto = CourseDetailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应状态码', example: 200 }),
    __metadata("design:type", Number)
], CourseDetailResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '响应消息', example: 'success' }),
    __metadata("design:type", String)
], CourseDetailResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '课程详情数据', type: () => CourseDetailDataDto }),
    __metadata("design:type", CourseDetailDataDto)
], CourseDetailResponseDto.prototype, "data", void 0);
//# sourceMappingURL=course-detail.dto.js.map